-- 创建技术资讯系统相关表

-- 资讯分类表
CREATE TABLE news_category (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    icon VARCHAR(100) COMMENT '分类图标',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_category_name (name)
) COMMENT='资讯分类表';

-- 资讯表
CREATE TABLE news (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(500) NOT NULL COMMENT '资讯标题',
    summary TEXT COMMENT '资讯摘要',
    content LONGTEXT COMMENT '资讯内容',
    cover_image VARCHAR(500) COMMENT '封面图片URL',
    source VARCHAR(200) COMMENT '来源网站',
    source_url VARCHAR(1000) COMMENT '原文链接',
    author VARCHAR(200) COMMENT '作者',
    category_id BIGINT COMMENT '分类ID',
    tags JSON COMMENT '标签列表',
    status VARCHAR(50) NOT NULL DEFAULT 'PUBLISHED' COMMENT '状态：DRAFT, PUBLISHED, ARCHIVED',
    priority INT DEFAULT 0 COMMENT '优先级，数值越大优先级越高',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选',
    is_trending BOOLEAN DEFAULT FALSE COMMENT '是否热门',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    share_count INT DEFAULT 0 COMMENT '分享数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    published_at TIMESTAMP COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_published_at (published_at),
    INDEX idx_is_featured (is_featured),
    INDEX idx_is_trending (is_trending),
    INDEX idx_view_count (view_count),
    INDEX idx_like_count (like_count),
    FOREIGN KEY (category_id) REFERENCES news_category(id) ON DELETE SET NULL
) COMMENT='资讯表';

-- 用户资讯点赞表
CREATE TABLE news_like (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    news_id BIGINT NOT NULL COMMENT '资讯ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
    UNIQUE KEY uk_user_news (user_id, news_id),
    INDEX idx_user_id (user_id),
    INDEX idx_news_id (news_id),
    FOREIGN KEY (news_id) REFERENCES news(id) ON DELETE CASCADE
) COMMENT='用户资讯点赞表';

-- 插入默认分类数据
INSERT INTO news_category (name, description, icon, sort_order) VALUES
('前端开发', '前端技术、框架、工具相关资讯', 'code-outline', 1),
('后端开发', '后端技术、架构、数据库相关资讯', 'server-outline', 2),
('移动开发', '移动应用开发、跨平台技术资讯', 'phone-portrait-outline', 3),
('人工智能', 'AI、机器学习、深度学习相关资讯', 'bulb-outline', 4),
('云计算', '云服务、容器、微服务相关资讯', 'cloud-outline', 5),
('开发工具', '开发工具、IDE、效率工具资讯', 'construct-outline', 6),
('行业动态', '技术行业新闻、公司动态、市场趋势', 'trending-up-outline', 7),
('开源项目', '开源项目介绍、社区动态', 'git-branch-outline', 8);

-- 插入示例资讯数据
INSERT INTO news (title, summary, content, cover_image, source, source_url, author, category_id, tags, is_featured, is_trending, view_count, like_count, share_count, published_at) VALUES
('React 19 正式发布：全新并发特性和性能优化', 
 'React 19 带来了革命性的并发渲染特性，显著提升了大型应用的性能表现。新版本包含自动批处理、Suspense改进等重要更新。',
 '# React 19 正式发布

React 团队今天正式发布了 React 19，这是一个里程碑式的版本更新。

## 主要特性

### 1. 并发渲染优化
- 自动批处理优化
- Suspense 边界改进
- 更好的错误处理

### 2. 性能提升
- 减少 50% 的包体积
- 更快的首屏渲染
- 优化的内存使用

### 3. 开发者体验
- 更好的 TypeScript 支持
- 改进的开发工具
- 简化的 API 设计

这次更新将为 React 生态系统带来显著的性能提升和开发体验改善。',
 'https://example.com/react19-cover.jpg',
 'React官方博客',
 'https://react.dev/blog/2024/react-19-release',
 'React Team',
 1,
 '["React", "前端框架", "性能优化", "并发渲染"]',
 TRUE,
 TRUE,
 1250,
 89,
 34,
 '2025-01-13 10:00:00'),

('Spring Boot 3.2 发布：原生镜像和虚拟线程支持', 
 'Spring Boot 3.2 正式支持 GraalVM 原生镜像编译和 Java 21 虚拟线程，为 Java 应用带来更好的启动性能和资源利用率。',
 '# Spring Boot 3.2 重大更新

Spring Boot 3.2 版本带来了期待已久的原生镜像支持和虚拟线程特性。

## 核心特性

### 1. GraalVM 原生镜像
- 毫秒级启动时间
- 显著降低内存占用
- 更好的云原生支持

### 2. 虚拟线程支持
- 基于 Java 21 Project Loom
- 更高的并发处理能力
- 简化的异步编程模型

### 3. 其他改进
- 更好的可观测性
- 增强的安全特性
- 简化的配置管理

这些特性将帮助开发者构建更高效的微服务应用。',
 'https://example.com/springboot32-cover.jpg',
 'Spring官方博客',
 'https://spring.io/blog/2024/spring-boot-3-2-release',
 'Spring Team',
 2,
 '["Spring Boot", "Java", "微服务", "原生镜像", "虚拟线程"]',
 TRUE,
 FALSE,
 980,
 67,
 28,
 '2025-01-12 14:30:00'),

('Flutter 3.16 发布：Material Design 3 完整支持', 
 'Flutter 3.16 带来了完整的 Material Design 3 支持，新增了多项性能优化和开发工具改进，进一步提升跨平台开发体验。',
 '# Flutter 3.16 重要更新

Flutter 3.16 版本专注于设计系统升级和性能优化。

## 主要更新

### 1. Material Design 3
- 完整的 MD3 组件库
- 动态颜色主题支持
- 新的设计令牌系统

### 2. 性能优化
- 渲染性能提升 30%
- 更快的热重载
- 优化的内存管理

### 3. 开发工具
- 改进的 DevTools
- 更好的调试体验
- 增强的性能分析

这次更新让 Flutter 应用能够更好地适配现代设计规范。',
 'https://example.com/flutter316-cover.jpg',
 'Flutter官方博客',
 'https://flutter.dev/blog/2024/flutter-3-16-release',
 'Flutter Team',
 3,
 '["Flutter", "移动开发", "Material Design", "跨平台"]',
 FALSE,
 TRUE,
 756,
 45,
 19,
 '2025-01-11 16:45:00'),

('OpenAI GPT-4 Turbo 降价 50%：AI 应用开发成本大幅下降', 
 'OpenAI 宣布 GPT-4 Turbo API 价格下调 50%，同时推出新的函数调用功能，为开发者构建 AI 应用提供更经济的解决方案。',
 '# OpenAI GPT-4 Turbo 重大价格调整

OpenAI 今天宣布了 GPT-4 Turbo 的重要更新和价格调整。

## 主要变化

### 1. 价格调整
- 输入 token 价格降低 50%
- 输出 token 价格降低 25%
- 更经济的 AI 应用开发成本

### 2. 新功能
- 改进的函数调用
- 更好的 JSON 模式
- 增强的代码理解能力

### 3. 性能提升
- 更快的响应速度
- 更准确的输出结果
- 更稳定的服务质量

这次调整将大大降低 AI 应用的开发和运营成本。',
 'https://example.com/gpt4turbo-cover.jpg',
 'OpenAI官方博客',
 'https://openai.com/blog/gpt-4-turbo-pricing-update',
 'OpenAI Team',
 4,
 '["OpenAI", "GPT-4", "人工智能", "API", "价格调整"]',
 TRUE,
 TRUE,
 2100,
 156,
 78,
 '2025-01-10 09:15:00'),

('Docker Desktop 4.26 发布：支持 Kubernetes 1.29', 
 'Docker Desktop 4.26 版本新增对 Kubernetes 1.29 的支持，并带来了容器性能优化和开发者体验改进。',
 '# Docker Desktop 4.26 更新详情

Docker Desktop 4.26 为开发者带来了更好的容器化开发体验。

## 核心更新

### 1. Kubernetes 支持
- 支持 Kubernetes 1.29
- 改进的集群管理
- 更好的资源监控

### 2. 性能优化
- 容器启动速度提升 40%
- 减少内存占用
- 优化的文件系统性能

### 3. 开发体验
- 新的 GUI 界面
- 改进的日志查看
- 更好的错误提示

这些改进将帮助开发者更高效地进行容器化开发。',
 'https://example.com/docker426-cover.jpg',
 'Docker官方博客',
 'https://docker.com/blog/docker-desktop-4-26-release',
 'Docker Team',
 5,
 '["Docker", "容器化", "Kubernetes", "开发工具"]',
 FALSE,
 FALSE,
 634,
 38,
 15,
 '2025-01-09 11:20:00');
