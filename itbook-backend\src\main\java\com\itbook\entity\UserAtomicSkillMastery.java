package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户原子技能掌握度实体
 * 存储用户对原子技能的掌握情况和学习历史
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Entity
@Table(name = "user_atomic_skill_mastery")
public class UserAtomicSkillMastery {

    /**
     * 掌握水平枚举
     */
    public enum MasteryLevel {
        NONE("未掌握"),
        BASIC("基础"),
        INTERMEDIATE("中级"),
        ADVANCED("高级"),
        EXPERT("专家");
        
        private final String description;
        
        MasteryLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 用户（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    @JsonIgnore
    private User user;

    /**
     * 原子技能ID
     */
    @NotNull(message = "原子技能ID不能为空")
    @Column(name = "atomic_skill_id", nullable = false)
    private Long atomicSkillId;

    /**
     * 原子技能（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "atomic_skill_id", insertable = false, updatable = false)
    @JsonIgnore
    private AtomicSkill atomicSkill;

    /**
     * 掌握水平
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "mastery_level", nullable = false)
    private MasteryLevel masteryLevel = MasteryLevel.NONE;

    /**
     * 掌握分数(0-100)
     */
    @DecimalMin(value = "0.00", message = "掌握分数不能小于0")
    @DecimalMax(value = "100.00", message = "掌握分数不能大于100")
    @Column(name = "mastery_score", precision = 5, scale = 2)
    private BigDecimal masteryScore = BigDecimal.ZERO;

    /**
     * 置信度(0-1)
     */
    @DecimalMin(value = "0.00", message = "置信度不能小于0")
    @DecimalMax(value = "1.00", message = "置信度不能大于1")
    @Column(name = "confidence_level", precision = 3, scale = 2)
    private BigDecimal confidenceLevel = BigDecimal.ZERO;

    /**
     * 学习时长（小时）
     */
    @DecimalMin(value = "0.00", message = "学习时长不能为负数")
    @Column(name = "learning_hours", precision = 6, scale = 2)
    private BigDecimal learningHours = BigDecimal.ZERO;

    /**
     * 练习次数
     */
    @Min(value = 0, message = "练习次数不能为负数")
    @Column(name = "practice_count")
    private Integer practiceCount = 0;

    /**
     * 评估次数
     */
    @Min(value = 0, message = "评估次数不能为负数")
    @Column(name = "assessment_count")
    private Integer assessmentCount = 0;

    /**
     * 最后评估分数
     */
    @DecimalMin(value = "0.00", message = "最后评估分数不能小于0")
    @DecimalMax(value = "100.00", message = "最后评估分数不能大于100")
    @Column(name = "last_assessment_score", precision = 5, scale = 2)
    private BigDecimal lastAssessmentScore;

    /**
     * 首次学习时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "first_learned_at")
    private LocalDateTime firstLearnedAt;

    /**
     * 最后练习时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_practiced_at")
    private LocalDateTime lastPracticedAt;

    /**
     * 最后评估时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "last_assessed_at")
    private LocalDateTime lastAssessedAt;

    /**
     * 掌握达成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "mastery_achieved_at")
    private LocalDateTime masteryAchievedAt;

    /**
     * 通过哪个路径学习
     */
    @Column(name = "learned_via_path_id")
    private Long learnedViaPathId;

    /**
     * 学习路径（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "learned_via_path_id", insertable = false, updatable = false)
    @JsonIgnore
    private DynamicLearningPath learnedViaPath;

    /**
     * 学习上下文信息（JSON格式）
     */
    @Column(name = "learning_context", columnDefinition = "JSON")
    private String learningContextJson;

    /**
     * 是否已认证
     */
    @Column(name = "is_certified")
    private Boolean isCertified = false;

    /**
     * 认证日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "certification_date")
    private LocalDateTime certificationDate;

    /**
     * 是否需要复习
     */
    @Column(name = "needs_refresh")
    private Boolean needsRefresh = false;

    /**
     * 遗忘衰减因子(0-1)
     */
    @DecimalMin(value = "0.00", message = "衰减因子不能小于0")
    @DecimalMax(value = "1.00", message = "衰减因子不能大于1")
    @Column(name = "decay_factor", precision = 3, scale = 2)
    private BigDecimal decayFactor = BigDecimal.ONE;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 构造函数
    public UserAtomicSkillMastery() {}

    public UserAtomicSkillMastery(Long userId, Long atomicSkillId) {
        this.userId = userId;
        this.atomicSkillId = atomicSkillId;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }

    public Long getAtomicSkillId() { return atomicSkillId; }
    public void setAtomicSkillId(Long atomicSkillId) { this.atomicSkillId = atomicSkillId; }

    public AtomicSkill getAtomicSkill() { return atomicSkill; }
    public void setAtomicSkill(AtomicSkill atomicSkill) { this.atomicSkill = atomicSkill; }

    public MasteryLevel getMasteryLevel() { return masteryLevel; }
    public void setMasteryLevel(MasteryLevel masteryLevel) { this.masteryLevel = masteryLevel; }

    public BigDecimal getMasteryScore() { return masteryScore; }
    public void setMasteryScore(BigDecimal masteryScore) { this.masteryScore = masteryScore; }

    public BigDecimal getConfidenceLevel() { return confidenceLevel; }
    public void setConfidenceLevel(BigDecimal confidenceLevel) { this.confidenceLevel = confidenceLevel; }

    public BigDecimal getLearningHours() { return learningHours; }
    public void setLearningHours(BigDecimal learningHours) { this.learningHours = learningHours; }

    public Integer getPracticeCount() { return practiceCount; }
    public void setPracticeCount(Integer practiceCount) { this.practiceCount = practiceCount; }

    public Integer getAssessmentCount() { return assessmentCount; }
    public void setAssessmentCount(Integer assessmentCount) { this.assessmentCount = assessmentCount; }

    public BigDecimal getLastAssessmentScore() { return lastAssessmentScore; }
    public void setLastAssessmentScore(BigDecimal lastAssessmentScore) { this.lastAssessmentScore = lastAssessmentScore; }

    public LocalDateTime getFirstLearnedAt() { return firstLearnedAt; }
    public void setFirstLearnedAt(LocalDateTime firstLearnedAt) { this.firstLearnedAt = firstLearnedAt; }

    public LocalDateTime getLastPracticedAt() { return lastPracticedAt; }
    public void setLastPracticedAt(LocalDateTime lastPracticedAt) { this.lastPracticedAt = lastPracticedAt; }

    public LocalDateTime getLastAssessedAt() { return lastAssessedAt; }
    public void setLastAssessedAt(LocalDateTime lastAssessedAt) { this.lastAssessedAt = lastAssessedAt; }

    public LocalDateTime getMasteryAchievedAt() { return masteryAchievedAt; }
    public void setMasteryAchievedAt(LocalDateTime masteryAchievedAt) { this.masteryAchievedAt = masteryAchievedAt; }

    public Long getLearnedViaPathId() { return learnedViaPathId; }
    public void setLearnedViaPathId(Long learnedViaPathId) { this.learnedViaPathId = learnedViaPathId; }

    public DynamicLearningPath getLearnedViaPath() { return learnedViaPath; }
    public void setLearnedViaPath(DynamicLearningPath learnedViaPath) { this.learnedViaPath = learnedViaPath; }

    public String getLearningContextJson() { return learningContextJson; }
    public void setLearningContextJson(String learningContextJson) { this.learningContextJson = learningContextJson; }

    public Boolean getIsCertified() { return isCertified; }
    public void setIsCertified(Boolean isCertified) { this.isCertified = isCertified; }

    public LocalDateTime getCertificationDate() { return certificationDate; }
    public void setCertificationDate(LocalDateTime certificationDate) { this.certificationDate = certificationDate; }

    public Boolean getNeedsRefresh() { return needsRefresh; }
    public void setNeedsRefresh(Boolean needsRefresh) { this.needsRefresh = needsRefresh; }

    public BigDecimal getDecayFactor() { return decayFactor; }
    public void setDecayFactor(BigDecimal decayFactor) { this.decayFactor = decayFactor; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    @Override
    public String toString() {
        return "UserAtomicSkillMastery{" +
                "id=" + id +
                ", userId=" + userId +
                ", atomicSkillId=" + atomicSkillId +
                ", masteryLevel=" + masteryLevel +
                ", masteryScore=" + masteryScore +
                ", confidenceLevel=" + confidenceLevel +
                '}';
    }
}
