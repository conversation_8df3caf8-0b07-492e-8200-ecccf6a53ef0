package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态路径生成算法服务
 * 
 * 核心功能：
 * 1. 基于用户画像生成个性化学习路径
 * 2. 整合原子技能、知识图谱、用户行为分析
 * 3. 支持多种路径生成策略
 * 4. 实现路径质量评估和优化
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DynamicPathGenerationService {

    private final DynamicLearningPathRepository dynamicLearningPathRepository;
    private final DynamicPathStepRepository dynamicPathStepRepository;
    private final AtomicSkillRepository atomicSkillRepository;
    private final SkillRelationshipRepository skillRelationshipRepository;
    private final UserRepository userRepository;
    private final CareerGoalRepository careerGoalRepository;
    private final CourseRepository courseRepository;
    private final LearningPathRepository learningPathRepository;
    private final UserBehaviorAnalysisService userBehaviorAnalysisService;
    private final SkillRelationshipService skillRelationshipService;

    /**
     * 路径生成策略枚举
     */
    public enum GenerationStrategy {
        SKILL_BASED,        // 基于技能的路径生成
        CAREER_ORIENTED,    // 面向职业的路径生成
        ADAPTIVE,           // 自适应路径生成
        HYBRID              // 混合策略
    }

    /**
     * 路径生成参数
     */
    public static class PathGenerationParams {
        private Long userId;
        private Long careerGoalId;
        private GenerationStrategy strategy = GenerationStrategy.HYBRID;
        private Integer timeConstraintWeeks;
        private String difficultyPreference = "GRADUAL";
        private String learningStyle = "MIXED";
        private Map<String, Object> customParams = new HashMap<>();

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getCareerGoalId() { return careerGoalId; }
        public void setCareerGoalId(Long careerGoalId) { this.careerGoalId = careerGoalId; }
        
        public GenerationStrategy getStrategy() { return strategy; }
        public void setStrategy(GenerationStrategy strategy) { this.strategy = strategy; }
        
        public Integer getTimeConstraintWeeks() { return timeConstraintWeeks; }
        public void setTimeConstraintWeeks(Integer timeConstraintWeeks) { this.timeConstraintWeeks = timeConstraintWeeks; }
        
        public String getDifficultyPreference() { return difficultyPreference; }
        public void setDifficultyPreference(String difficultyPreference) { this.difficultyPreference = difficultyPreference; }
        
        public String getLearningStyle() { return learningStyle; }
        public void setLearningStyle(String learningStyle) { this.learningStyle = learningStyle; }
        
        public Map<String, Object> getCustomParams() { return customParams; }
        public void setCustomParams(Map<String, Object> customParams) { this.customParams = customParams; }
    }

    /**
     * 路径生成结果
     */
    public static class PathGenerationResult {
        private DynamicLearningPath generatedPath;
        private List<DynamicPathStep> pathSteps;
        private BigDecimal qualityScore;
        private String generationReport;
        private Map<String, Object> metadata;

        // Getters and Setters
        public DynamicLearningPath getGeneratedPath() { return generatedPath; }
        public void setGeneratedPath(DynamicLearningPath generatedPath) { this.generatedPath = generatedPath; }
        
        public List<DynamicPathStep> getPathSteps() { return pathSteps; }
        public void setPathSteps(List<DynamicPathStep> pathSteps) { this.pathSteps = pathSteps; }
        
        public BigDecimal getQualityScore() { return qualityScore; }
        public void setQualityScore(BigDecimal qualityScore) { this.qualityScore = qualityScore; }
        
        public String getGenerationReport() { return generationReport; }
        public void setGenerationReport(String generationReport) { this.generationReport = generationReport; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 生成动态学习路径
     * 
     * @param params 路径生成参数
     * @return 路径生成结果
     */
    @Transactional
    public PathGenerationResult generateDynamicPath(PathGenerationParams params) {
        log.info("🚀 开始生成动态学习路径: userId={}, strategy={}", 
                params.getUserId(), params.getStrategy());

        try {
            // 1. 验证输入参数
            validateGenerationParams(params);

            // 2. 获取用户画像数据
            UserProfileData userProfile = getUserProfileData(params.getUserId());

            // 3. 获取职业目标信息
            CareerGoal careerGoal = getCareerGoal(params.getCareerGoalId());

            // 4. 根据策略生成路径
            PathGenerationResult result = generatePathByStrategy(params, userProfile, careerGoal);

            // 5. 评估路径质量
            BigDecimal qualityScore = evaluatePathQuality(result.getGeneratedPath(), result.getPathSteps());
            result.setQualityScore(qualityScore);

            // 6. 保存生成的路径
            DynamicLearningPath savedPath = saveDynamicPath(result.getGeneratedPath(), result.getPathSteps());
            result.setGeneratedPath(savedPath);

            log.info("✅ 动态学习路径生成成功: pathId={}, qualityScore={}", 
                    savedPath.getId(), qualityScore);

            return result;

        } catch (Exception e) {
            log.error("❌ 动态学习路径生成失败: userId={}", params.getUserId(), e);
            throw new RuntimeException("动态学习路径生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证生成参数
     */
    private void validateGenerationParams(PathGenerationParams params) {
        if (params.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        if (!userRepository.existsById(params.getUserId())) {
            throw new IllegalArgumentException("用户不存在: " + params.getUserId());
        }

        if (params.getCareerGoalId() != null && !careerGoalRepository.existsById(params.getCareerGoalId())) {
            throw new IllegalArgumentException("职业目标不存在: " + params.getCareerGoalId());
        }
    }

    /**
     * 获取用户画像数据
     */
    private UserProfileData getUserProfileData(Long userId) {
        log.debug("📊 获取用户画像数据: userId={}", userId);

        try {
            // 获取用户学习行为分析
            Object behaviorAnalysis = userBehaviorAnalysisService.analyzeLearningBehavior(userId);

            // 获取用户技能分析
            Map<String, Object> skillAnalysis = userBehaviorAnalysisService.getUserSkillAnalysis(userId);

            // 获取用户偏好技能分类
            Map<String, Object> skillCategories = userBehaviorAnalysisService.getUserPreferredSkillCategories(userId);

            // 获取用户学习洞察
            Map<String, Object> learningInsights = userBehaviorAnalysisService.getUserLearningInsights(userId);

            UserProfileData profileData = new UserProfileData();
            profileData.setUserId(userId);
            profileData.setBehaviorAnalysis(behaviorAnalysis);
            profileData.setSkillAnalysis(skillAnalysis);
            profileData.setSkillCategories(skillCategories);
            profileData.setLearningInsights(learningInsights);

            return profileData;

        } catch (Exception e) {
            log.error("获取用户画像数据失败: userId={}", userId, e);
            throw new RuntimeException("获取用户画像数据失败", e);
        }
    }

    /**
     * 获取职业目标信息
     */
    private CareerGoal getCareerGoal(Long careerGoalId) {
        if (careerGoalId == null) {
            return null;
        }
        
        return careerGoalRepository.findById(careerGoalId)
                .orElseThrow(() -> new IllegalArgumentException("职业目标不存在: " + careerGoalId));
    }

    /**
     * 用户画像数据类
     */
    public static class UserProfileData {
        private Long userId;
        private Object behaviorAnalysis;
        private Map<String, Object> skillAnalysis;
        private Map<String, Object> skillCategories;
        private Map<String, Object> learningInsights;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Object getBehaviorAnalysis() { return behaviorAnalysis; }
        public void setBehaviorAnalysis(Object behaviorAnalysis) { this.behaviorAnalysis = behaviorAnalysis; }
        
        public Map<String, Object> getSkillAnalysis() { return skillAnalysis; }
        public void setSkillAnalysis(Map<String, Object> skillAnalysis) { this.skillAnalysis = skillAnalysis; }
        
        public Map<String, Object> getSkillCategories() { return skillCategories; }
        public void setSkillCategories(Map<String, Object> skillCategories) { this.skillCategories = skillCategories; }
        
        public Map<String, Object> getLearningInsights() { return learningInsights; }
        public void setLearningInsights(Map<String, Object> learningInsights) { this.learningInsights = learningInsights; }
    }

    /**
     * 根据策略生成路径
     */
    private PathGenerationResult generatePathByStrategy(PathGenerationParams params,
                                                       UserProfileData userProfile,
                                                       CareerGoal careerGoal) {
        log.debug("🎯 根据策略生成路径: strategy={}", params.getStrategy());

        switch (params.getStrategy()) {
            case SKILL_BASED:
                return generateSkillBasedPath(params, userProfile, careerGoal);
            case CAREER_ORIENTED:
                return generateCareerOrientedPath(params, userProfile, careerGoal);
            case ADAPTIVE:
                return generateAdaptivePath(params, userProfile, careerGoal);
            case HYBRID:
            default:
                return generateHybridPath(params, userProfile, careerGoal);
        }
    }

    /**
     * 生成基于技能的路径
     */
    private PathGenerationResult generateSkillBasedPath(PathGenerationParams params,
                                                       UserProfileData userProfile,
                                                       CareerGoal careerGoal) {
        log.debug("🔧 生成基于技能的路径");

        PathGenerationResult result = new PathGenerationResult();

        // 1. 分析用户当前技能状态
        Map<String, Object> skillAnalysis = userProfile.getSkillAnalysis();
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> masteredSkills = (List<Map<String, Object>>) skillAnalysis.get("masteredSkills");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> learningSkills = (List<Map<String, Object>>) skillAnalysis.get("learningSkills");

        // 2. 确定目标技能集合
        Set<String> targetSkills = determineTargetSkills(careerGoal, userProfile);

        // 3. 构建技能依赖图
        Map<String, List<String>> skillDependencies = buildSkillDependencyGraph(targetSkills);

        // 4. 生成学习路径
        DynamicLearningPath path = createDynamicPath(params, "基于技能的个性化路径",
                "根据您的技能水平和目标技能定制的学习路径");

        List<DynamicPathStep> steps = generateSkillBasedSteps(targetSkills, skillDependencies,
                masteredSkills, learningSkills, params);

        result.setGeneratedPath(path);
        result.setPathSteps(steps);
        result.setGenerationReport("基于技能分析生成路径，包含" + steps.size() + "个学习步骤");

        return result;
    }

    /**
     * 生成面向职业的路径
     */
    private PathGenerationResult generateCareerOrientedPath(PathGenerationParams params,
                                                           UserProfileData userProfile,
                                                           CareerGoal careerGoal) {
        log.debug("💼 生成面向职业的路径");

        PathGenerationResult result = new PathGenerationResult();

        // 1. 分析职业技能要求
        List<String> requiredSkills = analyzeCareerSkillRequirements(careerGoal);

        // 2. 评估用户技能差距
        Map<String, Double> skillGaps = assessSkillGaps(requiredSkills, userProfile);

        // 3. 优先级排序
        List<String> prioritizedSkills = prioritizeSkillsByCareerRelevance(skillGaps, careerGoal);

        // 4. 生成职业导向路径
        DynamicLearningPath path = createDynamicPath(params, "职业导向学习路径",
                "面向" + (careerGoal != null ? careerGoal.getName() : "目标职业") + "的专业学习路径");

        List<DynamicPathStep> steps = generateCareerOrientedSteps(prioritizedSkills, userProfile, params);

        result.setGeneratedPath(path);
        result.setPathSteps(steps);
        result.setGenerationReport("面向职业生成路径，重点关注" + prioritizedSkills.size() + "个核心技能");

        return result;
    }

    /**
     * 生成自适应路径
     */
    private PathGenerationResult generateAdaptivePath(PathGenerationParams params,
                                                     UserProfileData userProfile,
                                                     CareerGoal careerGoal) {
        log.debug("🔄 生成自适应路径");

        PathGenerationResult result = new PathGenerationResult();

        // 1. 分析学习模式和偏好
        Map<String, Object> learningInsights = userProfile.getLearningInsights();
        String learningPattern = extractLearningPattern(learningInsights);

        // 2. 动态调整难度和节奏
        String adaptiveDifficulty = calculateAdaptiveDifficulty(userProfile);
        Integer adaptivePacing = calculateAdaptivePacing(userProfile, params.getTimeConstraintWeeks());

        // 3. 生成自适应路径
        DynamicLearningPath path = createDynamicPath(params, "自适应学习路径",
                "根据您的学习习惯和进度动态调整的智能路径");

        List<DynamicPathStep> steps = generateAdaptiveSteps(userProfile, careerGoal,
                adaptiveDifficulty, adaptivePacing, params);

        result.setGeneratedPath(path);
        result.setPathSteps(steps);
        result.setGenerationReport("自适应路径生成，学习模式：" + learningPattern +
                "，难度：" + adaptiveDifficulty + "，节奏：" + adaptivePacing + "周");

        return result;
    }

    /**
     * 生成混合策略路径
     */
    private PathGenerationResult generateHybridPath(PathGenerationParams params,
                                                   UserProfileData userProfile,
                                                   CareerGoal careerGoal) {
        log.debug("🔀 生成混合策略路径");

        // 1. 生成多种策略的路径
        PathGenerationResult skillBasedResult = generateSkillBasedPath(params, userProfile, careerGoal);
        PathGenerationResult careerOrientedResult = generateCareerOrientedPath(params, userProfile, careerGoal);
        PathGenerationResult adaptiveResult = generateAdaptivePath(params, userProfile, careerGoal);

        // 2. 融合多种策略的优势
        PathGenerationResult hybridResult = new PathGenerationResult();

        DynamicLearningPath hybridPath = createDynamicPath(params, "智能混合学习路径",
                "融合技能导向、职业导向和自适应策略的综合学习路径");

        // 3. 智能合并步骤
        List<DynamicPathStep> hybridSteps = mergePathSteps(
                skillBasedResult.getPathSteps(),
                careerOrientedResult.getPathSteps(),
                adaptiveResult.getPathSteps(),
                userProfile
        );

        hybridResult.setGeneratedPath(hybridPath);
        hybridResult.setPathSteps(hybridSteps);
        hybridResult.setGenerationReport("混合策略路径，融合了技能导向、职业导向和自适应的优势，共" +
                hybridSteps.size() + "个优化步骤");

        return hybridResult;
    }

    /**
     * 确定目标技能集合
     */
    private Set<String> determineTargetSkills(CareerGoal careerGoal, UserProfileData userProfile) {
        Set<String> targetSkills = new HashSet<>();

        // 1. 从职业目标获取技能要求
        if (careerGoal != null) {
            // 解析职业目标的技能要求
            List<String> careerSkills = analyzeCareerSkillRequirements(careerGoal);
            targetSkills.addAll(careerSkills);
        }

        // 2. 从用户偏好技能分类获取
        Map<String, Object> skillCategories = userProfile.getSkillCategories();
        if (skillCategories != null) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> preferredCategories =
                    (List<Map<String, Object>>) skillCategories.get("preferredCategories");

            if (preferredCategories != null) {
                for (Map<String, Object> category : preferredCategories) {
                    String categoryName = (String) category.get("category");
                    // 根据分类获取相关技能
                    List<AtomicSkill> categorySkills = atomicSkillRepository.findByCategory(categoryName);
                    targetSkills.addAll(categorySkills.stream()
                            .map(AtomicSkill::getName)
                            .collect(Collectors.toList()));
                }
            }
        }

        // 3. 如果没有明确目标，使用所有技能的前10个
        if (targetSkills.isEmpty()) {
            List<AtomicSkill> allSkills = atomicSkillRepository.findAll();
            targetSkills.addAll(allSkills.stream()
                    .limit(10)
                    .map(AtomicSkill::getName)
                    .collect(Collectors.toList()));
        }

        log.debug("确定目标技能集合: {}", targetSkills);
        return targetSkills;
    }

    /**
     * 构建技能依赖图
     */
    private Map<String, List<String>> buildSkillDependencyGraph(Set<String> targetSkills) {
        Map<String, List<String>> dependencyGraph = new HashMap<>();

        for (String skillName : targetSkills) {
            List<AtomicSkill> skills = atomicSkillRepository.findByNameContainingIgnoreCase(skillName);
            if (!skills.isEmpty()) {
                AtomicSkill skill = skills.get(0);

                // 获取前置技能 - 简化版本，暂时不使用关系类型
                List<SkillRelationship> prerequisites = skillRelationshipRepository
                        .findByTargetSkillId(skill.getId());

                List<String> prereqNames = prerequisites.stream()
                        .map(rel -> rel.getSourceSkill().getName())
                        .collect(Collectors.toList());

                dependencyGraph.put(skillName, prereqNames);
            }
        }

        return dependencyGraph;
    }

    /**
     * 创建动态路径实体
     */
    private DynamicLearningPath createDynamicPath(PathGenerationParams params, String name, String description) {
        DynamicLearningPath path = new DynamicLearningPath();
        path.setUserId(params.getUserId());
        path.setCareerGoalId(params.getCareerGoalId());
        path.setName(name);
        path.setDescription(description);
        path.setPathType(DynamicLearningPath.PathType.CAREER_ORIENTED);
        path.setLearningStyle(DynamicLearningPath.LearningStyle.valueOf(params.getLearningStyle()));
        path.setDifficultyPreference(DynamicLearningPath.DifficultyPreference.valueOf(params.getDifficultyPreference()));
        path.setTimeConstraint(params.getTimeConstraintWeeks());
        path.setGenerationAlgorithm("HYBRID_V1.0");
        path.setStatus(DynamicLearningPath.Status.ACTIVE);
        path.setIsTemplate(false);

        return path;
    }

    /**
     * 生成基于技能的步骤
     */
    private List<DynamicPathStep> generateSkillBasedSteps(Set<String> targetSkills,
                                                         Map<String, List<String>> skillDependencies,
                                                         List<Map<String, Object>> masteredSkills,
                                                         List<Map<String, Object>> learningSkills,
                                                         PathGenerationParams params) {
        List<DynamicPathStep> steps = new ArrayList<>();
        int stepOrder = 1;

        // 获取已掌握技能名称集合
        Set<String> masteredSkillNames = masteredSkills.stream()
                .map(skill -> (String) skill.get("name"))
                .collect(Collectors.toSet());

        // 拓扑排序确定学习顺序
        List<String> orderedSkills = topologicalSort(targetSkills, skillDependencies, masteredSkillNames);

        for (String skillName : orderedSkills) {
            if (!masteredSkillNames.contains(skillName)) {
                // 为每个技能创建学习步骤
                List<DynamicPathStep> skillSteps = createSkillLearningSteps(skillName, stepOrder, params);
                steps.addAll(skillSteps);
                stepOrder += skillSteps.size();
            }
        }

        return steps;
    }

    /**
     * 拓扑排序确定技能学习顺序
     */
    private List<String> topologicalSort(Set<String> skills, Map<String, List<String>> dependencies, Set<String> masteredSkills) {
        List<String> result = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        Set<String> visiting = new HashSet<>();

        for (String skill : skills) {
            if (!visited.contains(skill) && !masteredSkills.contains(skill)) {
                topologicalSortDFS(skill, dependencies, visited, visiting, result, masteredSkills);
            }
        }

        return result;
    }

    /**
     * 拓扑排序DFS辅助方法
     */
    private void topologicalSortDFS(String skill, Map<String, List<String>> dependencies,
                                   Set<String> visited, Set<String> visiting, List<String> result,
                                   Set<String> masteredSkills) {
        if (visiting.contains(skill)) {
            // 检测到循环依赖，跳过
            return;
        }

        if (visited.contains(skill)) {
            return;
        }

        visiting.add(skill);

        List<String> prereqs = dependencies.getOrDefault(skill, new ArrayList<>());
        for (String prereq : prereqs) {
            if (!masteredSkills.contains(prereq)) {
                topologicalSortDFS(prereq, dependencies, visited, visiting, result, masteredSkills);
            }
        }

        visiting.remove(skill);
        visited.add(skill);
        result.add(skill);
    }

    /**
     * 分析职业技能要求
     */
    private List<String> analyzeCareerSkillRequirements(CareerGoal careerGoal) {
        List<String> requiredSkills = new ArrayList<>();

        if (careerGoal == null) {
            return requiredSkills;
        }

        // 从职业目标的技能标签中提取技能 - 暂时跳过，因为CareerGoal没有skillTags字段
        // 可以从职业目标的描述或其他字段中提取技能信息

        // 从职业目标描述中提取技能关键词
        String description = careerGoal.getDescription();
        if (description != null) {
            List<String> extractedSkills = extractSkillsFromText(description);
            requiredSkills.addAll(extractedSkills);
        }

        return requiredSkills.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 从文本中提取技能关键词
     */
    private List<String> extractSkillsFromText(String text) {
        List<String> skills = new ArrayList<>();

        // 获取所有原子技能名称作为关键词库
        List<AtomicSkill> allSkills = atomicSkillRepository.findAll();

        for (AtomicSkill skill : allSkills) {
            if (text.toLowerCase().contains(skill.getName().toLowerCase())) {
                skills.add(skill.getName());
            }
        }

        return skills;
    }

    /**
     * 评估技能差距
     */
    private Map<String, Double> assessSkillGaps(List<String> requiredSkills, UserProfileData userProfile) {
        Map<String, Double> skillGaps = new HashMap<>();

        // 获取用户当前技能水平
        Map<String, Object> skillAnalysis = userProfile.getSkillAnalysis();
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> masteredSkills = (List<Map<String, Object>>) skillAnalysis.get("masteredSkills");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> learningSkills = (List<Map<String, Object>>) skillAnalysis.get("learningSkills");

        // 构建用户技能水平映射
        Map<String, Double> userSkillLevels = new HashMap<>();

        if (masteredSkills != null) {
            for (Map<String, Object> skill : masteredSkills) {
                String name = (String) skill.get("name");
                Double proficiency = (Double) skill.get("proficiency");
                userSkillLevels.put(name, proficiency);
            }
        }

        if (learningSkills != null) {
            for (Map<String, Object> skill : learningSkills) {
                String name = (String) skill.get("name");
                Double proficiency = (Double) skill.get("proficiency");
                userSkillLevels.put(name, proficiency);
            }
        }

        // 计算技能差距
        for (String requiredSkill : requiredSkills) {
            double currentLevel = userSkillLevels.getOrDefault(requiredSkill, 0.0);
            double targetLevel = 80.0; // 目标熟练度80%
            double gap = Math.max(0, targetLevel - currentLevel);
            skillGaps.put(requiredSkill, gap);
        }

        return skillGaps;
    }

    /**
     * 按职业相关性优先级排序技能
     */
    private List<String> prioritizeSkillsByCareerRelevance(Map<String, Double> skillGaps, CareerGoal careerGoal) {
        return skillGaps.entrySet().stream()
                .sorted((e1, e2) -> {
                    // 按技能差距降序排序（差距越大，优先级越高）
                    int gapComparison = Double.compare(e2.getValue(), e1.getValue());
                    if (gapComparison != 0) {
                        return gapComparison;
                    }
                    // 如果差距相同，按技能名称排序
                    return e1.getKey().compareTo(e2.getKey());
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 生成面向职业的步骤
     */
    private List<DynamicPathStep> generateCareerOrientedSteps(List<String> prioritizedSkills,
                                                             UserProfileData userProfile,
                                                             PathGenerationParams params) {
        List<DynamicPathStep> steps = new ArrayList<>();
        int stepOrder = 1;

        for (String skillName : prioritizedSkills) {
            List<DynamicPathStep> skillSteps = createSkillLearningSteps(skillName, stepOrder, params);
            steps.addAll(skillSteps);
            stepOrder += skillSteps.size();
        }

        return steps;
    }

    /**
     * 提取学习模式
     */
    private String extractLearningPattern(Map<String, Object> learningInsights) {
        if (learningInsights == null) {
            return "BALANCED";
        }

        // 从学习洞察中提取学习模式信息
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> suggestions = (List<Map<String, Object>>) learningInsights.get("improvementSuggestions");

        if (suggestions != null) {
            for (Map<String, Object> suggestion : suggestions) {
                String title = (String) suggestion.get("title");
                if ("平衡学习节奏".equals(title)) {
                    return "BURST"; // 爆发型学习
                }
            }
        }

        return "BALANCED"; // 默认平衡型
    }

    /**
     * 计算自适应难度
     */
    private String calculateAdaptiveDifficulty(UserProfileData userProfile) {
        Map<String, Object> skillAnalysis = userProfile.getSkillAnalysis();
        Double averageProficiency = (Double) skillAnalysis.get("averageProficiency");

        if (averageProficiency == null) {
            return "BEGINNER";
        }

        if (averageProficiency >= 80.0) {
            return "ADVANCED";
        } else if (averageProficiency >= 60.0) {
            return "INTERMEDIATE";
        } else {
            return "BEGINNER";
        }
    }

    /**
     * 计算自适应节奏
     */
    private Integer calculateAdaptivePacing(UserProfileData userProfile, Integer timeConstraintWeeks) {
        if (timeConstraintWeeks != null) {
            return timeConstraintWeeks;
        }

        // 根据用户学习活跃度计算建议节奏
        // 这里可以根据学习行为分析结果计算合适的学习节奏
        // 暂时返回默认值
        return 12; // 默认12周
    }

    /**
     * 生成自适应步骤
     */
    private List<DynamicPathStep> generateAdaptiveSteps(UserProfileData userProfile,
                                                       CareerGoal careerGoal,
                                                       String adaptiveDifficulty,
                                                       Integer adaptivePacing,
                                                       PathGenerationParams params) {
        // 根据自适应参数生成步骤
        // 这里可以实现更复杂的自适应逻辑

        // 暂时使用基础的技能导向方法
        Set<String> targetSkills = determineTargetSkills(careerGoal, userProfile);
        Map<String, List<String>> skillDependencies = buildSkillDependencyGraph(targetSkills);

        Map<String, Object> skillAnalysis = userProfile.getSkillAnalysis();
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> masteredSkills = (List<Map<String, Object>>) skillAnalysis.get("masteredSkills");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> learningSkills = (List<Map<String, Object>>) skillAnalysis.get("learningSkills");

        return generateSkillBasedSteps(targetSkills, skillDependencies, masteredSkills, learningSkills, params);
    }

    /**
     * 合并多种策略的路径步骤
     */
    private List<DynamicPathStep> mergePathSteps(List<DynamicPathStep> skillBasedSteps,
                                                List<DynamicPathStep> careerOrientedSteps,
                                                List<DynamicPathStep> adaptiveSteps,
                                                UserProfileData userProfile) {
        // 简单的合并策略：优先选择技能导向的步骤，补充职业导向的步骤
        List<DynamicPathStep> mergedSteps = new ArrayList<>();

        // 去重并合并
        Set<String> addedSkills = new HashSet<>();

        // 首先添加技能导向的步骤
        for (DynamicPathStep step : skillBasedSteps) {
            String skillKey = step.getAtomicSkillId().toString();
            if (!addedSkills.contains(skillKey)) {
                mergedSteps.add(step);
                addedSkills.add(skillKey);
            }
        }

        // 补充职业导向的步骤
        for (DynamicPathStep step : careerOrientedSteps) {
            String skillKey = step.getAtomicSkillId().toString();
            if (!addedSkills.contains(skillKey)) {
                mergedSteps.add(step);
                addedSkills.add(skillKey);
            }
        }

        // 重新排序步骤
        for (int i = 0; i < mergedSteps.size(); i++) {
            mergedSteps.get(i).setStepOrder(i + 1);
        }

        return mergedSteps;
    }

    /**
     * 创建技能学习步骤
     */
    private List<DynamicPathStep> createSkillLearningSteps(String skillName, int startOrder, PathGenerationParams params) {
        List<DynamicPathStep> steps = new ArrayList<>();

        // 查找对应的原子技能
        List<AtomicSkill> atomicSkills = atomicSkillRepository.findByNameContainingIgnoreCase(skillName);
        if (atomicSkills.isEmpty()) {
            log.warn("未找到原子技能: {}", skillName);
            return steps;
        }

        AtomicSkill atomicSkill = atomicSkills.get(0);

        // 创建学习步骤
        DynamicPathStep step = new DynamicPathStep();
        step.setAtomicSkillId(atomicSkill.getId());
        step.setStepOrder(startOrder);
        step.setStepType(DynamicPathStep.StepType.LEARN);
        step.setEstimatedHours(2); // 默认2小时
        step.setDifficultyAdjustment(BigDecimal.ONE);
        step.setPriorityWeight(BigDecimal.ONE);
        step.setPersonalizationReason("基于用户画像生成的个性化学习步骤");
        step.setStatus(DynamicPathStep.Status.NOT_STARTED);
        step.setProgressPercentage(BigDecimal.ZERO);

        steps.add(step);

        return steps;
    }



    /**
     * 评估路径质量
     */
    private BigDecimal evaluatePathQuality(DynamicLearningPath path, List<DynamicPathStep> steps) {
        double qualityScore = 0.0;

        // 1. 步骤数量合理性 (20%)
        int stepCount = steps.size();
        double stepScore = Math.min(1.0, Math.max(0.0, (20.0 - Math.abs(stepCount - 10)) / 20.0));
        qualityScore += stepScore * 0.2;

        // 2. 技能覆盖度 (30%)
        double coverageScore = calculateSkillCoverage(steps);
        qualityScore += coverageScore * 0.3;

        // 3. 难度递进性 (25%)
        double progressionScore = calculateDifficultyProgression(steps);
        qualityScore += progressionScore * 0.25;

        // 4. 时间估算合理性 (25%)
        double timeScore = calculateTimeReasonableness(steps, path.getTimeConstraint());
        qualityScore += timeScore * 0.25;

        return BigDecimal.valueOf(Math.max(0.0, Math.min(1.0, qualityScore)));
    }

    /**
     * 计算技能覆盖度
     */
    private double calculateSkillCoverage(List<DynamicPathStep> steps) {
        Set<Long> coveredSkills = new HashSet<>();
        for (DynamicPathStep step : steps) {
            coveredSkills.add(step.getAtomicSkillId());
        }

        // 假设理想的技能覆盖数量是5-10个
        int skillCount = coveredSkills.size();
        return Math.min(1.0, skillCount / 8.0);
    }

    /**
     * 计算难度递进性
     */
    private double calculateDifficultyProgression(List<DynamicPathStep> steps) {
        if (steps.size() < 2) {
            return 1.0;
        }

        // 简化版本：基于步骤顺序假设难度递进
        // 实际项目中可以根据原子技能的难度级别来计算
        return 0.8; // 假设80%的递进性
    }

    /**
     * 计算时间合理性
     */
    private double calculateTimeReasonableness(List<DynamicPathStep> steps, Integer timeConstraintWeeks) {
        int totalHours = steps.stream()
                .mapToInt(step -> step.getEstimatedHours() != null ? step.getEstimatedHours() : 2)
                .sum();

        if (timeConstraintWeeks == null) {
            timeConstraintWeeks = 12; // 默认12周
        }

        // 假设每周学习10小时
        int availableHours = timeConstraintWeeks * 10;

        if (totalHours <= availableHours) {
            return 1.0;
        } else {
            return Math.max(0.0, 1.0 - (double)(totalHours - availableHours) / availableHours);
        }
    }

    /**
     * 保存动态路径
     */
    private DynamicLearningPath saveDynamicPath(DynamicLearningPath path, List<DynamicPathStep> steps) {
        try {
            // 保存路径
            DynamicLearningPath savedPath = dynamicLearningPathRepository.save(path);

            // 设置步骤的路径ID并保存
            for (DynamicPathStep step : steps) {
                step.setPathId(savedPath.getId());
            }
            dynamicPathStepRepository.saveAll(steps);

            log.info("✅ 动态路径保存成功: pathId={}, stepCount={}", savedPath.getId(), steps.size());
            return savedPath;

        } catch (Exception e) {
            log.error("❌ 动态路径保存失败", e);
            throw new RuntimeException("动态路径保存失败", e);
        }
    }
}
