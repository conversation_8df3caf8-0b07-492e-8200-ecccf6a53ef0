<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITBook 删除功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #FF3B30;
        }
        button.danger:hover {
            background: #d32f2f;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ITBook 删除功能测试</h1>
        <p>这个页面用于测试简历删除功能的前后端联调。</p>

        <div class="test-section">
            <h3>1. 检查后端API</h3>
            <button onclick="testBackendAPI()">测试后端删除API</button>
            <div id="backend-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 检查简历列表</h3>
            <button onclick="getResumeList()">获取简历列表</button>
            <div id="resume-list-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试删除功能</h3>
            <p>简历ID: <input type="text" id="resumeId" value="23" placeholder="输入简历ID"></p>
            <p>用户ID: <input type="text" id="userId" value="1" placeholder="输入用户ID"></p>
            <button class="danger" onclick="testDeleteFunction()">测试删除简历</button>
            <div id="delete-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 前端应用链接</h3>
            <p>
                <a href="http://localhost:8081" target="_blank">打开ITBook前端应用</a>
            </p>
            <p>
                <a href="http://localhost:8081/jobs/resume/23" target="_blank">直接访问简历详情页面 (ID: 23)</a>
            </p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8888/api';

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testBackendAPI() {
            try {
                showResult('backend-result', '正在测试后端API...', 'info');
                
                const response = await fetch(`${API_BASE}/resumes/user/1`);
                const data = await response.json();
                
                if (data.code === 20000) {
                    showResult('backend-result', 
                        `✅ 后端API正常\n响应: ${JSON.stringify(data, null, 2)}`, 
                        'success'
                    );
                } else {
                    showResult('backend-result', 
                        `❌ 后端API异常\n响应: ${JSON.stringify(data, null, 2)}`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('backend-result', 
                    `❌ 网络错误: ${error.message}`, 
                    'error'
                );
            }
        }

        async function getResumeList() {
            try {
                showResult('resume-list-result', '正在获取简历列表...', 'info');
                
                const response = await fetch(`${API_BASE}/resumes/user/1`);
                const data = await response.json();
                
                if (data.code === 20000) {
                    const resumes = data.data;
                    let message = `✅ 找到 ${resumes.length} 个简历:\n\n`;
                    resumes.forEach(resume => {
                        message += `ID: ${resume.id} - ${resume.title}\n`;
                    });
                    showResult('resume-list-result', message, 'success');
                } else {
                    showResult('resume-list-result', 
                        `❌ 获取失败: ${data.message}`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('resume-list-result', 
                    `❌ 网络错误: ${error.message}`, 
                    'error'
                );
            }
        }

        async function testDeleteFunction() {
            const resumeId = document.getElementById('resumeId').value;
            const userId = document.getElementById('userId').value;
            
            if (!resumeId || !userId) {
                showResult('delete-result', '❌ 请输入简历ID和用户ID', 'error');
                return;
            }

            if (!confirm(`确定要删除简历 ${resumeId} 吗？此操作不可撤销。`)) {
                showResult('delete-result', '🚫 用户取消删除操作', 'info');
                return;
            }

            try {
                showResult('delete-result', '正在删除简历...', 'info');
                
                const response = await fetch(`${API_BASE}/resumes/${resumeId}/user/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 20000) {
                    showResult('delete-result', 
                        `✅ 删除成功!\n响应: ${JSON.stringify(data, null, 2)}`, 
                        'success'
                    );
                    // 自动刷新简历列表
                    setTimeout(getResumeList, 1000);
                } else {
                    showResult('delete-result', 
                        `❌ 删除失败\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('delete-result', 
                    `❌ 网络错误: ${error.message}`, 
                    'error'
                );
            }
        }

        // 页面加载时自动测试后端API
        window.onload = function() {
            testBackendAPI();
            getResumeList();
        };
    </script>
</body>
</html>
