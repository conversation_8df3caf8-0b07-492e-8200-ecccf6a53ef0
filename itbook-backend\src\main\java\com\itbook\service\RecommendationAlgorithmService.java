package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐算法服务类
 * 实现多因子评分模型的核心推荐算法
 * 
 * <AUTHOR> Team
 * @since 2025-07-10
 */
@Service
public class RecommendationAlgorithmService {

    private static final Logger log = LoggerFactory.getLogger(RecommendationAlgorithmService.class);

    @Autowired
    private LearningPathRepository learningPathRepository;

    @Autowired
    private JobSkillRequirementRepository jobSkillRequirementRepository;

    @Autowired
    private UserSkillAssessmentRepository userSkillAssessmentRepository;

    @Autowired
    private PathAdaptationRuleRepository pathAdaptationRuleRepository;

    // 推荐因子权重配置
    private static final BigDecimal SKILL_MATCH_WEIGHT = new BigDecimal("0.40");      // 技能匹配度权重 40%
    private static final BigDecimal TIME_MATCH_WEIGHT = new BigDecimal("0.30");       // 时间匹配度权重 30%
    private static final BigDecimal STYLE_MATCH_WEIGHT = new BigDecimal("0.20");      // 学习风格匹配度权重 20%
    private static final BigDecimal GOAL_MATCH_WEIGHT = new BigDecimal("0.10");       // 就业目标匹配度权重 10%

    /**
     * 多因子评分模型 - 计算学习路径推荐分数
     * 
     * @param userProfile 用户画像
     * @param learningPath 学习路径
     * @param jobSkillRequirements 岗位技能要求
     * @param userSkillAssessments 用户技能评估
     * @return 推荐分数 (0-1)
     */
    public BigDecimal calculateRecommendationScore(UserProfile userProfile, LearningPath learningPath,
                                                  List<JobSkillRequirement> jobSkillRequirements,
                                                  List<UserSkillAssessment> userSkillAssessments) {
        log.debug("计算推荐分数: userId={}, pathId={}", userProfile.getUserId(), learningPath.getId());

        try {
            // 1. 技能匹配度评分 (40%)
            BigDecimal skillMatchScore = calculateSkillMatchScore(userProfile, jobSkillRequirements, userSkillAssessments);
            
            // 2. 时间匹配度评分 (30%)
            BigDecimal timeMatchScore = calculateTimeMatchScore(userProfile, learningPath);
            
            // 3. 学习风格匹配度评分 (20%)
            BigDecimal styleMatchScore = calculateStyleMatchScore(userProfile, learningPath);
            
            // 4. 就业目标匹配度评分 (10%)
            BigDecimal goalMatchScore = calculateGoalMatchScore(userProfile, learningPath);

            // 加权计算总分
            BigDecimal totalScore = skillMatchScore.multiply(SKILL_MATCH_WEIGHT)
                    .add(timeMatchScore.multiply(TIME_MATCH_WEIGHT))
                    .add(styleMatchScore.multiply(STYLE_MATCH_WEIGHT))
                    .add(goalMatchScore.multiply(GOAL_MATCH_WEIGHT));

            // 应用适配规则调整分数
            BigDecimal adjustedScore = applyAdaptationRules(userProfile, learningPath, totalScore);

            log.debug("推荐分数计算完成: userId={}, pathId={}, score={}", 
                    userProfile.getUserId(), learningPath.getId(), adjustedScore);

            return adjustedScore.setScale(3, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.error("计算推荐分数失败: userId={}, pathId={}", userProfile.getUserId(), learningPath.getId(), e);
            return new BigDecimal("0.500"); // 返回默认分数
        }
    }

    /**
     * 计算技能匹配度评分
     * 基于用户当前技能水平与岗位技能要求的匹配程度
     */
    private BigDecimal calculateSkillMatchScore(UserProfile userProfile, List<JobSkillRequirement> jobSkillRequirements,
                                              List<UserSkillAssessment> userSkillAssessments) {
        if (jobSkillRequirements.isEmpty()) {
            // 如果没有明确的技能要求，基于用户技能水平给出基础分数
            return mapSkillLevelToScore(userProfile.getCurrentSkillLevel());
        }

        // 构建用户技能映射
        Map<String, Integer> userSkillMap = userSkillAssessments.stream()
                .collect(Collectors.toMap(
                        UserSkillAssessment::getSkillName,
                        UserSkillAssessment::getCurrentLevel,
                        (existing, replacement) -> Math.max(existing, replacement)
                ));

        BigDecimal totalScore = BigDecimal.ZERO;
        BigDecimal totalWeight = BigDecimal.ZERO;

        for (JobSkillRequirement requirement : jobSkillRequirements) {
            BigDecimal weight = requirement.getWeight() != null ? requirement.getWeight() : BigDecimal.ONE;
            totalWeight = totalWeight.add(weight);

            Integer userLevel = userSkillMap.get(requirement.getSkillName());
            BigDecimal skillScore;

            if (userLevel == null) {
                // 用户没有该技能的评估，根据是否为核心技能给分
                skillScore = requirement.getIsCoreSkill() ? new BigDecimal("0.2") : new BigDecimal("0.4");
            } else {
                // 计算技能水平匹配度
                int requiredLevel = requirement.getRequiredLevel();
                if (userLevel >= requiredLevel) {
                    // 用户技能水平达到或超过要求
                    skillScore = BigDecimal.ONE;
                } else {
                    // 用户技能水平低于要求，按比例计分
                    skillScore = BigDecimal.valueOf(userLevel)
                            .divide(BigDecimal.valueOf(requiredLevel), 3, RoundingMode.HALF_UP);
                }

                // 核心技能权重加成
                if (requirement.getIsCoreSkill()) {
                    skillScore = skillScore.multiply(new BigDecimal("1.2")).min(BigDecimal.ONE);
                }
            }

            totalScore = totalScore.add(skillScore.multiply(weight));
        }

        return totalWeight.compareTo(BigDecimal.ZERO) > 0 ? 
                totalScore.divide(totalWeight, 3, RoundingMode.HALF_UP) : 
                new BigDecimal("0.500");
    }

    /**
     * 计算时间匹配度评分
     * 基于用户可用学习时间与路径预计完成时间的匹配程度
     */
    private BigDecimal calculateTimeMatchScore(UserProfile userProfile, LearningPath learningPath) {
        Integer availableTimePerWeek = userProfile.getAvailableTimePerWeek();
        Integer estimatedHours = learningPath.getEstimatedHours();

        if (availableTimePerWeek == null || estimatedHours == null) {
            return new BigDecimal("0.700"); // 默认分数
        }

        // 计算完成路径需要的周数
        int estimatedWeeks = estimatedHours / availableTimePerWeek;

        // 根据学习节奏偏好调整理想完成时间
        int idealMinWeeks, idealMaxWeeks;
        switch (userProfile.getPreferredLearningPace()) {
            case FAST:
                idealMinWeeks = 4;
                idealMaxWeeks = 12;
                break;
            case SLOW:
                idealMinWeeks = 12;
                idealMaxWeeks = 24;
                break;
            case NORMAL:
            default:
                idealMinWeeks = 8;
                idealMaxWeeks = 16;
                break;
        }

        // 计算时间匹配分数
        if (estimatedWeeks >= idealMinWeeks && estimatedWeeks <= idealMaxWeeks) {
            return BigDecimal.ONE; // 完美匹配
        } else if (estimatedWeeks < idealMinWeeks) {
            // 时间过短，可能过于紧张
            double ratio = (double) estimatedWeeks / idealMinWeeks;
            return BigDecimal.valueOf(0.6 + 0.4 * ratio);
        } else {
            // 时间过长，可能缺乏动力
            double ratio = (double) idealMaxWeeks / estimatedWeeks;
            return BigDecimal.valueOf(0.4 + 0.6 * ratio);
        }
    }

    /**
     * 计算学习风格匹配度评分
     * 基于用户学习风格偏好与路径特征的匹配程度
     */
    private BigDecimal calculateStyleMatchScore(UserProfile userProfile, LearningPath learningPath) {
        UserProfile.LearningStyle userStyle = userProfile.getLearningStyle();
        LearningPath.PathType pathType = learningPath.getPathType();

        // 基础匹配分数
        BigDecimal baseScore = new BigDecimal("0.700");

        // 根据路径类型调整分数
        switch (pathType) {
            case PERSONALIZED:
                // 个性化路径适合所有学习风格
                baseScore = BigDecimal.ONE;
                break;
            case STANDARD:
                // 标准路径适合混合型和理论型学习者
                if (userStyle == UserProfile.LearningStyle.MIXED || 
                    userStyle == UserProfile.LearningStyle.THEORETICAL) {
                    baseScore = new BigDecimal("0.900");
                } else {
                    baseScore = new BigDecimal("0.750");
                }
                break;
            case TEMPLATE:
                // 模板路径适合实践型学习者
                if (userStyle == UserProfile.LearningStyle.PRACTICAL || 
                    userStyle == UserProfile.LearningStyle.PROJECT_DRIVEN) {
                    baseScore = new BigDecimal("0.850");
                } else {
                    baseScore = new BigDecimal("0.700");
                }
                break;
            case CUSTOM:
                // 自定义路径适合项目驱动型学习者
                if (userStyle == UserProfile.LearningStyle.PROJECT_DRIVEN) {
                    baseScore = new BigDecimal("0.950");
                } else {
                    baseScore = new BigDecimal("0.650");
                }
                break;
            case STRUCTURED:
                // 结构化路径适合理论型和混合型学习者
                if (userStyle == UserProfile.LearningStyle.THEORETICAL ||
                    userStyle == UserProfile.LearningStyle.MIXED) {
                    baseScore = new BigDecimal("0.880");
                } else {
                    baseScore = new BigDecimal("0.720");
                }
                break;
        }

        // 根据编程经验调整分数
        if (userProfile.getHasProgrammingExperience()) {
            // 有编程经验的用户更适合高难度路径
            if (learningPath.getDifficultyLevel() == LearningPath.DifficultyLevel.ADVANCED) {
                baseScore = baseScore.multiply(new BigDecimal("1.1")).min(BigDecimal.ONE);
            }
        } else {
            // 无编程经验的用户更适合初级路径
            if (learningPath.getDifficultyLevel() == LearningPath.DifficultyLevel.BEGINNER) {
                baseScore = baseScore.multiply(new BigDecimal("1.1")).min(BigDecimal.ONE);
            }
        }

        return baseScore;
    }

    /**
     * 计算就业目标匹配度评分
     * 基于用户职业目标与学习路径目标的匹配程度
     */
    private BigDecimal calculateGoalMatchScore(UserProfile userProfile, LearningPath learningPath) {
        String careerGoal = userProfile.getCareerGoal();
        String pathName = learningPath.getName();
        String pathDescription = learningPath.getDescription();

        if (careerGoal == null || careerGoal.trim().isEmpty()) {
            return new BigDecimal("0.700"); // 默认分数
        }

        // 转换为小写进行匹配
        String goalLower = careerGoal.toLowerCase();
        String nameLower = pathName != null ? pathName.toLowerCase() : "";
        String descLower = pathDescription != null ? pathDescription.toLowerCase() : "";

        // 关键词匹配
        String[] goalKeywords = goalLower.split("\\s+");
        int matchCount = 0;

        for (String keyword : goalKeywords) {
            if (keyword.length() > 2) { // 忽略过短的词
                if (nameLower.contains(keyword) || descLower.contains(keyword)) {
                    matchCount++;
                }
            }
        }

        // 计算匹配度
        if (goalKeywords.length == 0) {
            return new BigDecimal("0.700");
        }

        double matchRatio = (double) matchCount / goalKeywords.length;
        if (matchRatio >= 0.8) {
            return BigDecimal.ONE;
        } else if (matchRatio >= 0.5) {
            return new BigDecimal("0.850");
        } else if (matchRatio >= 0.3) {
            return new BigDecimal("0.700");
        } else {
            return new BigDecimal("0.500");
        }
    }

    /**
     * 应用适配规则调整推荐分数
     */
    private BigDecimal applyAdaptationRules(UserProfile userProfile, LearningPath learningPath, BigDecimal baseScore) {
        try {
            List<PathAdaptationRule> activeRules = pathAdaptationRuleRepository.findActiveRulesOrderByPriority();
            BigDecimal adjustedScore = baseScore;

            for (PathAdaptationRule rule : activeRules) {
                if (evaluateRuleCondition(rule, userProfile, learningPath)) {
                    adjustedScore = applyRuleAction(rule, adjustedScore);
                }
            }

            // 确保分数在有效范围内
            return adjustedScore.max(BigDecimal.ZERO).min(BigDecimal.ONE);

        } catch (Exception e) {
            log.warn("应用适配规则失败，使用基础分数: userId={}, pathId={}", 
                    userProfile.getUserId(), learningPath.getId(), e);
            return baseScore;
        }
    }

    /**
     * 评估规则条件是否满足
     */
    private boolean evaluateRuleCondition(PathAdaptationRule rule, UserProfile userProfile, LearningPath learningPath) {
        // 简化的规则条件评估，实际项目中可以使用更复杂的表达式引擎
        String condition = rule.getConditionExpression().toLowerCase();
        
        // 技能水平条件
        if (condition.contains("skill_level")) {
            if (condition.contains("beginner") && userProfile.getCurrentSkillLevel() <= 2) {
                return true;
            }
            if (condition.contains("intermediate") && userProfile.getCurrentSkillLevel() >= 3 && userProfile.getCurrentSkillLevel() <= 4) {
                return true;
            }
            if (condition.contains("advanced") && userProfile.getCurrentSkillLevel() >= 4) {
                return true;
            }
        }

        // 时间约束条件
        if (condition.contains("time_limited") && userProfile.getAvailableTimePerWeek() <= 10) {
            return true;
        }
        if (condition.contains("time_rich") && userProfile.getAvailableTimePerWeek() >= 20) {
            return true;
        }

        // 学习风格条件
        if (condition.contains("practical") && userProfile.getLearningStyle() == UserProfile.LearningStyle.PRACTICAL) {
            return true;
        }

        return false;
    }

    /**
     * 应用规则动作
     */
    private BigDecimal applyRuleAction(PathAdaptationRule rule, BigDecimal currentScore) {
        Map<String, Object> actionParams = rule.getActionParameters();
        
        switch (rule.getActionType()) {
            case RECOMMEND:
                // 提升推荐分数
                BigDecimal boost = actionParams.containsKey("boost") ? 
                        new BigDecimal(actionParams.get("boost").toString()) : new BigDecimal("0.1");
                return currentScore.add(boost);
                
            case EXCLUDE:
                // 降低推荐分数
                BigDecimal penalty = actionParams.containsKey("penalty") ? 
                        new BigDecimal(actionParams.get("penalty").toString()) : new BigDecimal("0.2");
                return currentScore.subtract(penalty);
                
            case MODIFY:
                // 修改推荐分数
                BigDecimal modifier = actionParams.containsKey("modifier") ? 
                        new BigDecimal(actionParams.get("modifier").toString()) : new BigDecimal("1.0");
                return currentScore.multiply(modifier);
                
            case PRIORITY:
                // 优先级调整
                BigDecimal priorityBoost = actionParams.containsKey("priorityBoost") ? 
                        new BigDecimal(actionParams.get("priorityBoost").toString()) : new BigDecimal("0.05");
                return currentScore.add(priorityBoost);
                
            default:
                return currentScore;
        }
    }

    /**
     * 将技能水平映射到基础分数
     */
    private BigDecimal mapSkillLevelToScore(Integer skillLevel) {
        if (skillLevel == null) {
            return new BigDecimal("0.500");
        }
        
        switch (skillLevel) {
            case 1: return new BigDecimal("0.600");
            case 2: return new BigDecimal("0.700");
            case 3: return new BigDecimal("0.800");
            case 4: return new BigDecimal("0.900");
            case 5: return new BigDecimal("0.950");
            default: return new BigDecimal("0.500");
        }
    }
}
