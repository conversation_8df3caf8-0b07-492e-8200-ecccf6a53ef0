package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.UserBehaviorAnalysisService;
import com.itbook.service.UserBehaviorAnalysisService.LearningBehaviorAnalysis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户行为分析控制器
 * 提供用户学习行为分析相关的API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/user-behavior-analysis")
@RequiredArgsConstructor
@Tag(name = "用户行为分析", description = "用户学习行为分析相关接口")
public class UserBehaviorAnalysisController {

    private final UserBehaviorAnalysisService userBehaviorAnalysisService;

    /**
     * 获取用户学习行为分析报告
     */
    @GetMapping("/{userId}/learning-behavior")
    @Operation(summary = "获取用户学习行为分析", description = "分析用户的学习行为模式，生成详细的行为分析报告")
    public ResponseEntity<ApiResponse<LearningBehaviorAnalysis>> analyzeLearningBehavior(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        
        log.info("获取用户学习行为分析: userId={}", userId);
        
        try {
            LearningBehaviorAnalysis analysis = userBehaviorAnalysisService.analyzeLearningBehavior(userId);
            return ResponseEntity.ok(ApiResponse.success("用户学习行为分析成功", analysis));
            
        } catch (Exception e) {
            log.error("获取用户学习行为分析失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户学习行为分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户学习活跃度概览
     */
    @GetMapping("/{userId}/activity-overview")
    @Operation(summary = "获取用户学习活跃度概览", description = "获取用户学习活跃度的简要概览信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLearningActivityOverview(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        
        log.info("获取用户学习活跃度概览: userId={}", userId);
        
        try {
            LearningBehaviorAnalysis analysis = userBehaviorAnalysisService.analyzeLearningBehavior(userId);
            
            Map<String, Object> overview = new HashMap<>();
            overview.put("userId", userId);
            overview.put("learningActivityScore", analysis.getLearningActivityScore());
            overview.put("totalLearningDays", analysis.getTotalLearningDays());
            overview.put("continuousLearningDays", analysis.getContinuousLearningDays());
            overview.put("averageDailyLearningTime", analysis.getAverageDailyLearningTime());
            overview.put("totalMasteredSkills", analysis.getTotalMasteredSkills());
            overview.put("learningEfficiencyScore", analysis.getLearningEfficiencyScore());
            overview.put("analysisTime", analysis.getAnalysisTime());
            
            return ResponseEntity.ok(ApiResponse.success("获取学习活跃度概览成功", overview));
            
        } catch (Exception e) {
            log.error("获取用户学习活跃度概览失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户学习活跃度概览失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户技能掌握分析
     */
    @GetMapping("/{userId}/skill-mastery-analysis")
    @Operation(summary = "获取用户技能掌握分析", description = "分析用户的技能掌握情况和成长趋势")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSkillMasteryAnalysis(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        
        log.info("获取用户技能掌握分析: userId={}", userId);
        
        try {
            LearningBehaviorAnalysis analysis = userBehaviorAnalysisService.analyzeLearningBehavior(userId);
            
            Map<String, Object> skillAnalysis = new HashMap<>();
            skillAnalysis.put("userId", userId);
            skillAnalysis.put("skillMasteryGrowthRate", analysis.getSkillMasteryGrowthRate());
            skillAnalysis.put("strongSkillAreas", analysis.getStrongSkillAreas());
            skillAnalysis.put("weakSkillAreas", analysis.getWeakSkillAreas());
            skillAnalysis.put("totalMasteredSkills", analysis.getTotalMasteredSkills());
            skillAnalysis.put("preferredSkillCategories", analysis.getPreferredSkillCategories());
            skillAnalysis.put("analysisTime", analysis.getAnalysisTime());
            
            return ResponseEntity.ok(ApiResponse.success("获取技能掌握分析成功", skillAnalysis));
            
        } catch (Exception e) {
            log.error("获取用户技能掌握分析失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户技能掌握分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户学习偏好分析
     */
    @GetMapping("/{userId}/learning-preferences")
    @Operation(summary = "获取用户学习偏好分析", description = "分析用户的学习偏好和习惯")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLearningPreferences(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        
        log.info("获取用户学习偏好分析: userId={}", userId);
        
        try {
            LearningBehaviorAnalysis analysis = userBehaviorAnalysisService.analyzeLearningBehavior(userId);
            
            Map<String, Object> preferences = new HashMap<>();
            preferences.put("userId", userId);
            preferences.put("preferredLearningTime", analysis.getPreferredLearningTime());
            preferences.put("preferredSkillCategories", analysis.getPreferredSkillCategories());
            preferences.put("learningPatternType", analysis.getLearningPatternType());
            preferences.put("averageDailyLearningTime", analysis.getAverageDailyLearningTime());
            preferences.put("analysisTime", analysis.getAnalysisTime());
            
            return ResponseEntity.ok(ApiResponse.success("获取学习偏好分析成功", preferences));
            
        } catch (Exception e) {
            log.error("获取用户学习偏好分析失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户学习偏好分析失败: " + e.getMessage()));
        }
    }



    /**
     * 获取用户学习效率分析
     */
    @GetMapping("/{userId}/learning-efficiency")
    @Operation(summary = "获取用户学习效率分析", description = "分析用户的学习效率和完成情况")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLearningEfficiency(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        
        log.info("获取用户学习效率分析: userId={}", userId);
        
        try {
            LearningBehaviorAnalysis analysis = userBehaviorAnalysisService.analyzeLearningBehavior(userId);
            
            Map<String, Object> efficiency = new HashMap<>();
            efficiency.put("userId", userId);
            efficiency.put("learningEfficiencyScore", analysis.getLearningEfficiencyScore());
            efficiency.put("averageCompletionRate", analysis.getAverageCompletionRate());
            efficiency.put("abandonedPathsCount", analysis.getAbandonedPathsCount());
            efficiency.put("totalMasteredSkills", analysis.getTotalMasteredSkills());
            efficiency.put("skillMasteryGrowthRate", analysis.getSkillMasteryGrowthRate());
            efficiency.put("analysisTime", analysis.getAnalysisTime());
            
            return ResponseEntity.ok(ApiResponse.success("获取学习效率分析成功", efficiency));
            
        } catch (Exception e) {
            log.error("获取用户学习效率分析失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户学习效率分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户画像综合评分
     */
    @GetMapping("/{userId}/profile-score")
    @Operation(summary = "获取用户画像综合评分", description = "计算用户画像的综合评分和等级")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserProfileScore(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        
        log.info("获取用户画像综合评分: userId={}", userId);
        
        try {
            LearningBehaviorAnalysis analysis = userBehaviorAnalysisService.analyzeLearningBehavior(userId);
            
            // 计算综合评分
            double activityWeight = 0.3;
            double efficiencyWeight = 0.3;
            double masteryWeight = 0.2;
            double communityWeight = 0.2;
            
            double totalScore = 
                    (analysis.getLearningActivityScore() * activityWeight) +
                    (analysis.getLearningEfficiencyScore() * efficiencyWeight) +
                    (analysis.getSkillMasteryGrowthRate() * masteryWeight) +
                    (analysis.getCommunityInteractionScore() * communityWeight);
            
            // 确定等级
            String level;
            if (totalScore >= 80) {
                level = "优秀学习者";
            } else if (totalScore >= 60) {
                level = "积极学习者";
            } else if (totalScore >= 40) {
                level = "普通学习者";
            } else {
                level = "新手学习者";
            }
            
            Map<String, Object> scoreInfo = new HashMap<>();
            scoreInfo.put("userId", userId);
            scoreInfo.put("totalScore", Math.round(totalScore * 10.0) / 10.0);
            scoreInfo.put("level", level);
            scoreInfo.put("activityScore", analysis.getLearningActivityScore());
            scoreInfo.put("efficiencyScore", analysis.getLearningEfficiencyScore());
            scoreInfo.put("masteryScore", analysis.getSkillMasteryGrowthRate());
            scoreInfo.put("communityScore", analysis.getCommunityInteractionScore());
            scoreInfo.put("analysisTime", analysis.getAnalysisTime());
            
            return ResponseEntity.ok(ApiResponse.success("获取用户画像综合评分成功", scoreInfo));
            
        } catch (Exception e) {
            log.error("获取用户画像综合评分失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户画像综合评分失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户偏好技能分类分析
     */
    @GetMapping("/{userId}/preferred-skill-categories")
    @Operation(summary = "获取用户偏好技能分类", description = "分析用户偏好的技能分类和学习时间分布")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserPreferredSkillCategories(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {

        log.info("获取用户偏好技能分类: userId={}", userId);

        try {
            Map<String, Object> result = userBehaviorAnalysisService.getUserPreferredSkillCategories(userId);
            return ResponseEntity.ok(ApiResponse.success("获取用户偏好技能分类成功", result));
        } catch (Exception e) {
            log.error("获取用户偏好技能分类失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户偏好技能分类失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户技能分析
     */
    @GetMapping("/{userId}/skill-analysis")
    @Operation(summary = "获取用户技能分析", description = "分析用户已掌握和正在学习的技能")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserSkillAnalysis(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {

        log.info("获取用户技能分析: userId={}", userId);

        try {
            Map<String, Object> result = userBehaviorAnalysisService.getUserSkillAnalysis(userId);
            return ResponseEntity.ok(ApiResponse.success("获取用户技能分析成功", result));
        } catch (Exception e) {
            log.error("获取用户技能分析失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户技能分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户学习洞察
     */
    @GetMapping("/{userId}/learning-insights")
    @Operation(summary = "获取用户学习洞察", description = "分析用户学习行为并提供个性化建议和洞察")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserLearningInsights(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {

        log.info("获取用户学习洞察: userId={}", userId);

        try {
            Map<String, Object> result = userBehaviorAnalysisService.getUserLearningInsights(userId);
            return ResponseEntity.ok(ApiResponse.success("获取用户学习洞察成功", result));
        } catch (Exception e) {
            log.error("获取用户学习洞察失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取用户学习洞察失败: " + e.getMessage()));
        }
    }
}
