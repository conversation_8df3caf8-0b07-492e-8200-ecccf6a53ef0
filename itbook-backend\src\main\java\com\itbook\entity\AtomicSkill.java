package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 原子技能实体
 * 存储最小可验证技能单元，是知识图谱的基础节点
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Entity
@Table(name = "atomic_skill")
public class AtomicSkill {

    /**
     * 技能难度级别枚举
     */
    public enum DifficultyLevel {
        BEGINNER("初级"),
        INTERMEDIATE("中级"), 
        ADVANCED("高级"),
        EXPERT("专家");
        
        private final String description;
        
        DifficultyLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 技能类型枚举
     */
    public enum SkillType {
        CORE("核心技能"),
        SUPPORTING("支撑技能"),
        BONUS("加分技能");
        
        private final String description;
        
        SkillType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 评估方法枚举
     */
    public enum AssessmentMethod {
        QUIZ("在线测试"),
        PROJECT("项目实战"),
        PRACTICE("实践练习"),
        PEER_REVIEW("同行评议");
        
        private final String description;
        
        AssessmentMethod(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 技能状态枚举
     */
    public enum Status {
        DRAFT("草稿"),
        PUBLISHED("已发布"),
        DEPRECATED("已废弃");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 技能编码，全局唯一标识符
     */
    @NotBlank(message = "技能编码不能为空")
    @Size(max = 100, message = "技能编码长度不能超过100字符")
    @Column(name = "skill_code", nullable = false, unique = true)
    private String skillCode;

    /**
     * 技能名称
     */
    @NotBlank(message = "技能名称不能为空")
    @Size(max = 200, message = "技能名称长度不能超过200字符")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 技能详细描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 技能分类
     */
    @NotBlank(message = "技能分类不能为空")
    @Size(max = 100, message = "技能分类长度不能超过100字符")
    @Column(name = "category", nullable = false)
    private String category;

    /**
     * 技能子分类
     */
    @Size(max = 100, message = "技能子分类长度不能超过100字符")
    @Column(name = "subcategory")
    private String subcategory;

    /**
     * 难度级别
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "difficulty_level", nullable = false)
    private DifficultyLevel difficultyLevel = DifficultyLevel.BEGINNER;

    /**
     * 预计学习时长（小时）
     */
    @Min(value = 0, message = "预计学习时长不能为负数")
    @Column(name = "estimated_hours")
    private Integer estimatedHours = 0;

    /**
     * 技能类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "skill_type", nullable = false)
    private SkillType skillType = SkillType.CORE;

    /**
     * 验证标准（JSON格式）
     */
    @Column(name = "verification_criteria", columnDefinition = "JSON")
    private String verificationCriteriaJson;

    /**
     * 评估方法
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "assessment_method")
    private AssessmentMethod assessmentMethod = AssessmentMethod.QUIZ;

    /**
     * 通过阈值(0-1)
     */
    @DecimalMin(value = "0.0", message = "通过阈值不能小于0")
    @DecimalMax(value = "1.0", message = "通过阈值不能大于1")
    @Column(name = "pass_threshold", precision = 3, scale = 2)
    private BigDecimal passThreshold = new BigDecimal("0.70");

    /**
     * 学习资源列表（JSON格式）
     */
    @Column(name = "learning_resources", columnDefinition = "JSON")
    private String learningResourcesJson;

    /**
     * 练习题目列表（JSON格式）
     */
    @Column(name = "practice_exercises", columnDefinition = "JSON")
    private String practiceExercisesJson;

    /**
     * 技能标签（JSON格式）
     */
    @Column(name = "tags", columnDefinition = "JSON")
    private String tagsJson;

    /**
     * 关键词，用于搜索
     */
    @Column(name = "keywords", columnDefinition = "TEXT")
    private String keywords;

    /**
     * 行业相关性（JSON格式）
     */
    @Column(name = "industry_relevance", columnDefinition = "JSON")
    private String industryRelevanceJson;

    /**
     * 学习人数
     */
    @Min(value = 0, message = "学习人数不能为负数")
    @Column(name = "learner_count")
    private Integer learnerCount = 0;

    /**
     * 完成率
     */
    @DecimalMin(value = "0.00", message = "完成率不能小于0")
    @DecimalMax(value = "100.00", message = "完成率不能大于100")
    @Column(name = "completion_rate", precision = 5, scale = 2)
    private BigDecimal completionRate = BigDecimal.ZERO;

    /**
     * 平均评分
     */
    @DecimalMin(value = "0.00", message = "平均评分不能小于0")
    @DecimalMax(value = "5.00", message = "平均评分不能大于5")
    @Column(name = "average_rating", precision = 3, scale = 2)
    private BigDecimal averageRating = BigDecimal.ZERO;

    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.DRAFT;

    /**
     * 版本号
     */
    @Size(max = 20, message = "版本号长度不能超过20字符")
    @Column(name = "version")
    private String version = "1.0";

    /**
     * 是否激活
     */
    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;

    /**
     * 作为源技能的关系列表（一对多关系）
     */
    @OneToMany(mappedBy = "sourceSkill", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<SkillRelationship> outgoingRelationships = new ArrayList<>();

    /**
     * 作为目标技能的关系列表（一对多关系）
     */
    @OneToMany(mappedBy = "targetSkill", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<SkillRelationship> incomingRelationships = new ArrayList<>();

    /**
     * 用户掌握度记录（一对多关系）
     */
    @OneToMany(mappedBy = "atomicSkill", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<UserAtomicSkillMastery> masteryRecords = new ArrayList<>();

    // 构造函数
    public AtomicSkill() {}

    public AtomicSkill(String skillCode, String name, String category) {
        this.skillCode = skillCode;
        this.name = name;
        this.category = category;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getSkillCode() { return skillCode; }
    public void setSkillCode(String skillCode) { this.skillCode = skillCode; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public String getSubcategory() { return subcategory; }
    public void setSubcategory(String subcategory) { this.subcategory = subcategory; }

    public DifficultyLevel getDifficultyLevel() { return difficultyLevel; }
    public void setDifficultyLevel(DifficultyLevel difficultyLevel) { this.difficultyLevel = difficultyLevel; }

    public Integer getEstimatedHours() { return estimatedHours; }
    public void setEstimatedHours(Integer estimatedHours) { this.estimatedHours = estimatedHours; }

    public SkillType getSkillType() { return skillType; }
    public void setSkillType(SkillType skillType) { this.skillType = skillType; }

    public String getVerificationCriteriaJson() { return verificationCriteriaJson; }
    public void setVerificationCriteriaJson(String verificationCriteriaJson) { this.verificationCriteriaJson = verificationCriteriaJson; }

    public AssessmentMethod getAssessmentMethod() { return assessmentMethod; }
    public void setAssessmentMethod(AssessmentMethod assessmentMethod) { this.assessmentMethod = assessmentMethod; }

    public BigDecimal getPassThreshold() { return passThreshold; }
    public void setPassThreshold(BigDecimal passThreshold) { this.passThreshold = passThreshold; }

    public String getLearningResourcesJson() { return learningResourcesJson; }
    public void setLearningResourcesJson(String learningResourcesJson) { this.learningResourcesJson = learningResourcesJson; }

    public String getPracticeExercisesJson() { return practiceExercisesJson; }
    public void setPracticeExercisesJson(String practiceExercisesJson) { this.practiceExercisesJson = practiceExercisesJson; }

    public String getTagsJson() { return tagsJson; }
    public void setTagsJson(String tagsJson) { this.tagsJson = tagsJson; }

    public String getKeywords() { return keywords; }
    public void setKeywords(String keywords) { this.keywords = keywords; }

    public String getIndustryRelevanceJson() { return industryRelevanceJson; }
    public void setIndustryRelevanceJson(String industryRelevanceJson) { this.industryRelevanceJson = industryRelevanceJson; }

    public Integer getLearnerCount() { return learnerCount; }
    public void setLearnerCount(Integer learnerCount) { this.learnerCount = learnerCount; }

    public BigDecimal getCompletionRate() { return completionRate; }
    public void setCompletionRate(BigDecimal completionRate) { this.completionRate = completionRate; }

    public BigDecimal getAverageRating() { return averageRating; }
    public void setAverageRating(BigDecimal averageRating) { this.averageRating = averageRating; }

    public Status getStatus() { return status; }
    public void setStatus(Status status) { this.status = status; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public Long getCreatedBy() { return createdBy; }
    public void setCreatedBy(Long createdBy) { this.createdBy = createdBy; }

    public Long getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(Long updatedBy) { this.updatedBy = updatedBy; }

    public List<SkillRelationship> getOutgoingRelationships() { return outgoingRelationships; }
    public void setOutgoingRelationships(List<SkillRelationship> outgoingRelationships) { this.outgoingRelationships = outgoingRelationships; }

    public List<SkillRelationship> getIncomingRelationships() { return incomingRelationships; }
    public void setIncomingRelationships(List<SkillRelationship> incomingRelationships) { this.incomingRelationships = incomingRelationships; }

    public List<UserAtomicSkillMastery> getMasteryRecords() { return masteryRecords; }
    public void setMasteryRecords(List<UserAtomicSkillMastery> masteryRecords) { this.masteryRecords = masteryRecords; }

    @Override
    public String toString() {
        return "AtomicSkill{" +
                "id=" + id +
                ", skillCode='" + skillCode + '\'' +
                ", name='" + name + '\'' +
                ", category='" + category + '\'' +
                ", subcategory='" + subcategory + '\'' +
                ", difficultyLevel=" + difficultyLevel +
                ", skillType=" + skillType +
                ", status=" + status +
                '}';
    }
}
