package com.itbook.repository;

import com.itbook.entity.UserCareerGoal;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户职业目标数据访问接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-11
 */
@Repository
public interface UserCareerGoalRepository extends JpaRepository<UserCareerGoal, Long> {

    /**
     * 根据用户ID查找激活的职业目标
     * 
     * @param userId 用户ID
     * @return 激活的职业目标（可选）
     */
    Optional<UserCareerGoal> findByUserIdAndIsActiveTrue(Long userId);

    /**
     * 根据用户ID查找所有职业目标
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 职业目标分页列表
     */
    Page<UserCareerGoal> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户ID和激活状态查找职业目标
     * 
     * @param userId 用户ID
     * @param isActive 是否激活
     * @param pageable 分页参数
     * @return 职业目标分页列表
     */
    Page<UserCareerGoal> findByUserIdAndIsActive(Long userId, Boolean isActive, Pageable pageable);

    /**
     * 根据职业目标ID查找所有用户职业目标
     *
     * @param careerGoalId 职业目标ID
     * @param pageable 分页参数
     * @return 职业目标分页列表
     */
    Page<UserCareerGoal> findByCareerGoalId(Long careerGoalId, Pageable pageable);

    /**
     * 根据职业级别ID查找职业目标
     *
     * @param careerLevelId 职业级别ID
     * @param pageable 分页参数
     * @return 职业目标分页列表
     */
    Page<UserCareerGoal> findByCareerLevelId(Long careerLevelId, Pageable pageable);

    /**
     * 查找即将到期的职业目标
     * 
     * @param beforeDate 截止日期
     * @param pageable 分页参数
     * @return 即将到期的职业目标分页列表
     */
    Page<UserCareerGoal> findByTargetCompletionDateBeforeAndIsActiveTrue(LocalDateTime beforeDate, Pageable pageable);

    /**
     * 查找已过期的职业目标
     * 
     * @param currentDate 当前日期
     * @param pageable 分页参数
     * @return 已过期的职业目标分页列表
     */
    Page<UserCareerGoal> findByTargetCompletionDateBeforeAndIsActiveTrueOrderByTargetCompletionDateDesc(
            LocalDateTime currentDate, Pageable pageable);

    /**
     * 根据优先级查找职业目标
     * 
     * @param userId 用户ID
     * @param priority 优先级
     * @return 职业目标列表
     */
    List<UserCareerGoal> findByUserIdAndPriorityOrderBySetAtDesc(Long userId, Integer priority);

    /**
     * 查找用户最近设置的职业目标
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 最近设置的职业目标分页列表
     */
    Page<UserCareerGoal> findByUserIdOrderBySetAtDesc(Long userId, Pageable pageable);

    /**
     * 统计用户的职业目标数量
     * 
     * @param userId 用户ID
     * @return 职业目标数量
     */
    long countByUserId(Long userId);

    /**
     * 统计激活的职业目标数量
     * 
     * @param userId 用户ID
     * @return 激活的职业目标数量
     */
    long countByUserIdAndIsActiveTrue(Long userId);

    /**
     * 检查用户是否已有激活的职业目标
     * 
     * @param userId 用户ID
     * @return 是否存在激活的职业目标
     */
    boolean existsByUserIdAndIsActiveTrue(Long userId);

    /**
     * 停用用户的所有职业目标
     *
     * @param userId 用户ID
     * @return 更新的记录数
     */
    @Modifying
    @Query("UPDATE UserCareerGoal ucg SET ucg.isActive = false WHERE ucg.userId = :userId AND ucg.isActive = true")
    int deactivateAllUserGoals(@Param("userId") Long userId);

    /**
     * 查找热门职业目标统计
     *
     * @param pageable 分页参数
     * @return 职业目标统计列表
     */
    @Query("SELECT ucg.careerGoalId, COUNT(ucg) as goalCount " +
           "FROM UserCareerGoal ucg " +
           "WHERE ucg.careerGoalId IS NOT NULL AND ucg.isActive = true " +
           "GROUP BY ucg.careerGoalId " +
           "ORDER BY goalCount DESC")
    Page<Object[]> findPopularCareerGoals(Pageable pageable);

    /**
     * 查找职业级别分布统计
     * 注意：现在使用targetLevel枚举字段而不是careerLevelId
     *
     * @return 级别分布统计
     */
    @Query("SELECT ucg.targetLevel, COUNT(ucg) as levelCount " +
           "FROM UserCareerGoal ucg " +
           "WHERE ucg.isActive = true " +
           "GROUP BY ucg.targetLevel " +
           "ORDER BY levelCount DESC")
    List<Object[]> findCareerLevelDistribution();

    /**
     * 查找用户职业目标完成情况统计
     * 
     * @param userId 用户ID
     * @return 完成情况统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN ucg.targetCompletionDate > CURRENT_TIMESTAMP THEN 1 END) as activeGoals, " +
           "COUNT(CASE WHEN ucg.targetCompletionDate <= CURRENT_TIMESTAMP THEN 1 END) as expiredGoals, " +
           "COUNT(CASE WHEN ucg.targetCompletionDate IS NULL THEN 1 END) as openEndedGoals " +
           "FROM UserCareerGoal ucg " +
           "WHERE ucg.userId = :userId AND ucg.isActive = true")
    Object[] findUserGoalStatistics(@Param("userId") Long userId);
}
