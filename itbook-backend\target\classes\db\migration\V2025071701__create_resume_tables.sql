-- ===================================================================
-- ITBook简历管理系统 - 数据库表结构
-- 创建时间: 2025-07-17
-- 作者: ITBook Team
-- 
-- 创建简历相关的数据库表
-- ===================================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===================================================================
-- 简历主表
-- ===================================================================
DROP TABLE IF EXISTS `resume`;
CREATE TABLE `resume` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '简历ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '简历标题',
  `summary` text COMMENT '个人简介',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认简历',
  `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开',
  `download_count` int DEFAULT 0 COMMENT '下载次数',
  `view_count` int DEFAULT 0 COMMENT '浏览次数',
  `template_id` varchar(50) DEFAULT NULL COMMENT '模板ID',
  `status` enum('DRAFT','PUBLISHED','ARCHIVED') DEFAULT 'DRAFT' COMMENT '简历状态',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_user_id` (`user_id`),
  KEY `idx_resume_status` (`status`),
  KEY `idx_resume_is_default` (`is_default`),
  CONSTRAINT `fk_resume_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历主表';

-- ===================================================================
-- 个人信息表
-- ===================================================================
DROP TABLE IF EXISTS `resume_personal_info`;
CREATE TABLE `resume_personal_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `full_name` varchar(100) NOT NULL COMMENT '姓名',
  `email` varchar(200) COMMENT '邮箱',
  `phone` varchar(50) COMMENT '电话',
  `location` varchar(200) COMMENT '所在地',
  `github` varchar(500) COMMENT 'GitHub地址',
  `linkedin` varchar(500) COMMENT 'LinkedIn地址',
  `website` varchar(500) COMMENT '个人网站',
  `avatar_url` varchar(500) COMMENT '头像URL',
  `birth_date` date COMMENT '出生日期',
  `gender` enum('MALE','FEMALE','OTHER') COMMENT '性别',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resume_personal_info_resume_id` (`resume_id`),
  CONSTRAINT `fk_resume_personal_info_resume_id` FOREIGN KEY (`resume_id`) REFERENCES `resume` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历个人信息表';

-- ===================================================================
-- 工作经历表
-- ===================================================================
DROP TABLE IF EXISTS `resume_work_experience`;
CREATE TABLE `resume_work_experience` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `company` varchar(200) NOT NULL COMMENT '公司名称',
  `position` varchar(200) NOT NULL COMMENT '职位',
  `description` text COMMENT '工作描述',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `is_current` tinyint(1) DEFAULT 0 COMMENT '是否为当前工作',
  `location` varchar(200) COMMENT '工作地点',
  `achievements` json COMMENT '工作成就列表',
  `technologies` json COMMENT '使用技术列表',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_work_experience_resume_id` (`resume_id`),
  KEY `idx_resume_work_experience_sort_order` (`sort_order`),
  CONSTRAINT `fk_resume_work_experience_resume_id` FOREIGN KEY (`resume_id`) REFERENCES `resume` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历工作经历表';

-- ===================================================================
-- 教育背景表
-- ===================================================================
DROP TABLE IF EXISTS `resume_education`;
CREATE TABLE `resume_education` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `school` varchar(200) NOT NULL COMMENT '学校名称',
  `degree` varchar(100) NOT NULL COMMENT '学位',
  `major` varchar(200) COMMENT '专业',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `is_current` tinyint(1) DEFAULT 0 COMMENT '是否在读',
  `gpa` decimal(3,2) COMMENT 'GPA成绩',
  `description` text COMMENT '描述',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_education_resume_id` (`resume_id`),
  KEY `idx_resume_education_sort_order` (`sort_order`),
  CONSTRAINT `fk_resume_education_resume_id` FOREIGN KEY (`resume_id`) REFERENCES `resume` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历教育背景表';

-- ===================================================================
-- 技能表
-- ===================================================================
DROP TABLE IF EXISTS `resume_skill`;
CREATE TABLE `resume_skill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `name` varchar(100) NOT NULL COMMENT '技能名称',
  `level` enum('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT') DEFAULT 'INTERMEDIATE' COMMENT '技能水平',
  `category` varchar(100) COMMENT '技能分类',
  `years_of_experience` int COMMENT '使用年限',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_skill_resume_id` (`resume_id`),
  KEY `idx_resume_skill_category` (`category`),
  KEY `idx_resume_skill_sort_order` (`sort_order`),
  CONSTRAINT `fk_resume_skill_resume_id` FOREIGN KEY (`resume_id`) REFERENCES `resume` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历技能表';

-- ===================================================================
-- 项目经验表
-- ===================================================================
DROP TABLE IF EXISTS `resume_project`;
CREATE TABLE `resume_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `name` varchar(200) NOT NULL COMMENT '项目名称',
  `description` text COMMENT '项目描述',
  `role` varchar(100) COMMENT '担任角色',
  `start_date` date COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `is_current` tinyint(1) DEFAULT 0 COMMENT '是否进行中',
  `technologies` json COMMENT '使用技术列表',
  `achievements` json COMMENT '项目成果列表',
  `demo_url` varchar(500) COMMENT '演示地址',
  `source_url` varchar(500) COMMENT '源码地址',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_project_resume_id` (`resume_id`),
  KEY `idx_resume_project_sort_order` (`sort_order`),
  CONSTRAINT `fk_resume_project_resume_id` FOREIGN KEY (`resume_id`) REFERENCES `resume` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历项目经验表';

-- ===================================================================
-- 证书资质表
-- ===================================================================
DROP TABLE IF EXISTS `resume_certification`;
CREATE TABLE `resume_certification` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `name` varchar(200) NOT NULL COMMENT '证书名称',
  `issuer` varchar(200) COMMENT '颁发机构',
  `issue_date` date COMMENT '颁发日期',
  `expiry_date` date COMMENT '过期日期',
  `credential_id` varchar(200) COMMENT '证书编号',
  `credential_url` varchar(500) COMMENT '证书链接',
  `description` text COMMENT '证书描述',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_certification_resume_id` (`resume_id`),
  KEY `idx_resume_certification_sort_order` (`sort_order`),
  CONSTRAINT `fk_resume_certification_resume_id` FOREIGN KEY (`resume_id`) REFERENCES `resume` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历证书资质表';

-- ===================================================================
-- 语言能力表
-- ===================================================================
DROP TABLE IF EXISTS `resume_language`;
CREATE TABLE `resume_language` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `name` varchar(100) NOT NULL COMMENT '语言名称',
  `level` enum('BASIC','CONVERSATIONAL','FLUENT','NATIVE') DEFAULT 'CONVERSATIONAL' COMMENT '语言水平',
  `description` varchar(500) COMMENT '语言描述',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_language_resume_id` (`resume_id`),
  KEY `idx_resume_language_sort_order` (`sort_order`),
  CONSTRAINT `fk_resume_language_resume_id` FOREIGN KEY (`resume_id`) REFERENCES `resume` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='简历语言能力表';

SET FOREIGN_KEY_CHECKS = 1;

-- ===================================================================
-- 插入测试数据
-- ===================================================================

-- 插入测试简历
INSERT INTO `resume` (`user_id`, `title`, `summary`, `is_default`, `is_public`, `download_count`, `view_count`, `status`) VALUES
(2, '前端开发工程师简历', '3年前端开发经验，熟练掌握React、Vue、TypeScript等技术栈，有丰富的移动端开发经验。', 1, 1, 156, 1250, 'PUBLISHED'),
(2, 'Java后端开发简历', '2年Java开发经验，熟悉Spring Boot、微服务架构，有分布式系统开发经验。', 0, 0, 89, 567, 'PUBLISHED');

-- 插入个人信息
INSERT INTO `resume_personal_info` (`resume_id`, `full_name`, `email`, `phone`, `location`, `github`, `linkedin`) VALUES
(1, '张三', '<EMAIL>', '138****8888', '北京', 'https://github.com/zhangsan', 'https://linkedin.com/in/zhangsan'),
(2, '张三', '<EMAIL>', '138****8888', '北京', NULL, NULL);

-- 插入工作经历
INSERT INTO `resume_work_experience` (`resume_id`, `company`, `position`, `description`, `start_date`, `end_date`, `is_current`, `location`, `achievements`, `technologies`, `sort_order`) VALUES
(1, '阿里巴巴', '前端开发工程师', '负责电商平台前端开发，参与多个核心项目的架构设计和开发工作。', '2022-01-01', '2024-12-01', 1, '杭州', '["优化页面加载速度提升30%", "主导移动端重构项目", "指导3名初级开发工程师"]', '["React", "TypeScript", "Vue", "Webpack", "Node.js"]', 1),
(1, '腾讯', '前端实习生', '参与微信小程序开发，负责组件库建设和维护工作。', '2021-06-01', '2021-12-31', 0, '深圳', '["开发10+个通用组件", "参与小程序性能优化"]', '["Vue", "JavaScript", "小程序"]', 2);

-- 插入教育背景
INSERT INTO `resume_education` (`resume_id`, `school`, `degree`, `major`, `start_date`, `end_date`, `is_current`, `gpa`, `description`, `sort_order`) VALUES
(1, '北京大学', '本科', '计算机科学与技术', '2017-09-01', '2021-06-30', 0, 3.8, '主修计算机科学与技术，辅修数学。在校期间参与多个科研项目，发表论文2篇。', 1),
(2, '清华大学', '本科', '软件工程', '2018-09-01', '2022-06-30', 0, 3.9, '主修软件工程，专注于后端开发和系统架构设计。', 1);

-- 插入技能
INSERT INTO `resume_skill` (`resume_id`, `name`, `level`, `category`, `years_of_experience`, `sort_order`) VALUES
(1, 'React', 'ADVANCED', '前端框架', 3, 1),
(1, 'TypeScript', 'ADVANCED', '编程语言', 3, 2),
(1, 'Vue', 'INTERMEDIATE', '前端框架', 2, 3),
(1, 'Node.js', 'INTERMEDIATE', '后端技术', 2, 4),
(1, 'Webpack', 'INTERMEDIATE', '构建工具', 2, 5),
(2, 'Java', 'ADVANCED', '编程语言', 2, 1),
(2, 'Spring Boot', 'ADVANCED', '后端框架', 2, 2),
(2, 'MySQL', 'INTERMEDIATE', '数据库', 2, 3),
(2, 'Redis', 'INTERMEDIATE', '缓存', 1, 4);

-- 插入项目经验
INSERT INTO `resume_project` (`resume_id`, `name`, `description`, `role`, `start_date`, `end_date`, `is_current`, `technologies`, `achievements`, `demo_url`, `source_url`, `sort_order`) VALUES
(1, '电商管理后台', '基于React的电商管理后台系统，支持商品管理、订单处理、用户管理等功能。', '前端负责人', '2023-01-01', '2023-06-30', 0, '["React", "TypeScript", "Ant Design", "Redux"]', '["完成20+个功能模块开发", "系统性能提升40%", "用户体验评分提升至4.8分"]', 'https://demo.example.com', 'https://github.com/example/admin', 1),
(1, '移动端H5应用', '响应式H5应用，支持多端适配，提供流畅的移动端用户体验。', '核心开发', '2022-06-01', '2022-12-31', 0, '["Vue", "Vant", "Sass", "Webpack"]', '["支持iOS/Android双端", "页面加载速度提升50%"]', 'https://m.example.com', NULL, 2);

-- 插入证书资质
INSERT INTO `resume_certification` (`resume_id`, `name`, `issuer`, `issue_date`, `credential_id`, `description`, `sort_order`) VALUES
(1, 'AWS Certified Developer', 'Amazon Web Services', '2023-03-15', 'AWS-DEV-2023-001', 'AWS开发者认证，证明具备在AWS平台上开发和部署应用的能力。', 1),
(2, 'Oracle Certified Java Programmer', 'Oracle', '2022-08-20', 'OCP-JAVA-2022-002', 'Oracle Java程序员认证，证明具备Java开发的专业技能。', 1);

-- 插入语言能力
INSERT INTO `resume_language` (`resume_id`, `name`, `level`, `description`, `sort_order`) VALUES
(1, '中文', 'NATIVE', '母语', 1),
(1, '英语', 'FLUENT', 'CET-6，能够流利进行技术交流和文档阅读', 2),
(2, '中文', 'NATIVE', '母语', 1),
(2, '英语', 'CONVERSATIONAL', 'CET-4，能够进行基本的技术交流', 2);
