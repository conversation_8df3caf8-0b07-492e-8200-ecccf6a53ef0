import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StyleSheet,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { dynamicLearningPathService } from '../../services/DynamicLearningPathService';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';

/**
 * 动态学习路径生成页面
 * 提供个性化学习路径生成和推荐功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
const DynamicLearningPathScreen: React.FC = () => {
  const colors = useThemeColors();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [learningPlan, setLearningPlan] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState<'recommendations' | 'plan'>('recommendations');

  // 从Redux获取用户信息
  const { user } = useSelector((state: RootState) => state.auth);
  const userId = user?.id || 2; // 默认用户ID

  useEffect(() => {
    loadPersonalizedRecommendations();
  }, []);

  // 加载个性化推荐
  const loadPersonalizedRecommendations = async () => {
    try {
      setError(null);
      setLoading(true);

      const result = await dynamicLearningPathService.getUserPersonalizedRecommendations(userId, undefined, 10);
      
      if (result.success && result.data) {
        setRecommendations(result.data.recommendations || []);
        setLearningPlan(result.data.learningPlan || null);
      } else {
        setError(result.message || '加载个性化推荐失败');
      }
    } catch (error) {
      console.error('加载个性化推荐失败:', error);
      setError('加载个性化推荐失败，请重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 生成个性化学习路径
  const generatePersonalizedPath = async (targetJobId: number) => {
    try {
      setLoading(true);
      
      const result = await dynamicLearningPathService.generatePersonalizedPath(userId, targetJobId);
      
      if (result.success) {
        Alert.alert(
          '生成成功',
          '个性化学习路径已生成，正在刷新推荐...',
          [{ text: '确定', onPress: () => loadPersonalizedRecommendations() }]
        );
      } else {
        Alert.alert('生成失败', result.message || '生成个性化学习路径失败');
      }
    } catch (error) {
      console.error('生成个性化学习路径失败:', error);
      Alert.alert('生成失败', '生成个性化学习路径失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理刷新
  const handleRefresh = () => {
    setRefreshing(true);
    loadPersonalizedRecommendations();
  };

  // 渲染推荐卡片
  const renderRecommendationCard = (recommendation: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={[styles.recommendationCard, { backgroundColor: colors.backgroundSecondary }]}
      activeOpacity={0.7}
      onPress={() => {
        // TODO: 导航到学习路径详情页面
        console.log('点击推荐:', recommendation);
      }}
    >
      <View style={styles.cardHeader}>
        <Text style={[styles.cardTitle, { color: colors.textPrimary }]}>
          {recommendation.learningPath?.name || '个性化学习路径'}
        </Text>
        <View style={[styles.scoreTag, { backgroundColor: colors.primary + '20' }]}>
          <Text style={[styles.scoreText, { color: colors.primary }]}>
            {Math.round((recommendation.score || 0) * 100)}%
          </Text>
        </View>
      </View>
      
      <Text style={[styles.cardDescription, { color: colors.textSecondary }]}>
        {recommendation.learningPath?.description || '基于您的技能水平和学习偏好定制的学习路径'}
      </Text>
      
      <View style={styles.cardFooter}>
        <View style={styles.cardStats}>
          <View style={styles.statItem}>
            <Ionicons name="time-outline" size={16} color={colors.textSecondary} />
            <Text style={[styles.statText, { color: colors.textSecondary }]}>
              {recommendation.learningPath?.estimatedHours || 0}小时
            </Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="trending-up-outline" size={16} color={colors.textSecondary} />
            <Text style={[styles.statText, { color: colors.textSecondary }]}>
              {recommendation.learningPath?.difficultyLevel || '中级'}
            </Text>
          </View>
        </View>
        
        <Text style={[styles.reasonText, { color: colors.textTertiary }]}>
          {recommendation.reason || '基于您的学习历史推荐'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  // 渲染学习计划
  const renderLearningPlan = () => {
    if (!learningPlan) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="document-outline" size={64} color={colors.textTertiary} />
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            暂无个性化学习计划
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.planContainer}>
        <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
          个性化学习计划
        </Text>
        
        {learningPlan.suggestedSkills && learningPlan.suggestedSkills.length > 0 && (
          <View style={styles.skillsSection}>
            <Text style={[styles.subsectionTitle, { color: colors.textPrimary }]}>
              推荐技能
            </Text>
            {learningPlan.suggestedSkills.map((skill: any, index: number) => (
              <View key={index} style={[styles.skillCard, { backgroundColor: colors.backgroundSecondary }]}>
                <Text style={[styles.skillName, { color: colors.textPrimary }]}>
                  {skill.skill?.name || '未知技能'}
                </Text>
                <Text style={[styles.skillReason, { color: colors.textSecondary }]}>
                  {skill.suggestionReason || '推荐理由'}
                </Text>
                <View style={[styles.skillScore, { backgroundColor: colors.primary + '20' }]}>
                  <Text style={[styles.skillScoreText, { color: colors.primary }]}>
                    {Math.round((skill.suggestionScore || 0) * 100)}%
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textPrimary }]}>
            加载个性化推荐中...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
            加载失败
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadPersonalizedRecommendations}
            activeOpacity={0.7}
          >
            <Text style={[styles.retryButtonText, { color: colors.backgroundPrimary }]}>
              重新加载
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundPrimary }]}>
      {/* 顶部导航栏 */}
      <View style={[styles.header, { backgroundColor: colors.backgroundPrimary, borderBottomColor: colors.border }]}>
        <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
          动态学习路径
        </Text>
        
        <TouchableOpacity
          onPress={handleRefresh}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Tab 切换 */}
      <View style={[styles.tabContainer, { backgroundColor: colors.backgroundPrimary }]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'recommendations' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('recommendations')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'recommendations' ? colors.primary : colors.textSecondary }
          ]}>
            智能推荐
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tabButton,
            { borderBottomColor: selectedTab === 'plan' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('plan')}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            { color: selectedTab === 'plan' ? colors.primary : colors.textSecondary }
          ]}>
            学习计划
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'recommendations' && (
          <View style={styles.recommendationsContainer}>
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              为您推荐的学习路径
            </Text>
            
            {recommendations.length > 0 ? (
              recommendations.map((recommendation, index) => 
                renderRecommendationCard(recommendation, index)
              )
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons name="bulb-outline" size={64} color={colors.textTertiary} />
                <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                  暂无个性化推荐
                </Text>
                <Text style={[styles.emptySubtext, { color: colors.textTertiary }]}>
                  完善您的技能档案以获得更好的推荐
                </Text>
              </View>
            )}
          </View>
        )}
        
        {selectedTab === 'plan' && renderLearningPlan()}
        
        {/* 底部间距 */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('medium'),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing('lg'),
  },
  errorTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('semiBold'),
    textAlign: 'center',
    marginTop: tokens.spacing('md'),
    marginBottom: tokens.spacing('sm'),
  },
  errorMessage: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    marginBottom: tokens.spacing('lg'),
  },
  retryButton: {
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
  },
  retryButtonText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
  },
  tabButton: {
    flex: 1,
    paddingVertical: tokens.spacing('sm'),
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('semiBold'),
  },
  content: {
    flex: 1,
  },
  recommendationsContainer: {
    padding: tokens.spacing('md'),
  },
  sectionTitle: {
    fontSize: tokens.fontSize('lg'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('md'),
  },
  recommendationCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('md'),
    marginBottom: tokens.spacing('md'),
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: tokens.spacing('sm'),
  },
  cardTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
    flex: 1,
    marginRight: tokens.spacing('sm'),
  },
  scoreTag: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
  },
  scoreText: {
    fontSize: tokens.fontSize('xs'),
    fontWeight: tokens.fontWeight('bold'),
  },
  cardDescription: {
    fontSize: tokens.fontSize('sm'),
    lineHeight: 20,
    marginBottom: tokens.spacing('md'),
  },
  cardFooter: {
    gap: tokens.spacing('sm'),
  },
  cardStats: {
    flexDirection: 'row',
    gap: tokens.spacing('md'),
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing('xs'),
  },
  statText: {
    fontSize: tokens.fontSize('xs'),
  },
  reasonText: {
    fontSize: tokens.fontSize('xs'),
    fontStyle: 'italic',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: tokens.spacing('xl'),
  },
  emptyText: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('medium'),
    marginTop: tokens.spacing('md'),
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: tokens.fontSize('sm'),
    marginTop: tokens.spacing('xs'),
    textAlign: 'center',
  },
  planContainer: {
    padding: tokens.spacing('md'),
  },
  subsectionTitle: {
    fontSize: tokens.fontSize('md'),
    fontWeight: tokens.fontWeight('semiBold'),
    marginBottom: tokens.spacing('sm'),
  },
  skillsSection: {
    marginTop: tokens.spacing('md'),
  },
  skillCard: {
    padding: tokens.spacing('md'),
    borderRadius: tokens.radius('sm'),
    marginBottom: tokens.spacing('sm'),
    flexDirection: 'row',
    alignItems: 'center',
  },
  skillName: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
    flex: 1,
  },
  skillReason: {
    fontSize: tokens.fontSize('xs'),
    flex: 2,
    marginHorizontal: tokens.spacing('sm'),
  },
  skillScore: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('xs'),
  },
  skillScoreText: {
    fontSize: tokens.fontSize('xs'),
    fontWeight: tokens.fontWeight('bold'),
  },
});

export default DynamicLearningPathScreen;
