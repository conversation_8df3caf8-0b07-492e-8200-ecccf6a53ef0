-- ===================================================================
-- ITBook数据库表结构重构 - 数据迁移脚本
-- 创建时间: 2025-07-14
-- 作者: ITBook Team
-- 
-- 任务1：将career_path数据迁移到career_level表，删除career_path表
-- 任务2：将project表功能整合到career_project表，删除project表
-- ===================================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===================================================================
-- 任务1：career_path和career_level表整合
-- ===================================================================

-- 第一步：扩展career_level表结构，添加career_path表中的字段
ALTER TABLE `career_level` 
ADD COLUMN `duration` varchar(50) COMMENT '预期时长' AFTER `description`,
ADD COLUMN `skills` json COMMENT '所需技能列表' AFTER `duration`,
ADD COLUMN `projects` json COMMENT '推荐项目列表' AFTER `skills`;

-- 第二步：将career_path表中的数据迁移到career_level表
-- 更新已存在的career_level记录
UPDATE career_level cl 
JOIN career_path cp ON cl.career_goal_id = cp.career_goal_id AND cl.level_code = cp.level
SET 
    cl.duration = cp.duration,
    cl.skills = cp.skills,
    cl.projects = cp.projects,
    cl.salary_range_min = cp.salary_min * 1000,  -- 转换为元
    cl.salary_range_max = cp.salary_max * 1000,  -- 转换为元
    cl.updated_at = CURRENT_TIMESTAMP;

-- 第三步：插入career_path中存在但career_level中不存在的记录
INSERT INTO career_level (
    career_goal_id, level_code, level_name, description, duration, skills, projects,
    min_experience_years, max_experience_years, salary_range_min, salary_range_max, sort_order
)
SELECT 
    cp.career_goal_id,
    cp.level as level_code,
    CASE 
        WHEN cp.level = 'junior' THEN '初级工程师'
        WHEN cp.level = 'mid' THEN '中级工程师'
        WHEN cp.level = 'senior' THEN '高级工程师'
        ELSE cp.level
    END as level_name,
    CONCAT('职业级别：', cp.level) as description,
    cp.duration,
    cp.skills,
    cp.projects,
    CASE 
        WHEN cp.level = 'junior' THEN 0
        WHEN cp.level = 'mid' THEN 2
        WHEN cp.level = 'senior' THEN 5
        ELSE 0
    END as min_experience_years,
    CASE 
        WHEN cp.level = 'junior' THEN 2
        WHEN cp.level = 'mid' THEN 5
        WHEN cp.level = 'senior' THEN 10
        ELSE NULL
    END as max_experience_years,
    cp.salary_min * 1000 as salary_range_min,  -- 转换为元
    cp.salary_max * 1000 as salary_range_max,  -- 转换为元
    CASE 
        WHEN cp.level = 'junior' THEN 1
        WHEN cp.level = 'mid' THEN 2
        WHEN cp.level = 'senior' THEN 3
        ELSE 99
    END as sort_order
FROM career_path cp
LEFT JOIN career_level cl ON cp.career_goal_id = cl.career_goal_id AND cp.level = cl.level_code
WHERE cl.id IS NULL;

-- ===================================================================
-- 任务2：project和career_project表整合
-- ===================================================================

-- 第一步：扩展career_project表结构，添加project表中的展示相关字段
ALTER TABLE `career_project`
ADD COLUMN `image_url` varchar(500) COMMENT '项目封面图片URL' AFTER `description`,
ADD COLUMN `participants` int NOT NULL DEFAULT 0 COMMENT '参与人数' AFTER `deliverables`,
ADD COLUMN `rating` decimal(3,2) NOT NULL DEFAULT 0.00 COMMENT '项目评分' AFTER `participants`,
ADD COLUMN `status` enum('ACTIVE','COMPLETED','ARCHIVED') NOT NULL DEFAULT 'ACTIVE' COMMENT '项目状态' AFTER `rating`,
ADD COLUMN `type` enum('WEB_DEVELOPMENT','MOBILE_DEVELOPMENT','BACKEND_DEVELOPMENT','FULLSTACK_DEVELOPMENT','DATA_SCIENCE','MACHINE_LEARNING','DEVOPS','GAME_DEVELOPMENT','BLOCKCHAIN','IOT') NOT NULL DEFAULT 'WEB_DEVELOPMENT' COMMENT '项目类型' AFTER `status`,
ADD COLUMN `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为精选项目' AFTER `type`,
ADD COLUMN `is_trending` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为热门项目' AFTER `is_featured`,
ADD COLUMN `view_count` int NOT NULL DEFAULT 0 COMMENT '浏览次数' AFTER `is_trending`,
ADD COLUMN `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞次数' AFTER `view_count`,
ADD COLUMN `bookmark_count` int NOT NULL DEFAULT 0 COMMENT '收藏次数' AFTER `like_count`,
ADD COLUMN `source_url` varchar(500) COMMENT '项目源码链接' AFTER `bookmark_count`,
ADD COLUMN `demo_url` varchar(500) COMMENT '项目演示链接' AFTER `source_url`;

-- 第二步：将project表数据迁移到career_project表
-- 为每个project创建对应的career_project记录，默认关联到通用职业目标
INSERT INTO career_project (
    career_goal_id, project_id, title, description, image_url, difficulty, tech_stack, estimated_time,
    business_scenario, learning_objectives, deliverables, participants, rating, status, type,
    is_featured, is_trending, view_count, like_count, bookmark_count, source_url, demo_url
)
SELECT 
    1 as career_goal_id,  -- 默认关联到第一个职业目标，后续可以调整
    CONCAT('project_', p.id) as project_id,
    p.title,
    p.description,
    p.image_url,
    CASE 
        WHEN p.difficulty = 'BEGINNER' THEN 'junior'
        WHEN p.difficulty = 'INTERMEDIATE' THEN 'mid'
        WHEN p.difficulty = 'ADVANCED' THEN 'senior'
        ELSE 'mid'
    END as difficulty,
    p.technologies as tech_stack,
    p.duration as estimated_time,
    CONCAT('项目类型：', p.type) as business_scenario,
    JSON_ARRAY('实践项目开发', '掌握相关技术栈', '提升编程能力') as learning_objectives,
    JSON_ARRAY('完整项目代码', '项目文档', '演示视频') as deliverables,
    p.participants,
    p.rating,
    p.status,
    p.type,
    p.is_featured,
    p.is_trending,
    p.view_count,
    p.like_count,
    p.bookmark_count,
    p.source_url,
    p.demo_url
FROM project p;

-- ===================================================================
-- 数据迁移验证
-- ===================================================================

-- 验证career_level表数据完整性
SELECT 
    'career_level_after_migration' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT level_code) as unique_levels,
    SUM(CASE WHEN skills IS NOT NULL THEN 1 ELSE 0 END) as records_with_skills,
    SUM(CASE WHEN projects IS NOT NULL THEN 1 ELSE 0 END) as records_with_projects
FROM career_level;

-- 验证career_project表数据完整性
SELECT 
    'career_project_after_migration' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT difficulty) as unique_difficulties,
    SUM(CASE WHEN image_url IS NOT NULL THEN 1 ELSE 0 END) as records_with_images,
    SUM(participants) as total_participants,
    AVG(rating) as average_rating
FROM career_project;

SET FOREIGN_KEY_CHECKS = 1;

-- ===================================================================
-- 迁移完成提示
-- ===================================================================
SELECT 'Data migration completed successfully!' as status;
