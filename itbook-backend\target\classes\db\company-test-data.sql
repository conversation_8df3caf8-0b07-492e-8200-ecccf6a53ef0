-- 公司测试数据
-- 用于测试公司相关功能

-- 清空现有数据
DELETE FROM `company`;

-- 重置自增ID
ALTER TABLE `company` AUTO_INCREMENT = 1;

-- 插入测试数据
INSERT INTO `company` (`id`, `name`, `description`, `website`, `industry`, `size_range`, `location`, `logo_url`, `rating`, `review_count`, `created_at`, `updated_at`) VALUES
(1, '阿里巴巴', '阿里巴巴集团是一家以电子商务为核心的科技公司，致力于让天下没有难做的生意。公司业务包括电商、云计算、数字媒体、金融科技等，是全球领先的数字经济体。', 'https://www.alibaba.com', '互联网', '10000+', '杭州', 'https://example.com/logos/alibaba.png', 4.5, 1250, NOW(), NOW()),
(2, '腾讯', '腾讯是一家世界领先的互联网科技公司，用技术丰富互联网用户的生活。公司业务涵盖社交、游戏、金融科技、企业服务等多个领域，致力于成为最受尊敬的互联网企业。', 'https://www.tencent.com', '互联网', '10000+', '深圳', 'https://example.com/logos/tencent.png', 4.3, 980, NOW(), NOW()),
(3, '字节跳动', '字节跳动是一家全球化的互联网技术公司，致力于用技术丰富人们的生活。公司旗下拥有抖音、今日头条、西瓜视频等知名产品，在人工智能、大数据、云计算等领域具有领先优势。', 'https://www.bytedance.com', '互联网', '10000+', '北京', 'https://example.com/logos/bytedance.png', 4.4, 1100, NOW(), NOW()),
(4, '美团', '美团是中国领先的生活服务电子商务平台，以"帮大家吃得更好，生活更好"为使命。公司业务涵盖餐饮外卖、到店服务、出行、住宿、新零售等多个领域。', 'https://www.meituan.com', '生活服务', '10000+', '北京', 'https://example.com/logos/meituan.png', 4.2, 850, NOW(), NOW()),
(5, '百度', '百度是全球最大的中文搜索引擎，致力于让人们更便捷地获取信息，找到所求。公司在人工智能、自动驾驶、云计算等前沿技术领域持续投入和创新。', 'https://www.baidu.com', '互联网', '10000+', '北京', 'https://example.com/logos/baidu.png', 4.1, 720, NOW(), NOW()),
(6, '华为', '华为是全球领先的ICT（信息与通信）基础设施和智能终端提供商，致力于把数字世界带入每个人、每个家庭、每个组织，构建万物互联的智能世界。', 'https://www.huawei.com', '通信技术', '10000+', '深圳', 'https://example.com/logos/huawei.png', 4.6, 1500, NOW(), NOW()),
(7, '小米', '小米是一家以手机、智能硬件和IoT平台为核心的互联网公司。公司秉承"让每个人都能享受科技的乐趣"的愿景，不断探索科技与生活的融合。', 'https://www.mi.com', '消费电子', '10000+', '北京', 'https://example.com/logos/xiaomi.png', 4.0, 680, NOW(), NOW()),
(8, '京东', '京东是中国领先的综合型电商平台，提供超过2亿种商品，涵盖家电、手机、电脑、服装、食品等13大品类。公司致力于为消费者提供优质的购物体验。', 'https://www.jd.com', '电子商务', '10000+', '北京', 'https://example.com/logos/jd.png', 4.2, 920, NOW(), NOW()),
(9, '网易', '网易是中国领先的互联网技术公司，在在线游戏、音乐、电子邮件、电子商务、广告等领域拥有多款知名产品。公司致力于通过互联网服务提升人们的生活品质。', 'https://www.netease.com', '互联网', '10000+', '杭州', 'https://example.com/logos/netease.png', 4.3, 750, NOW(), NOW()),
(10, '滴滴出行', '滴滴出行是全球领先的移动出行平台，为超过5.5亿用户提供出租车、专车、快车、顺风车、代驾等全面的出行服务。公司致力于通过共享经济模式解决城市出行问题。', 'https://www.didiglobal.com', '出行服务', '10000+', '北京', 'https://example.com/logos/didi.png', 3.9, 620, NOW(), NOW()),
(11, '拼多多', '拼多多是一个专注于C2M（Customer-to-Manufacturer）的社交电商平台，通过拼团模式为用户提供高性价比的商品。公司致力于让更多人享受到更好的商品和服务。', 'https://www.pinduoduo.com', '电子商务', '5000-9999', '上海', 'https://example.com/logos/pdd.png', 3.8, 580, NOW(), NOW()),
(12, '快手', '快手是中国领先的短视频社交平台，致力于记录和分享大众的日常生活。公司通过技术创新，为用户提供丰富的内容创作和社交互动体验。', 'https://www.kuaishou.com', '社交媒体', '5000-9999', '北京', 'https://example.com/logos/kuaishou.png', 4.0, 600, NOW(), NOW()),
(13, '携程', '携程是中国领先的在线旅行服务提供商，为用户提供酒店预订、机票预订、旅游度假、商旅管理等一站式旅行服务。公司致力于让旅行更加便捷和愉悦。', 'https://www.ctrip.com', '旅游服务', '5000-9999', '上海', 'https://example.com/logos/ctrip.png', 4.1, 700, NOW(), NOW()),
(14, '科大讯飞', '科大讯飞是中国领先的智能语音和人工智能技术提供商，专注于语音识别、语音合成、自然语言处理等核心技术研发。公司致力于让机器能听会说，能理解会思考。', 'https://www.iflytek.com', '人工智能', '1000-4999', '合肥', 'https://example.com/logos/iflytek.png', 4.4, 450, NOW(), NOW()),
(15, '商汤科技', '商汤科技是全球领先的人工智能平台公司，专注于计算机视觉和深度学习技术。公司致力于用AI技术赋能各行各业，推动人工智能产业化进程。', 'https://www.sensetime.com', '人工智能', '1000-4999', '北京', 'https://example.com/logos/sensetime.png', 4.5, 380, NOW(), NOW());
