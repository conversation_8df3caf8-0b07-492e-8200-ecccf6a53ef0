package com.itbook.service;

import com.itbook.entity.UserCareerGoal;
import com.itbook.entity.UserLearningPathProgress;
import com.itbook.entity.UserStepProgress;
import com.itbook.entity.CareerLevel;
import com.itbook.repository.UserCareerGoalRepository;
import com.itbook.repository.UserLearningPathProgressRepository;
import com.itbook.repository.UserStepProgressRepository;
import com.itbook.repository.CareerLevelRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 统一进度管理服务
 * 整合学习路径进度、职业进度、技能进度等功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-11
 */
@Service
@Transactional
public class UnifiedProgressService {

    private static final Logger log = LoggerFactory.getLogger(UnifiedProgressService.class);

    @Autowired
    private UserCareerGoalRepository careerGoalRepository;

    @Autowired
    private UserLearningPathProgressRepository pathProgressRepository;

    @Autowired
    private UserStepProgressRepository stepProgressRepository;

    @Autowired
    private CareerLevelRepository careerLevelRepository;

    @Autowired
    private CareerProgressAggregationService careerProgressService;

    @Autowired
    private SkillProgressCalculationService skillProgressService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取用户综合进度概览
     * 
     * @param userId 用户ID
     * @return 综合进度信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getUserProgressSummary(Long userId) {
        log.info("获取用户综合进度概览: userId={}", userId);

        Map<String, Object> summary = new HashMap<>();

        try {
            // 1. 获取职业目标信息
            Optional<UserCareerGoal> careerGoalOpt = careerGoalRepository.findByUserIdAndIsActiveTrue(userId);
            summary.put("hasCareerGoal", careerGoalOpt.isPresent());
            if (careerGoalOpt.isPresent()) {
                summary.put("careerGoal", careerGoalOpt.get());
            }

            // 2. 获取学习路径进度统计
            List<UserLearningPathProgress> pathProgresses = pathProgressRepository.findByUserId(userId);
            Map<String, Object> pathStats = new HashMap<>();
            pathStats.put("totalPaths", pathProgresses.size());
            pathStats.put("completedPaths", pathProgresses.stream()
                .mapToLong(p -> UserLearningPathProgress.Status.COMPLETED.equals(p.getStatus()) ? 1 : 0)
                .sum());
            pathStats.put("inProgressPaths", pathProgresses.stream()
                .mapToLong(p -> UserLearningPathProgress.Status.IN_PROGRESS.equals(p.getStatus()) ? 1 : 0)
                .sum());
            
            // 计算平均完成率
            if (!pathProgresses.isEmpty()) {
                BigDecimal avgCompletion = pathProgresses.stream()
                    .map(UserLearningPathProgress::getCompletionPercentage)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(pathProgresses.size()), 2, BigDecimal.ROUND_HALF_UP);
                pathStats.put("averageCompletion", avgCompletion);
            } else {
                pathStats.put("averageCompletion", BigDecimal.ZERO);
            }
            
            summary.put("learningPaths", pathStats);

            // 3. 获取技能进度概览
            List<SkillProgressCalculationService.SkillProgressView> skillProgresses = 
                skillProgressService.calculateUserSkillProgress(userId);
            Map<String, Object> skillStats = new HashMap<>();
            skillStats.put("totalSkills", skillProgresses.size());
            skillStats.put("topSkills", skillProgresses.stream()
                .sorted((a, b) -> b.getOverallProgress().compareTo(a.getOverallProgress()))
                .limit(5)
                .toArray());
            summary.put("skills", skillStats);

            // 4. 获取学习统计
            Map<String, Object> learningStats = calculateLearningStatistics(userId);
            summary.put("statistics", learningStats);

            // 5. 获取职业进度
            if (careerGoalOpt.isPresent()) {
                CareerProgressAggregationService.CareerProgressDetail careerProgress = 
                    careerProgressService.calculateCareerProgress(userId);
                summary.put("careerProgress", careerProgress.getOverallProgress());
            } else {
                summary.put("careerProgress", BigDecimal.ZERO);
            }

            log.info("用户综合进度概览获取成功: userId={}", userId);
            return summary;

        } catch (Exception e) {
            log.error("获取用户综合进度概览异常: userId={}", userId, e);
            throw new RuntimeException("获取用户综合进度概览失败", e);
        }
    }

    /**
     * 设置用户职业目标
     * 
     * @param userId 用户ID
     * @param request 职业目标设置请求
     * @return 创建的职业目标
     */
    public UserCareerGoal setUserCareerGoal(Long userId, Map<String, Object> request) {
        log.info("设置用户职业目标: userId={}, request={}", userId, request);

        try {
            // 1. 停用现有的激活目标
            careerGoalRepository.deactivateAllUserGoals(userId);

            // 2. 创建新的职业目标
            UserCareerGoal careerGoal = new UserCareerGoal();
            careerGoal.setUserId(userId);
            
            // 处理职业目标ID（支持两种字段名：careerGoalId 和 targetJobId）
            Object goalIdObj = null;
            if (request.containsKey("careerGoalId")) {
                goalIdObj = request.get("careerGoalId");
            } else if (request.containsKey("targetJobId")) {
                // 向后兼容：前端可能仍使用targetJobId字段名
                goalIdObj = request.get("targetJobId");
                log.info("使用向后兼容的targetJobId字段，建议前端改为careerGoalId");
            }

            if (goalIdObj != null) {
                Long careerGoalId;

                if (goalIdObj instanceof Number) {
                    // 如果是数字类型，直接转换
                    careerGoalId = ((Number) goalIdObj).longValue();
                } else {
                    // 如果是字符串类型，尝试解析为数字或使用映射
                    String goalIdStr = goalIdObj.toString();
                    try {
                        careerGoalId = Long.parseLong(goalIdStr);
                    } catch (NumberFormatException e) {
                        // 如果不是纯数字，使用字符串映射
                        careerGoalId = mapJobStringIdToLongId(goalIdStr);
                    }
                }

                careerGoal.setCareerGoalId(careerGoalId);
                log.info("设置职业目标ID: {}", careerGoalId);
            }

            // 处理目标级别
            if (request.containsKey("targetLevel")) {
                String levelStr = request.get("targetLevel").toString().toLowerCase();
                log.info("设置目标级别: {}", levelStr);

                try {
                    UserCareerGoal.TargetLevel targetLevel = UserCareerGoal.TargetLevel.valueOf(levelStr);
                    careerGoal.setTargetLevel(targetLevel);
                    log.info("目标级别设置成功: {}", targetLevel);
                } catch (IllegalArgumentException e) {
                    log.warn("无效的目标级别: {}, 使用默认值 junior", levelStr);
                    careerGoal.setTargetLevel(UserCareerGoal.TargetLevel.junior);
                }
            }
            
            if (request.containsKey("description")) {
                careerGoal.setDescription(request.get("description").toString());
            }
            
            if (request.containsKey("motivation")) {
                careerGoal.setMotivation(request.get("motivation").toString());
            }
            
            careerGoal.setSetAt(LocalDateTime.now());
            careerGoal.setIsActive(true);

            UserCareerGoal savedGoal = careerGoalRepository.save(careerGoal);
            log.info("用户职业目标设置成功: userId={}, goalId={}", userId, savedGoal.getId());
            
            return savedGoal;

        } catch (Exception e) {
            log.error("设置用户职业目标异常: userId={}", userId, e);
            throw new RuntimeException("设置用户职业目标失败", e);
        }
    }

    /**
     * 根据职业目标ID和级别代码查找CareerLevel ID
     *
     * @param careerGoalId 职业目标ID
     * @param levelCode 级别代码（如：junior, mid, senior）
     * @return CareerLevel ID，如果找不到返回null
     */
    private Long findCareerLevelId(Long careerGoalId, String levelCode) {
        if (careerGoalId == null || levelCode == null) {
            log.warn("findCareerLevelId参数为空: careerGoalId={}, levelCode={}", careerGoalId, levelCode);
            return null;
        }

        try {
            log.info("执行数据库查询: careerGoalId={}, levelCode={}", careerGoalId, levelCode);
            Optional<CareerLevel> careerLevelOpt = careerLevelRepository.findByCareerGoalIdAndLevelCode(careerGoalId, levelCode);

            if (careerLevelOpt.isPresent()) {
                CareerLevel careerLevel = careerLevelOpt.get();
                log.info("找到CareerLevel: id={}, name={}, isActive={}",
                        careerLevel.getId(), careerLevel.getLevelName(), careerLevel.getIsActive());
                return careerLevel.getId();
            } else {
                log.warn("数据库中未找到匹配的CareerLevel: careerGoalId={}, levelCode={}", careerGoalId, levelCode);
                return null;
            }
        } catch (Exception e) {
            log.error("查找CareerLevel异常: careerGoalId={}, levelCode={}", careerGoalId, levelCode, e);
            return null;
        }
    }

    /**
     * 更新学习进度
     * 
     * @param userId 用户ID
     * @param request 进度更新请求
     * @return 更新结果
     */
    public Map<String, Object> updateLearningProgress(Long userId, Map<String, Object> request) {
        log.info("更新用户学习进度: userId={}, request={}", userId, request);

        try {
            Map<String, Object> result = new HashMap<>();

            // 根据请求类型更新不同的进度
            String progressType = request.get("type").toString();
            
            switch (progressType) {
                case "step":
                    result = updateStepProgress(userId, request);
                    break;
                case "path":
                    result = updatePathProgress(userId, request);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的进度类型: " + progressType);
            }

            log.info("用户学习进度更新成功: userId={}, type={}", userId, progressType);
            return result;

        } catch (Exception e) {
            log.error("更新用户学习进度异常: userId={}", userId, e);
            throw new RuntimeException("更新用户学习进度失败", e);
        }
    }

    /**
     * 获取用户学习统计
     * 
     * @param userId 用户ID
     * @return 学习统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getUserLearningStatistics(Long userId) {
        log.info("获取用户学习统计: userId={}", userId);

        try {
            return calculateLearningStatistics(userId);
        } catch (Exception e) {
            log.error("获取用户学习统计异常: userId={}", userId, e);
            throw new RuntimeException("获取用户学习统计失败", e);
        }
    }

    /**
     * 获取用户成就信息
     * 
     * @param userId 用户ID
     * @return 成就信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getUserAchievements(Long userId) {
        log.info("获取用户成就: userId={}", userId);

        try {
            Map<String, Object> achievements = new HashMap<>();

            // 学习成就统计
            Map<String, Object> learningAchievements = new HashMap<>();
            
            // 完成的学习路径数量
            long completedPaths = pathProgressRepository.countByUserIdAndStatus(
                userId, UserLearningPathProgress.Status.COMPLETED);
            learningAchievements.put("completedPaths", completedPaths);
            
            // 完成的步骤数量
            long completedSteps = stepProgressRepository.countByUserIdAndStatus(
                userId, UserStepProgress.Status.COMPLETED);
            learningAchievements.put("completedSteps", completedSteps);
            
            // 总学习时间
            Long totalStudyMinutes = stepProgressRepository.calculateTotalStudiedMinutes(userId);
            learningAchievements.put("totalStudyHours", totalStudyMinutes != null ? totalStudyMinutes / 60 : 0);
            
            // 连续学习天数
            Integer streakDays = stepProgressRepository.findUserStreakDays(userId);
            learningAchievements.put("streakDays", streakDays != null ? streakDays : 0);
            
            achievements.put("learning", learningAchievements);

            // 技能成就
            List<SkillProgressCalculationService.SkillProgressView> skillProgresses = 
                skillProgressService.calculateUserSkillProgress(userId);
            Map<String, Object> skillAchievements = new HashMap<>();
            skillAchievements.put("totalSkills", skillProgresses.size());
            skillAchievements.put("masteredSkills", skillProgresses.stream()
                .mapToLong(skill -> skill.getOverallProgress().compareTo(BigDecimal.valueOf(80)) >= 0 ? 1 : 0)
                .sum());
            achievements.put("skills", skillAchievements);

            log.info("用户成就获取成功: userId={}", userId);
            return achievements;

        } catch (Exception e) {
            log.error("获取用户成就异常: userId={}", userId, e);
            throw new RuntimeException("获取用户成就失败", e);
        }
    }

    /**
     * 计算学习统计信息
     */
    private Map<String, Object> calculateLearningStatistics(Long userId) {
        Map<String, Object> stats = new HashMap<>();

        // 总学习时间
        Long totalMinutes = stepProgressRepository.calculateTotalStudiedMinutes(userId);
        stats.put("totalStudyMinutes", totalMinutes != null ? totalMinutes : 0);
        stats.put("totalStudyHours", totalMinutes != null ? totalMinutes / 60 : 0);

        // 平均完成率
        BigDecimal avgCompletion = stepProgressRepository.calculateAverageCompletionPercentage(userId);
        stats.put("averageCompletion", avgCompletion != null ? avgCompletion : BigDecimal.ZERO);

        // 学习路径统计
        long totalPaths = pathProgressRepository.countByUserId(userId);
        long completedPaths = pathProgressRepository.countByUserIdAndStatus(
            userId, UserLearningPathProgress.Status.COMPLETED);
        stats.put("totalPaths", totalPaths);
        stats.put("completedPaths", completedPaths);
        stats.put("pathCompletionRate", totalPaths > 0 ? 
            BigDecimal.valueOf(completedPaths * 100.0 / totalPaths).setScale(2, BigDecimal.ROUND_HALF_UP) : 
            BigDecimal.ZERO);

        // 步骤统计
        long totalSteps = stepProgressRepository.countByUserId(userId);
        long completedSteps = stepProgressRepository.countByUserIdAndStatus(
            userId, UserStepProgress.Status.COMPLETED);
        stats.put("totalSteps", totalSteps);
        stats.put("completedSteps", completedSteps);
        stats.put("stepCompletionRate", totalSteps > 0 ? 
            BigDecimal.valueOf(completedSteps * 100.0 / totalSteps).setScale(2, BigDecimal.ROUND_HALF_UP) : 
            BigDecimal.ZERO);

        return stats;
    }

    /**
     * 更新步骤进度
     */
    private Map<String, Object> updateStepProgress(Long userId, Map<String, Object> request) {
        Long stepId = Long.valueOf(request.get("stepId").toString());

        UserStepProgress stepProgress = stepProgressRepository.findByUserIdAndStepId(userId, stepId)
            .orElse(new UserStepProgress(userId, stepId));

        // 更新进度信息
        if (request.containsKey("completionPercentage")) {
            BigDecimal completion = new BigDecimal(request.get("completionPercentage").toString());
            stepProgress.setCompletionPercentage(completion);
        }

        if (request.containsKey("studiedMinutes")) {
            Integer minutes = Integer.valueOf(request.get("studiedMinutes").toString());
            stepProgress.setStudiedMinutes(stepProgress.getStudiedMinutes() + minutes);
        }

        if (request.containsKey("status")) {
            String statusStr = request.get("status").toString().toUpperCase();
            UserStepProgress.Status status = UserStepProgress.Status.valueOf(statusStr);
            stepProgress.setStatus(status);

            if (status == UserStepProgress.Status.COMPLETED) {
                stepProgress.setCompletedAt(LocalDateTime.now());
            }
        }

        stepProgress.setLastStudiedAt(LocalDateTime.now());
        stepProgressRepository.save(stepProgress);

        Map<String, Object> result = new HashMap<>();
        result.put("stepProgress", stepProgress);
        return result;
    }

    /**
     * 更新路径进度
     */
    private Map<String, Object> updatePathProgress(Long userId, Map<String, Object> request) {
        Long pathId = Long.valueOf(request.get("pathId").toString());

        Optional<UserLearningPathProgress> progressOpt =
            pathProgressRepository.findByUserIdAndLearningPathId(userId, pathId);

        if (!progressOpt.isPresent()) {
            throw new RuntimeException("未找到学习路径进度记录");
        }

        UserLearningPathProgress pathProgress = progressOpt.get();

        // 更新进度信息
        if (request.containsKey("completionPercentage")) {
            BigDecimal completion = new BigDecimal(request.get("completionPercentage").toString());
            pathProgress.setCompletionPercentage(completion);
        }

        if (request.containsKey("studiedMinutes")) {
            Integer minutes = Integer.valueOf(request.get("studiedMinutes").toString());
            pathProgress.setStudiedMinutes(pathProgress.getStudiedMinutes() + minutes);
        }

        if (request.containsKey("status")) {
            String statusStr = request.get("status").toString().toUpperCase();
            UserLearningPathProgress.Status status = UserLearningPathProgress.Status.valueOf(statusStr);
            pathProgress.setStatus(status);
        }

        pathProgressRepository.save(pathProgress);

        Map<String, Object> result = new HashMap<>();
        result.put("pathProgress", pathProgress);
        return result;
    }

    /**
     * 将字符串岗位ID映射为数字ID
     * 这是一个临时解决方案，用于处理前端字符串ID和后端数字ID的不一致问题
     *
     * @param jobIdStr 字符串岗位ID（如"java-backend-engineer"）
     * @return 对应的数字ID
     */
    private Long mapJobStringIdToLongId(String jobIdStr) {
        // 根据前端jobProfiles.ts中的定义，创建映射关系
        switch (jobIdStr) {
            case "java-backend-engineer":
                return 2L; // 对应数据库中的Java后端开发工程师
            case "react-frontend-engineer":
                return 3L; // 对应数据库中的前端开发工程师
            case "fullstack-engineer":
                return 4L; // 对应数据库中的全栈开发工程师
            case "data-analyst":
                return 5L; // 对应数据库中的数据分析师
            case "product-manager":
                return 6L; // 对应数据库中的产品经理
            default:
                log.warn("未知的岗位ID: {}, 使用默认值2", jobIdStr);
                return 2L; // 默认返回Java后端开发工程师
        }
    }

    /**
     * 初始化技能进度测试数据
     * 执行skill-progress-test-data.sql文件中的SQL语句
     */
    @Transactional
    public void initSkillProgressTestData() {
        log.info("开始执行技能进度测试数据初始化");

        try {
            // 读取SQL文件内容
            ClassPathResource resource = new ClassPathResource("db/skill-progress-test-data.sql");
            String sqlContent = FileCopyUtils.copyToString(
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));

            // 分割SQL语句（按分号分割）
            String[] sqlStatements = sqlContent.split(";");

            int executedCount = 0;
            for (String sql : sqlStatements) {
                String trimmedSql = sql.trim();
                // 跳过空语句和注释
                if (!trimmedSql.isEmpty() && !trimmedSql.startsWith("--") && !trimmedSql.startsWith("/*")) {
                    try {
                        jdbcTemplate.execute(trimmedSql);
                        executedCount++;
                        log.debug("执行SQL语句: {}", trimmedSql.substring(0, Math.min(50, trimmedSql.length())));
                    } catch (Exception e) {
                        log.warn("执行SQL语句失败: {}, 错误: {}", trimmedSql.substring(0, Math.min(50, trimmedSql.length())), e.getMessage());
                    }
                }
            }

            log.info("技能进度测试数据初始化完成，共执行 {} 条SQL语句", executedCount);

        } catch (IOException e) {
            log.error("读取SQL文件失败", e);
            throw new RuntimeException("读取SQL文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("执行SQL语句失败", e);
            throw new RuntimeException("执行SQL语句失败: " + e.getMessage());
        }
    }
}