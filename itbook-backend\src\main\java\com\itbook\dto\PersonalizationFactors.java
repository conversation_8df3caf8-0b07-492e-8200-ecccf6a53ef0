package com.itbook.dto;

import java.util.List;
import java.util.Map;

/**
 * 个性化因子DTO
 * 用于动态学习路径生成的个性化参数
 */
public class PersonalizationFactors {
    // 学习偏好
    private String learningStyle; // VISUAL, AUDITORY, KINESTHETIC, READING
    private String preferredPace; // SLOW, NORMAL, FAST
    private Integer dailyLearningHours;
    private List<String> preferredTimeSlots;
    private String difficultyPreference; // GRADUAL, MODERATE, CHALLENGING
    
    // 技能背景
    private List<String> existingSkills;
    private Map<String, String> skillLevels; // skillId -> level
    private String experienceLevel; // BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
    private List<String> industryBackground;
    
    // 目标设定
    private List<Long> targetSkillIds;
    private String targetTimeframe; // 目标完成时间框架
    private String careerGoal;
    private String priorityFocus; // BREADTH, DEPTH, BALANCED
    
    // 学习约束
    private Double availableHoursPerWeek;
    private List<String> unavailableDays;
    private String budgetConstraint; // FREE, LOW, MEDIUM, HIGH
    private List<String> resourcePreferences; // VIDEO, TEXT, INTERACTIVE, PROJECT
    
    // 动机因素
    private String motivationType; // ACHIEVEMENT, SOCIAL, PERSONAL, CAREER
    private Boolean needsGamification;
    private Boolean prefersCommunityLearning;
    private String feedbackFrequency; // IMMEDIATE, DAILY, WEEKLY
    
    // 适应性参数
    private Double adaptabilityScore; // 0.0-1.0
    private String learningAgility; // LOW, MEDIUM, HIGH
    private Boolean allowsExperimentation;
    private Double riskTolerance; // 0.0-1.0
    
    // 上下文信息
    private String currentRole;
    private String targetRole;
    private String companySize;
    private String industryType;
    private Map<String, Object> customFactors;

    public PersonalizationFactors() {}

    // Getters and Setters
    public String getLearningStyle() { return learningStyle; }
    public void setLearningStyle(String learningStyle) { this.learningStyle = learningStyle; }

    public String getPreferredPace() { return preferredPace; }
    public void setPreferredPace(String preferredPace) { this.preferredPace = preferredPace; }

    public Integer getDailyLearningHours() { return dailyLearningHours; }
    public void setDailyLearningHours(Integer dailyLearningHours) { this.dailyLearningHours = dailyLearningHours; }

    public List<String> getPreferredTimeSlots() { return preferredTimeSlots; }
    public void setPreferredTimeSlots(List<String> preferredTimeSlots) { this.preferredTimeSlots = preferredTimeSlots; }

    public String getDifficultyPreference() { return difficultyPreference; }
    public void setDifficultyPreference(String difficultyPreference) { this.difficultyPreference = difficultyPreference; }

    public List<String> getExistingSkills() { return existingSkills; }
    public void setExistingSkills(List<String> existingSkills) { this.existingSkills = existingSkills; }

    public Map<String, String> getSkillLevels() { return skillLevels; }
    public void setSkillLevels(Map<String, String> skillLevels) { this.skillLevels = skillLevels; }

    public String getExperienceLevel() { return experienceLevel; }
    public void setExperienceLevel(String experienceLevel) { this.experienceLevel = experienceLevel; }

    public List<String> getIndustryBackground() { return industryBackground; }
    public void setIndustryBackground(List<String> industryBackground) { this.industryBackground = industryBackground; }

    public List<Long> getTargetSkillIds() { return targetSkillIds; }
    public void setTargetSkillIds(List<Long> targetSkillIds) { this.targetSkillIds = targetSkillIds; }

    public String getTargetTimeframe() { return targetTimeframe; }
    public void setTargetTimeframe(String targetTimeframe) { this.targetTimeframe = targetTimeframe; }

    public String getCareerGoal() { return careerGoal; }
    public void setCareerGoal(String careerGoal) { this.careerGoal = careerGoal; }

    public String getPriorityFocus() { return priorityFocus; }
    public void setPriorityFocus(String priorityFocus) { this.priorityFocus = priorityFocus; }

    public Double getAvailableHoursPerWeek() { return availableHoursPerWeek; }
    public void setAvailableHoursPerWeek(Double availableHoursPerWeek) { this.availableHoursPerWeek = availableHoursPerWeek; }

    public List<String> getUnavailableDays() { return unavailableDays; }
    public void setUnavailableDays(List<String> unavailableDays) { this.unavailableDays = unavailableDays; }

    public String getBudgetConstraint() { return budgetConstraint; }
    public void setBudgetConstraint(String budgetConstraint) { this.budgetConstraint = budgetConstraint; }

    public List<String> getResourcePreferences() { return resourcePreferences; }
    public void setResourcePreferences(List<String> resourcePreferences) { this.resourcePreferences = resourcePreferences; }

    public String getMotivationType() { return motivationType; }
    public void setMotivationType(String motivationType) { this.motivationType = motivationType; }

    public Boolean getNeedsGamification() { return needsGamification; }
    public void setNeedsGamification(Boolean needsGamification) { this.needsGamification = needsGamification; }

    public Boolean getPrefersCommunityLearning() { return prefersCommunityLearning; }
    public void setPrefersCommunityLearning(Boolean prefersCommunityLearning) { this.prefersCommunityLearning = prefersCommunityLearning; }

    public String getFeedbackFrequency() { return feedbackFrequency; }
    public void setFeedbackFrequency(String feedbackFrequency) { this.feedbackFrequency = feedbackFrequency; }

    public Double getAdaptabilityScore() { return adaptabilityScore; }
    public void setAdaptabilityScore(Double adaptabilityScore) { this.adaptabilityScore = adaptabilityScore; }

    public String getLearningAgility() { return learningAgility; }
    public void setLearningAgility(String learningAgility) { this.learningAgility = learningAgility; }

    public Boolean getAllowsExperimentation() { return allowsExperimentation; }
    public void setAllowsExperimentation(Boolean allowsExperimentation) { this.allowsExperimentation = allowsExperimentation; }

    public Double getRiskTolerance() { return riskTolerance; }
    public void setRiskTolerance(Double riskTolerance) { this.riskTolerance = riskTolerance; }

    public String getCurrentRole() { return currentRole; }
    public void setCurrentRole(String currentRole) { this.currentRole = currentRole; }

    public String getTargetRole() { return targetRole; }
    public void setTargetRole(String targetRole) { this.targetRole = targetRole; }

    public String getCompanySize() { return companySize; }
    public void setCompanySize(String companySize) { this.companySize = companySize; }

    public String getIndustryType() { return industryType; }
    public void setIndustryType(String industryType) { this.industryType = industryType; }

    public Map<String, Object> getCustomFactors() { return customFactors; }
    public void setCustomFactors(Map<String, Object> customFactors) { this.customFactors = customFactors; }
}
