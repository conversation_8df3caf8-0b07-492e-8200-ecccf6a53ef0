-- ========================================
-- ITBook 个性化路径表结构迁移脚本
-- 将 path_recommendation 表重构为 personalized_path 表
-- 执行时间：2025-07-11
-- ========================================

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 检查旧表是否存在，如果存在则备份数据
DROP TABLE IF EXISTS `path_recommendation_backup`;
CREATE TABLE IF NOT EXISTS `path_recommendation_backup` AS 
SELECT * FROM `path_recommendation` WHERE 1=0;

-- 备份现有数据（如果表存在）
INSERT IGNORE INTO `path_recommendation_backup` 
SELECT * FROM `path_recommendation` WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'path_recommendation');

-- 2. 删除旧表
DROP TABLE IF EXISTS `path_recommendation`;

-- 3. 创建新的个性化路径表
DROP TABLE IF EXISTS `personalized_path`;
CREATE TABLE `personalized_path` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `job_id` bigint DEFAULT NULL COMMENT '目标岗位ID（可选）',
  `personalized_path_id` bigint NOT NULL COMMENT '个性化学习路径ID',
  `personalization_score` decimal(5,3) NOT NULL COMMENT '个性化匹配分数',
  `personalization_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '个性化理由',
  `algorithm_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '算法版本',
  `personalization_factors` json DEFAULT NULL COMMENT '个性化因子详情',
  `is_accepted` bit(1) DEFAULT NULL COMMENT '用户是否接受个性化路径',
  `feedback_rating` int DEFAULT NULL COMMENT '用户反馈评分(1-5)',
  `user_feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户反馈文本',
  `created_at` datetime(6) NOT NULL COMMENT '创建时间',
  `updated_at` datetime(6) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_personalized_path_id` (`personalized_path_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_personalized_path_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_personalized_path_job` FOREIGN KEY (`job_id`) REFERENCES `job` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_personalized_path_learning_path` FOREIGN KEY (`personalized_path_id`) REFERENCES `learning_path` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个性化学习路径记录表';

-- 4. 数据迁移（如果有备份数据）
INSERT INTO `personalized_path` (
  `user_id`,
  `job_id`, 
  `personalized_path_id`,
  `personalization_score`,
  `personalization_reason`,
  `algorithm_version`,
  `personalization_factors`,
  `is_accepted`,
  `feedback_rating`,
  `user_feedback`,
  `created_at`,
  `updated_at`
)
SELECT 
  `user_id`,
  `job_id`,
  `recommended_path_id` as `personalized_path_id`,
  `recommendation_score` as `personalization_score`,
  `recommendation_reason` as `personalization_reason`,
  `algorithm_version`,
  `recommendation_factors` as `personalization_factors`,
  `is_accepted`,
  `feedback_rating`,
  `user_feedback`,
  `created_at`,
  `updated_at`
FROM `path_recommendation_backup`
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'path_recommendation_backup');

-- 5. 更新 recommendation_feedback 表的外键引用
-- 首先删除旧的外键约束
ALTER TABLE `recommendation_feedback` DROP FOREIGN KEY IF EXISTS `fk_recommendation_feedback_recommendation`;

-- 添加新的外键约束
ALTER TABLE `recommendation_feedback` 
ADD CONSTRAINT `fk_recommendation_feedback_personalized_path` 
FOREIGN KEY (`recommendation_id`) REFERENCES `personalized_path` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT;

-- 6. 插入一些测试数据
INSERT INTO `personalized_path` (
  `user_id`,
  `job_id`,
  `personalized_path_id`,
  `personalization_score`,
  `personalization_reason`,
  `algorithm_version`,
  `personalization_factors`,
  `is_accepted`,
  `feedback_rating`,
  `user_feedback`,
  `created_at`,
  `updated_at`
) VALUES 
(1, 1, 1, 0.920, '基于你的编程基础和学习偏好，这条路径最适合你快速进入前端开发领域', 'v1.0', 
 '{"skills": ["JavaScript", "React"], "experience": "intermediate", "goal": "frontend"}', 
 NULL, NULL, NULL, NOW(6), NOW(6)),
(1, 2, 2, 0.850, '你的逻辑思维能力强，适合学习后端开发的系统性知识', 'v1.0',
 '{"skills": ["Java", "Spring"], "experience": "beginner", "goal": "backend"}',
 NULL, NULL, NULL, NOW(6), NOW(6)),
(1, 3, 3, 0.780, '数据分析入门门槛较低，适合你的学习时间安排', 'v1.0',
 '{"skills": ["Python"], "experience": "beginner", "goal": "data_analysis"}',
 NULL, NULL, NULL, NOW(6), NOW(6));

-- 7. 清理备份表（可选，生产环境建议保留一段时间）
-- DROP TABLE IF EXISTS `path_recommendation_backup`;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证数据
SELECT COUNT(*) as personalized_path_count FROM `personalized_path`;
SELECT * FROM `personalized_path` LIMIT 5;

COMMIT;
