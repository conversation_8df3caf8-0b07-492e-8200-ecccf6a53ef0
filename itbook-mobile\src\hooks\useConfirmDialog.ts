/**
 * 确认对话框Hook
 * 
 * 功能：
 * - 提供简单易用的确认对话框API
 * - 自动管理对话框状态
 * - 支持Promise风格的异步操作
 * - 跨平台兼容
 * 
 * <AUTHOR> Team
 * @since 2025-07-18
 */

import { useState, useCallback } from 'react';
import { Alert, Platform } from 'react-native';

export interface ConfirmOptions {
  /** 对话框标题 */
  title: string;
  /** 对话框内容 */
  message: string;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮样式类型 */
  confirmType?: 'default' | 'destructive' | 'primary';
  /** 是否显示警告图标（仅Web） */
  showWarningIcon?: boolean;
}

export interface UseConfirmDialogReturn {
  /** 是否显示对话框 */
  isVisible: boolean;
  /** 是否处于加载状态 */
  isLoading: boolean;
  /** 当前对话框配置 */
  dialogConfig: ConfirmOptions | null;
  /** 显示确认对话框 */
  showConfirm: (options: ConfirmOptions) => Promise<boolean>;
  /** 显示确认对话框（异步操作版本） */
  showConfirmAsync: (options: ConfirmOptions, asyncAction: () => Promise<void>) => Promise<boolean>;
  /** 手动关闭对话框 */
  hideDialog: () => void;
  /** 确认回调 */
  handleConfirm: () => void;
  /** 取消回调 */
  handleCancel: () => void;
}

/**
 * 确认对话框Hook
 */
export const useConfirmDialog = (): UseConfirmDialogReturn => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [dialogConfig, setDialogConfig] = useState<ConfirmOptions | null>(null);
  const [resolvePromise, setResolvePromise] = useState<((value: boolean) => void) | null>(null);
  const [asyncAction, setAsyncAction] = useState<(() => Promise<void>) | null>(null);

  /**
   * 显示确认对话框
   */
  const showConfirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      if (Platform.OS === 'web') {
        // Web环境使用自定义对话框
        setDialogConfig(options);
        setIsVisible(true);
        setResolvePromise(() => resolve);
      } else {
        // 移动端使用原生Alert
        Alert.alert(
          options.title,
          options.message,
          [
            {
              text: options.cancelText || '取消',
              style: 'cancel',
              onPress: () => resolve(false),
            },
            {
              text: options.confirmText || '确定',
              style: options.confirmType === 'destructive' ? 'destructive' : 'default',
              onPress: () => resolve(true),
            },
          ]
        );
      }
    });
  }, []);

  /**
   * 显示确认对话框（异步操作版本）
   */
  const showConfirmAsync = useCallback(
    (options: ConfirmOptions, action: () => Promise<void>): Promise<boolean> => {
      return new Promise((resolve) => {
        if (Platform.OS === 'web') {
          // Web环境使用自定义对话框
          setDialogConfig(options);
          setIsVisible(true);
          setAsyncAction(() => action);
          setResolvePromise(() => resolve);
        } else {
          // 移动端使用原生Alert
          Alert.alert(
            options.title,
            options.message,
            [
              {
                text: options.cancelText || '取消',
                style: 'cancel',
                onPress: () => resolve(false),
              },
              {
                text: options.confirmText || '确定',
                style: options.confirmType === 'destructive' ? 'destructive' : 'default',
                onPress: async () => {
                  try {
                    await action();
                    resolve(true);
                  } catch (error) {
                    console.error('异步操作失败:', error);
                    resolve(false);
                  }
                },
              },
            ]
          );
        }
      });
    },
    []
  );

  /**
   * 处理确认
   */
  const handleConfirm = useCallback(async () => {
    if (asyncAction) {
      // 异步操作版本
      setIsLoading(true);
      try {
        await asyncAction();
        setIsVisible(false);
        setIsLoading(false);
        setAsyncAction(null);
        resolvePromise?.(true);
      } catch (error) {
        console.error('异步操作失败:', error);
        setIsLoading(false);
        resolvePromise?.(false);
      }
    } else {
      // 普通版本
      setIsVisible(false);
      resolvePromise?.(true);
    }
    
    setResolvePromise(null);
  }, [asyncAction, resolvePromise]);

  /**
   * 处理取消
   */
  const handleCancel = useCallback(() => {
    setIsVisible(false);
    setIsLoading(false);
    setAsyncAction(null);
    resolvePromise?.(false);
    setResolvePromise(null);
  }, [resolvePromise]);

  /**
   * 手动关闭对话框
   */
  const hideDialog = useCallback(() => {
    setIsVisible(false);
    setIsLoading(false);
    setAsyncAction(null);
    setResolvePromise(null);
  }, []);

  return {
    isVisible,
    isLoading,
    dialogConfig,
    showConfirm,
    showConfirmAsync,
    hideDialog,
    handleConfirm,
    handleCancel,
  };
};

export default useConfirmDialog;
