package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 技能评估记录实体
 * 存储技能评估的详细记录，用于跟踪用户技能掌握情况
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Entity
@Table(name = "skill_assessment_record")
public class SkillAssessmentRecord {

    /**
     * 评估类型枚举
     */
    public enum AssessmentType {
        QUIZ("在线测试"),
        PROJECT("项目实战"),
        PRACTICE("实践练习"),
        PEER_REVIEW("同行评议"),
        SELF_ASSESSMENT("自我评估");
        
        private final String description;
        
        AssessmentType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 通过状态枚举
     */
    public enum PassStatus {
        PASS("通过"),
        FAIL("未通过"),
        PARTIAL("部分通过");
        
        private final String description;
        
        PassStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 评估者类型枚举
     */
    public enum AssessorType {
        SYSTEM("系统"),
        PEER("同行"),
        INSTRUCTOR("导师"),
        SELF("自评");
        
        private final String description;
        
        AssessorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 用户（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    @JsonIgnore
    private User user;

    /**
     * 原子技能ID
     */
    @NotNull(message = "原子技能ID不能为空")
    @Column(name = "atomic_skill_id", nullable = false)
    private Long atomicSkillId;

    /**
     * 原子技能（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "atomic_skill_id", insertable = false, updatable = false)
    @JsonIgnore
    private AtomicSkill atomicSkill;

    /**
     * 评估类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "assessment_type", nullable = false)
    private AssessmentType assessmentType;

    /**
     * 评估分数
     */
    @NotNull(message = "评估分数不能为空")
    @DecimalMin(value = "0.00", message = "评估分数不能小于0")
    @Column(name = "score", precision = 5, scale = 2, nullable = false)
    private BigDecimal score;

    /**
     * 最高分数
     */
    @NotNull(message = "最高分数不能为空")
    @DecimalMin(value = "0.00", message = "最高分数不能小于0")
    @Column(name = "max_score", precision = 5, scale = 2, nullable = false)
    private BigDecimal maxScore = new BigDecimal("100.00");

    /**
     * 通过状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "pass_status", nullable = false)
    private PassStatus passStatus;

    /**
     * 达到的掌握水平
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "mastery_level_achieved")
    private UserAtomicSkillMastery.MasteryLevel masteryLevelAchieved;

    /**
     * 评估详细数据（JSON格式）
     */
    @Column(name = "assessment_data", columnDefinition = "JSON")
    private String assessmentDataJson;

    /**
     * 评估反馈
     */
    @Column(name = "feedback", columnDefinition = "TEXT")
    private String feedback;

    /**
     * 改进建议（JSON格式）
     */
    @Column(name = "improvement_suggestions", columnDefinition = "JSON")
    private String improvementSuggestionsJson;

    /**
     * 评估上下文（JSON格式）
     */
    @Column(name = "assessment_context", columnDefinition = "JSON")
    private String assessmentContextJson;

    /**
     * 评估用时（分钟）
     */
    @Min(value = 0, message = "评估用时不能为负数")
    @Column(name = "time_spent_minutes")
    private Integer timeSpentMinutes;

    /**
     * 尝试次数
     */
    @Min(value = 1, message = "尝试次数不能小于1")
    @Column(name = "attempt_number")
    private Integer attemptNumber = 1;

    /**
     * 评估者类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "assessor_type")
    private AssessorType assessorType = AssessorType.SYSTEM;

    /**
     * 评估者ID
     */
    @Column(name = "assessor_id")
    private Long assessorId;

    /**
     * 创建时间（评估时间）
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 构造函数
    public SkillAssessmentRecord() {}

    public SkillAssessmentRecord(Long userId, Long atomicSkillId, AssessmentType assessmentType) {
        this.userId = userId;
        this.atomicSkillId = atomicSkillId;
        this.assessmentType = assessmentType;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }

    public Long getAtomicSkillId() { return atomicSkillId; }
    public void setAtomicSkillId(Long atomicSkillId) { this.atomicSkillId = atomicSkillId; }

    public AtomicSkill getAtomicSkill() { return atomicSkill; }
    public void setAtomicSkill(AtomicSkill atomicSkill) { this.atomicSkill = atomicSkill; }

    public AssessmentType getAssessmentType() { return assessmentType; }
    public void setAssessmentType(AssessmentType assessmentType) { this.assessmentType = assessmentType; }

    public BigDecimal getScore() { return score; }
    public void setScore(BigDecimal score) { this.score = score; }

    public BigDecimal getMaxScore() { return maxScore; }
    public void setMaxScore(BigDecimal maxScore) { this.maxScore = maxScore; }

    public PassStatus getPassStatus() { return passStatus; }
    public void setPassStatus(PassStatus passStatus) { this.passStatus = passStatus; }

    public UserAtomicSkillMastery.MasteryLevel getMasteryLevelAchieved() { return masteryLevelAchieved; }
    public void setMasteryLevelAchieved(UserAtomicSkillMastery.MasteryLevel masteryLevelAchieved) { this.masteryLevelAchieved = masteryLevelAchieved; }

    public String getAssessmentDataJson() { return assessmentDataJson; }
    public void setAssessmentDataJson(String assessmentDataJson) { this.assessmentDataJson = assessmentDataJson; }

    public String getFeedback() { return feedback; }
    public void setFeedback(String feedback) { this.feedback = feedback; }

    public String getImprovementSuggestionsJson() { return improvementSuggestionsJson; }
    public void setImprovementSuggestionsJson(String improvementSuggestionsJson) { this.improvementSuggestionsJson = improvementSuggestionsJson; }

    public String getAssessmentContextJson() { return assessmentContextJson; }
    public void setAssessmentContextJson(String assessmentContextJson) { this.assessmentContextJson = assessmentContextJson; }

    public Integer getTimeSpentMinutes() { return timeSpentMinutes; }
    public void setTimeSpentMinutes(Integer timeSpentMinutes) { this.timeSpentMinutes = timeSpentMinutes; }

    public Integer getAttemptNumber() { return attemptNumber; }
    public void setAttemptNumber(Integer attemptNumber) { this.attemptNumber = attemptNumber; }

    public AssessorType getAssessorType() { return assessorType; }
    public void setAssessorType(AssessorType assessorType) { this.assessorType = assessorType; }

    public Long getAssessorId() { return assessorId; }
    public void setAssessorId(Long assessorId) { this.assessorId = assessorId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    /**
     * 计算得分率
     */
    public BigDecimal getScoreRate() {
        if (maxScore == null || maxScore.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return score.divide(maxScore, 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 判断是否通过
     */
    public boolean isPassed() {
        return passStatus == PassStatus.PASS;
    }

    @Override
    public String toString() {
        return "SkillAssessmentRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", atomicSkillId=" + atomicSkillId +
                ", assessmentType=" + assessmentType +
                ", score=" + score +
                ", maxScore=" + maxScore +
                ", passStatus=" + passStatus +
                ", masteryLevelAchieved=" + masteryLevelAchieved +
                ", attemptNumber=" + attemptNumber +
                '}';
    }
}
