import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';

import { RootStackParamList } from './types';
import { MainTabNavigator } from './MainTabNavigator';
import { AuthStackNavigator } from './AuthStackNavigator';
import { useAuth } from '../hooks/useAuth';

// 直接导入所有屏幕组件（暂时禁用懒加载以修复问题）
import { ModalScreen } from '../screens/ModalScreen';
import { SettingsScreen } from '../screens/SettingsScreen';
import { ComponentDemoScreen } from '../screens/ComponentDemoScreen';
import { ArticleDetailScreen } from '../screens/discover/ArticleDetailScreen';
import { QuestionDetailScreen } from '../screens/QuestionDetailScreen';
import { NewsDetailScreen } from '../screens/discover/NewsDetailScreen';
import { JobDetailScreen } from '../screens/jobs/JobDetailScreen';
import { CompanyDetailScreen } from '../screens/jobs/CompanyDetailScreen';
import ResumeCreateScreen from '../screens/jobs/ResumeCreateScreen';
import ResumeDetailScreen from '../screens/jobs/ResumeDetailScreen';

// 学习相关页面
import { LearningPlanScreen } from '../screens/learning/LearningPlanScreen';
// import { NotesScreen } from '../screens/learning/NotesScreen'; // 已删除，学习笔记功能已移除
import { AchievementsScreen } from '../screens/profile/AchievementsScreen';
import { EditProfileScreen } from '../screens/profile/EditProfileScreen';
import { LearningDashboard } from '../components/dashboard/LearningDashboard';
import { CodeEditorScreen } from '../screens/learning/CodeEditorScreen';
import { LearningPathDetailScreen } from '../screens/learning/LearningPathDetailScreen';
import { StandardPathScreen } from '../screens/learning/StandardPathScreen';
import { PersonalizedPathScreen } from '../screens/learning/PersonalizedPathScreen';

// 原子技能相关页面
import { AtomicSkillListScreen } from '../screens/learning/AtomicSkillListScreen';
import { AtomicSkillDetailScreen } from '../screens/learning/AtomicSkillDetailScreen';

// 技能图谱可视化页面
import SkillGraphScreen from '../screens/visualization/SkillGraphScreen';

// 动态学习路径生成页面
import DynamicLearningPathScreen from '../screens/learning/DynamicLearningPathScreen';

// 前置技能分析页面
import PrerequisiteAnalysisScreen from '../screens/PrerequisiteAnalysisScreen';

// 面试题练习页面
import { InterviewPracticeScreen } from '../screens/interview/InterviewPracticeScreen';
import { QuestionListScreen } from '../screens/interview/QuestionListScreen';
import { QuestionDetailScreen as InterviewQuestionDetailScreen } from '../screens/interview/QuestionDetailScreen';

// 学习打卡页面
import { StudyCheckInScreen } from '../screens/study/StudyCheckInScreen';
import { StudyTimerScreen } from '../screens/study/StudyTimerScreen';

// 技能评估页面
import { SkillAssessmentScreen } from '../screens/assessment/SkillAssessmentScreen';
import { TestConfigurationScreen } from '../screens/assessment/TestConfigurationScreen';
import { TestExecutionScreen } from '../screens/assessment/TestExecutionScreen';

// 项目作品展示页面
import { ProjectShowcaseScreen } from '../screens/portfolio/ProjectShowcaseScreen';
import { ProjectDetailScreen } from '../screens/portfolio/ProjectDetailScreen';
import { MyPortfolioScreen } from '../screens/portfolio/MyPortfolioScreen';

// 社区功能页面
import { UserProfileScreen } from '../screens/community/UserProfileScreen';

// 用户画像页面
import { UserProfileSetupScreen } from '../screens/profile/UserProfileSetupScreen';
import { UserSkillAssessmentScreen } from '../screens/profile/UserSkillAssessmentScreen';
import { UserProfileManagementScreen } from '../screens/profile/UserProfileManagementScreen';
import { PersonalizedAssessmentScreen } from '../screens/profile/PersonalizedAssessmentScreen';
import UserProfileAnalysisScreen from '../screens/profile/UserProfileAnalysisScreen';

// 成就系统页面
import { AchievementCenterScreen } from '../screens/achievement/AchievementCenterScreen';

// 设置页面
import { ThemeSettingsScreen } from '../screens/settings/ThemeSettingsScreen';

// 离线管理页面
// import OfflineManagerScreen from '../screens/OfflineManagerScreen'; // 已删除，离线功能已移除

// 测试页面
import { LearningPathTestScreen } from '../screens/LearningPathTestScreen';

// 职业目标相关页面
import CareerGoalSelectionScreen from '../screens/CareerGoalSelectionScreen';
import CareerManagementScreen from '../screens/CareerManagementScreen';
import CareerGoalScreen from '../screens/CareerGoalScreen';

// 第三阶段新增页面 - 职业目标专属功能
import ProjectWorkshopScreen from '../screens/project/ProjectWorkshopScreen';
import InterviewPreparationScreen from '../screens/interview/InterviewPreparationScreen';
import PortfolioGenerationScreen from '../screens/portfolio/PortfolioGenerationScreen';
import JobRecommendationsScreen from '../screens/jobs/JobRecommendationsScreen';
import JobMatchAnalysisScreen from '../screens/jobs/JobMatchAnalysisScreen';

// 市场分析页面
import { MarketAnalysisScreen } from '../screens/MarketAnalysisScreen';

// 动态调整页面
import { AnalysisReportScreen } from '../screens/dynamic-adjustment/AnalysisReportScreen';

// 暂时创建占位组件，后续实现具体页面
const ProjectWorkshopDetailScreen = () => null;
const InterviewQuestionPracticeScreen = () => null;
const PortfolioPreviewScreen = () => null;
const CollaborativeCodingScreen = () => null;

const Stack = createNativeStackNavigator<RootStackParamList>();

// Web导航配置
const linking = Platform.OS === 'web' ? {
  prefixes: ['http://localhost:8082', 'https://itbook.app'],
  config: {
    screens: {
      Main: {
        screens: {
          Home: '',
          Study: 'study',
          Discover: 'discover',
          Jobs: 'jobs',
          Profile: 'profile',
        },
      },
      Auth: {
        screens: {
          Login: 'login',
          Register: 'register',
          ForgotPassword: 'forgot-password',
        },
      },
      Settings: 'settings',
      Modal: 'modal',
      ComponentDemo: 'demo',
      ArticleDetail: 'article/:articleId',
      QuestionDetail: 'question/:questionId',
      NewsDetail: 'news/:newsId',
      JobDetail: 'job/:jobId',
      CompanyDetail: 'company/:companyId?/:companyName?',
      ResumeCreate: 'resume/create',
      ResumeDetail: 'resume/:resumeId',

      // 学习相关页面
      LearningPlan: 'learning/plan',
      Notes: 'learning/notes',
      Achievements: 'profile/achievements',
      UserProfileSetup: 'profile/setup',
      UserSkillAssessment: 'profile/skill-assessment',
      UserProfileManagement: 'profile/management',
      PersonalizedAssessment: 'profile/assessment',
      LearningDashboard: 'learning/dashboard',
      CodeEditor: 'learning/code-editor',
      LearningPathDetail: 'learning/path/:pathId?',
      StandardPath: 'learning/standard-paths',
      StandardPathDetail: 'learning/standard-path/:jobId',
      PersonalizedPath: 'learning/personalized-paths',
      Recommendation: 'learning/recommendations', // 向后兼容

      // 原子技能相关页面
      AtomicSkillList: 'learning/atomic-skills',
      AtomicSkillDetail: 'learning/atomic-skill/:skillId',

      // 前置技能分析页面
      PrerequisiteAnalysis: 'learning/prerequisite-analysis/:skillId',

      // 技能图谱可视化页面
      SkillGraph: 'learning/skill-graph',

      // 面试题练习页面
      InterviewPractice: 'interview/practice',
      QuestionList: 'interview/questions',
      InterviewQuestionDetail: 'interview/question',
      InterviewStats: 'interview/stats',

      // 学习打卡页面
      StudyCheckIn: 'study/checkin',
      StudyTimer: 'study/timer',
      StudyCalendar: 'study/calendar',
      StudyStats: 'study/stats',
      GoalManagement: 'study/goals',
      HabitManagement: 'study/habits',
      StudyReport: 'study/report',

      // 技能评估页面
      SkillAssessment: 'assessment/home',
      TestConfiguration: 'assessment/config',
      TestExecution: 'assessment/test',
      AssessmentResult: 'assessment/result',
      AssessmentHistory: 'assessment/history',
      AssessmentStats: 'assessment/stats',

      // 项目作品展示页面
      ProjectShowcase: 'portfolio/showcase',
      ProjectDetail: 'portfolio/detail/:projectId',
      CreateProject: 'portfolio/create',
      EditProject: 'portfolio/edit',
      MyPortfolio: 'portfolio/my',

      // 设置页面
      ThemeSettings: 'settings/theme',
      OfflineManager: 'settings/offline',

      // 职业目标相关页面
      JobSelection: 'career/job-selection',
      CareerManagement: 'career/management',
      JobProfile: 'career/job-profile',

      // 第三阶段新增页面 - 职业目标专属功能
      ProjectWorkshop: 'career/project-workshop',
      ProjectWorkshopDetail: 'career/project-workshop/:projectId',
      InterviewPreparation: 'career/interview-preparation',
      InterviewQuestionPractice: 'career/interview-practice/:questionId',
      PortfolioGeneration: 'career/portfolio-generation',
      PortfolioPreview: 'career/portfolio-preview/:portfolioId',
      JobRecommendations: 'career/job-recommendations',
      JobMatchAnalysis: 'career/job-match/:jobId',
      CollaborativeCoding: 'career/collaborative-coding/:sessionId',
      MarketAnalysis: 'career/market-analysis/:jobId',
    },
  },
} : undefined;

interface RootNavigatorProps {
  authState: 'guest' | 'authenticated';
}

export const RootNavigator: React.FC<RootNavigatorProps> = ({ authState }) => {
  return (
    <NavigationContainer linking={linking}>
      <StatusBar style="auto" />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        {/* 主应用界面 - 支持游客和登录用户 */}
        <Stack.Screen
          name="Main"
          component={MainTabNavigator}
        />

        {/* 认证流程 */}
        <Stack.Screen
          name="Auth"
          component={AuthStackNavigator}
          options={{
            presentation: 'modal',
            headerShown: false,
          }}
        />

        {/* 模态页面 */}
        <Stack.Screen
          name="Modal"
          component={ModalScreen as any}
          options={{
            presentation: 'modal',
            headerShown: true,
            title: 'Modal',
          }}
        />

        {/* 设置页面 */}
        <Stack.Screen
          name="Settings"
          component={SettingsScreen as any}
          options={{
            headerShown: true,
            title: 'Settings',
          }}
        />

        {/* 组件演示页面 */}
        <Stack.Screen
          name="ComponentDemo"
          component={ComponentDemoScreen as any}
          options={{
            headerShown: true,
            title: 'Component Demo',
          }}
        />

        {/* 文章详情页面 */}
        <Stack.Screen
          name="ArticleDetail"
          component={ArticleDetailScreen as any}
          options={{
            headerShown: false,
          }}
          initialParams={{ articleId: 'article-1' }}
        />

        {/* 问答详情页面 */}
        <Stack.Screen
          name="QuestionDetail"
          component={QuestionDetailScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 资讯详情页面 */}
        <Stack.Screen
          name="NewsDetail"
          component={NewsDetailScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 职位详情页面 */}
        <Stack.Screen
          name="JobDetail"
          component={JobDetailScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 公司详情页面 */}
        <Stack.Screen
          name="CompanyDetail"
          component={CompanyDetailScreen as any}
          options={{
            headerShown: true,
            title: '公司详情',
          }}
        />

        {/* 创建简历页面 */}
        <Stack.Screen
          name="ResumeCreate"
          component={ResumeCreateScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 简历详情页面 */}
        <Stack.Screen
          name="ResumeDetail"
          component={ResumeDetailScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 学习计划管理页面 */}
        <Stack.Screen
          name="LearningPlan"
          component={LearningPlanScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 学习笔记页面已移除 */}
        {/* <Stack.Screen
          name="Notes"
          component={NotesScreen as any}
          options={{
            headerShown: false,
          }}
        /> */}

        {/* 成就系统页面 */}
        <Stack.Screen
          name="Achievements"
          component={AchievementsScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 用户画像设置页面 */}
        <Stack.Screen
          name="UserProfileSetup"
          component={UserProfileSetupScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 用户技能评估页面 */}
        <Stack.Screen
          name="UserSkillAssessment"
          component={UserSkillAssessmentScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 用户画像管理页面 */}
        <Stack.Screen
          name="UserProfileManagement"
          component={UserProfileManagementScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 个性化评估页面 */}
        <Stack.Screen
          name="PersonalizedAssessment"
          component={PersonalizedAssessmentScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 用户画像分析页面 */}
        <Stack.Screen
          name="UserProfileAnalysis"
          component={UserProfileAnalysisScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 学习仪表板页面 */}
        <Stack.Screen
          name="LearningDashboard"
          component={LearningDashboard as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 代码编辑器页面 */}
        <Stack.Screen
          name="CodeEditor"
          component={CodeEditorScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 学习路径详情页面 */}
        <Stack.Screen
          name="LearningPathDetail"
          component={LearningPathDetailScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 标准路径展示页面 */}
        <Stack.Screen
          name="StandardPath"
          component={StandardPathScreen as any}
          options={{
            headerShown: true,
            title: '标准学习路径',
          }}
        />

        {/* 个性化路径页面 */}
        <Stack.Screen
          name="PersonalizedPath"
          component={PersonalizedPathScreen as any}
          options={{
            headerShown: true,
            title: '个性化路径',
          }}
        />

        {/* 智能推荐页面（向后兼容） */}
        <Stack.Screen
          name="Recommendation"
          component={PersonalizedPathScreen as any}
          options={{
            headerShown: true,
            title: '个性化路径',
          }}
        />

        {/* 原子技能相关页面 */}
        <Stack.Screen
          name="AtomicSkillList"
          component={AtomicSkillListScreen as any}
          options={{
            headerShown: true,
            title: '原子技能',
          }}
        />

        <Stack.Screen
          name="AtomicSkillDetail"
          component={AtomicSkillDetailScreen as any}
          options={{
            headerShown: true,
            title: '技能详情',
          }}
        />

        {/* 技能图谱可视化页面 */}
        <Stack.Screen
          name="SkillGraph"
          component={SkillGraphScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 动态学习路径生成页面 */}
        <Stack.Screen
          name="DynamicLearningPath"
          component={DynamicLearningPathScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 前置技能分析页面 */}
        <Stack.Screen
          name="PrerequisiteAnalysis"
          component={PrerequisiteAnalysisScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 面试题练习页面 */}
        <Stack.Screen
          name="InterviewPractice"
          component={InterviewPracticeScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 题目列表页面 */}
        <Stack.Screen
          name="QuestionList"
          component={QuestionListScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 面试题目详情页面 */}
        <Stack.Screen
          name="InterviewQuestionDetail"
          component={InterviewQuestionDetailScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 学习打卡页面 */}
        <Stack.Screen
          name="StudyCheckIn"
          component={StudyCheckInScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 学习计时器页面 */}
        <Stack.Screen
          name="StudyTimer"
          component={StudyTimerScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 技能评估页面 */}
        <Stack.Screen
          name="SkillAssessment"
          component={SkillAssessmentScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 测试配置页面 */}
        <Stack.Screen
          name="TestConfiguration"
          component={TestConfigurationScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 测试执行页面 */}
        <Stack.Screen
          name="TestExecution"
          component={TestExecutionScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 项目作品展示页面 */}
        <Stack.Screen
          name="ProjectShowcase"
          component={ProjectShowcaseScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 项目详情页面 */}
        <Stack.Screen
          name="ProjectDetail"
          component={ProjectDetailScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 我的作品页面 */}
        <Stack.Screen
          name="MyPortfolio"
          component={MyPortfolioScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 用户个人主页 */}
        <Stack.Screen
          name="UserProfile"
          component={UserProfileScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 成就中心页面 */}
        <Stack.Screen
          name="AchievementCenter"
          component={AchievementCenterScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 用户资料编辑页面 */}
        <Stack.Screen
          name="EditProfile"
          component={EditProfileScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 主题设置页面 */}
        <Stack.Screen
          name="ThemeSettings"
          component={ThemeSettingsScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 离线管理页面已移除 */}
        {/* <Stack.Screen
          name="OfflineManager"
          component={OfflineManagerScreen as any}
          options={{
            headerShown: false,
          }}
        /> */}

        {/* 学习路径API测试页面 */}
        <Stack.Screen
          name="LearningPathTest"
          component={LearningPathTestScreen as any}
          options={{
            headerShown: true,
            title: '学习路径API测试',
          }}
        />

        {/* 职业目标选择页面 */}
        <Stack.Screen
          name="JobSelection"
          component={CareerGoalSelectionScreen as any}
          options={{
            headerShown: false,
            presentation: 'modal',
          }}
        />

        {/* 职业目标管理页面 */}
        <Stack.Screen
          name="CareerManagement"
          component={CareerManagementScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 职业目标详情页面 */}
        <Stack.Screen
          name="JobProfile"
          component={CareerGoalScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 第三阶段新增页面 - 职业目标专属功能 */}

        {/* 项目实战平台 */}
        <Stack.Screen
          name="ProjectWorkshop"
          component={ProjectWorkshopScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 项目实战详情 */}
        <Stack.Screen
          name="ProjectWorkshopDetail"
          component={ProjectWorkshopDetailScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 面试准备 */}
        <Stack.Screen
          name="InterviewPreparation"
          component={InterviewPreparationScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 面试题练习 */}
        <Stack.Screen
          name="InterviewQuestionPractice"
          component={InterviewQuestionPracticeScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 作品集生成 */}
        <Stack.Screen
          name="PortfolioGeneration"
          component={PortfolioGenerationScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 作品集预览 */}
        <Stack.Screen
          name="PortfolioPreview"
          component={PortfolioPreviewScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 岗位推荐 */}
        <Stack.Screen
          name="JobRecommendations"
          component={JobRecommendationsScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 岗位匹配分析 */}
        <Stack.Screen
          name="JobMatchAnalysis"
          component={JobMatchAnalysisScreen as any}
          options={{
            headerShown: false,
          }}
        />

        {/* 市场分析 */}
        <Stack.Screen
          name="MarketAnalysis"
          component={MarketAnalysisScreen as any}
          options={{
            headerShown: true,
          }}
        />

        {/* 协作编程 */}
        <Stack.Screen
          name="CollaborativeCoding"
          component={CollaborativeCodingScreen as any}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
