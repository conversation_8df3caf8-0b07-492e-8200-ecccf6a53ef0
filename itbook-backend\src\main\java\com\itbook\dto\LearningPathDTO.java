package com.itbook.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学习路径DTO
 * 用于解决Hibernate懒加载问题
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Data
public class LearningPathDTO {

    /**
     * 路径类型枚举
     */
    public enum PathType {
        STANDARD,      // 标准路径（每个岗位的官方标准学习路径）
        PERSONALIZED,  // 个性化推荐路径（基于用户画像生成的推荐路径）
        TEMPLATE,      // 模板路径（可复用的路径模板）
        CUSTOM         // 自定义路径（用户自己创建的路径）
    }

    /**
     * 难度级别枚举
     */
    public enum DifficultyLevel {
        BEGINNER,      // 初级
        INTERMEDIATE,  // 中级
        ADVANCED       // 高级
    }

    /**
     * 学习路径ID
     */
    private Long id;

    /**
     * 路径名称
     */
    private String name;

    /**
     * 路径描述
     */
    private String description;

    /**
     * 路径类型
     */
    private PathType pathType;

    /**
     * 难度级别
     */
    private DifficultyLevel difficultyLevel;

    /**
     * 预计学习时长（小时）
     */
    private Integer estimatedHours;

    /**
     * 路径图标
     */
    private String icon;

    /**
     * 路径封面图
     */
    private String coverImage;

    /**
     * 技能标签列表
     */
    private List<SkillTag> skillTags;

    /**
     * 前置要求
     */
    private String prerequisites;

    /**
     * 学习目标
     */
    private String learningObjectives;

    /**
     * 目标职位
     */
    private String targetPosition;

    /**
     * 薪资范围
     */
    private String salaryRange;

    /**
     * 就业前景
     */
    private String careerProspects;

    /**
     * 是否推荐
     */
    private Boolean isRecommended;

    /**
     * 推荐理由
     */
    private String recommendationReason;

    /**
     * 推荐分数
     */
    private BigDecimal recommendationScore;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
