-- ===================================================================
-- ITBook数据库重构 - project表数据迁移到career_project表
-- 版本: V2025071402
-- 创建时间: 2025-07-14
-- 作者: ITBook Team
-- 
-- 将project表的展示功能整合到career_project表，然后删除project表
-- ===================================================================

-- 第一步：扩展career_project表结构，添加project表中的展示相关字段
ALTER TABLE `career_project`
ADD COLUMN IF NOT EXISTS `image_url` varchar(500) COMMENT '项目封面图片URL' AFTER `description`,
ADD COLUMN IF NOT EXISTS `participants` int NOT NULL DEFAULT 0 COMMENT '参与人数' AFTER `deliverables`,
ADD COLUMN IF NOT EXISTS `rating` decimal(3,2) NOT NULL DEFAULT 0.00 COMMENT '项目评分' AFTER `participants`,
ADD COLUMN IF NOT EXISTS `status` enum('ACTIVE','COMPLETED','ARCHIVED') NOT NULL DEFAULT 'ACTIVE' COMMENT '项目状态' AFTER `rating`,
ADD COLUMN IF NOT EXISTS `type` enum('WEB_DEVELOPMENT','MOBILE_DEVELOPMENT','BACKEND_DEVELOPMENT','FULLSTACK_DEVELOPMENT','DATA_SCIENCE','MACHINE_LEARNING','DEVOPS','GAME_DEVELOPMENT','BLOCKCHAIN','IOT') NOT NULL DEFAULT 'WEB_DEVELOPMENT' COMMENT '项目类型' AFTER `status`,
ADD COLUMN IF NOT EXISTS `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为精选项目' AFTER `type`,
ADD COLUMN IF NOT EXISTS `is_trending` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为热门项目' AFTER `is_featured`,
ADD COLUMN IF NOT EXISTS `view_count` int NOT NULL DEFAULT 0 COMMENT '浏览次数' AFTER `is_trending`,
ADD COLUMN IF NOT EXISTS `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞次数' AFTER `view_count`,
ADD COLUMN IF NOT EXISTS `bookmark_count` int NOT NULL DEFAULT 0 COMMENT '收藏次数' AFTER `like_count`,
ADD COLUMN IF NOT EXISTS `source_url` varchar(500) COMMENT '项目源码链接' AFTER `bookmark_count`,
ADD COLUMN IF NOT EXISTS `demo_url` varchar(500) COMMENT '项目演示链接' AFTER `source_url`;

-- 第二步：将project表数据迁移到career_project表
-- 为每个project创建对应的career_project记录
INSERT INTO career_project (
    career_goal_id, project_id, title, description, image_url, difficulty, tech_stack, estimated_time,
    business_scenario, learning_objectives, deliverables, participants, rating, status, type,
    is_featured, is_trending, view_count, like_count, bookmark_count, source_url, demo_url
)
SELECT 
    -- 根据项目类型智能分配career_goal_id
    CASE 
        WHEN p.type IN ('BACKEND_DEVELOPMENT', 'FULLSTACK_DEVELOPMENT') THEN 2  -- Java后端工程师
        WHEN p.type IN ('WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT') THEN 3         -- React前端工程师
        WHEN p.type = 'DATA_SCIENCE' THEN 1                                     -- 数据科学家
        WHEN p.type = 'MACHINE_LEARNING' THEN 1                                 -- 数据科学家
        ELSE 2  -- 默认分配给Java后端工程师
    END as career_goal_id,
    CONCAT('migrated_project_', p.id) as project_id,
    p.title,
    p.description,
    p.image_url,
    -- 将project的difficulty映射到career_project的difficulty
    CASE 
        WHEN p.difficulty = 'BEGINNER' THEN 'junior'
        WHEN p.difficulty = 'INTERMEDIATE' THEN 'mid'
        WHEN p.difficulty = 'ADVANCED' THEN 'senior'
        ELSE 'mid'
    END as difficulty,
    p.technologies as tech_stack,
    p.duration as estimated_time,
    CONCAT('项目类型：', p.type, '，来源于project表迁移') as business_scenario,
    JSON_ARRAY('实践项目开发', '掌握相关技术栈', '提升编程能力', '积累项目经验') as learning_objectives,
    JSON_ARRAY('完整项目代码', '项目文档', '演示视频', '部署说明') as deliverables,
    p.participants,
    p.rating,
    p.status,
    p.type,
    p.is_featured,
    p.is_trending,
    p.view_count,
    p.like_count,
    p.bookmark_count,
    p.source_url,
    p.demo_url
FROM project p
WHERE NOT EXISTS (
    SELECT 1 FROM career_project cp 
    WHERE cp.project_id = CONCAT('migrated_project_', p.id)
);

-- 第三步：验证数据迁移完整性
SELECT 
    'career_project_after_migration' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT difficulty) as unique_difficulties,
    SUM(CASE WHEN image_url IS NOT NULL THEN 1 ELSE 0 END) as records_with_images,
    SUM(participants) as total_participants,
    AVG(rating) as average_rating,
    SUM(view_count) as total_views,
    COUNT(CASE WHEN project_id LIKE 'migrated_project_%' THEN 1 END) as migrated_records
FROM career_project;

-- 第四步：删除project表
DROP TABLE IF EXISTS `project`;

-- 第五步：为career_project表的新字段创建索引
ALTER TABLE `career_project`
ADD INDEX IF NOT EXISTS `idx_career_project_status` (`status`),
ADD INDEX IF NOT EXISTS `idx_career_project_type` (`type`),
ADD INDEX IF NOT EXISTS `idx_career_project_featured` (`is_featured`),
ADD INDEX IF NOT EXISTS `idx_career_project_trending` (`is_trending`),
ADD INDEX IF NOT EXISTS `idx_career_project_rating` (`rating`),
ADD INDEX IF NOT EXISTS `idx_career_project_participants` (`participants`),
ADD INDEX IF NOT EXISTS `idx_career_project_view_count` (`view_count`);

-- 第六步：更新表注释
ALTER TABLE `career_project` COMMENT = '职业项目模板表 - 整合了项目展示功能';

-- 验证最终结果
SELECT 'Project migration completed successfully!' as status;
