-- ===================================================================
-- ITBook简历管理系统 - 测试数据
-- 创建时间: 2025-07-17
-- 作者: ITBook Team
-- 
-- 为简历ID=1添加完整的测试数据，用于测试简历详情页面功能
-- ===================================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===================================================================
-- 工作经历测试数据
-- ===================================================================
INSERT INTO `resume_work_experience` (`resume_id`, `company`, `position`, `description`, `start_date`, `end_date`, `is_current`, `location`, `achievements`, `technologies`, `sort_order`) VALUES
(1, '阿里巴巴集团', '高级前端开发工程师', '负责淘宝网前端架构设计和开发，参与多个核心业务模块的重构和优化工作。', '2022-03-01', NULL, 1, '杭州', 
 '["主导了商品详情页的性能优化，页面加载速度提升40%", "设计并实现了组件库架构，提高了团队开发效率30%", "负责移动端适配方案，支持多种设备尺寸"]', 
 '["React", "TypeScript", "Webpack", "Node.js", "Redux", "Ant Design"]', 1),

(1, '腾讯科技', '前端开发工程师', '参与微信小程序平台的前端开发工作，负责开发者工具和管理后台的功能实现。', '2020-07-01', '2022-02-28', 0, '深圳', 
 '["开发了小程序调试工具，帮助开发者提高调试效率", "参与了管理后台的重构，代码可维护性显著提升", "负责性能监控模块，实现了实时性能数据展示"]', 
 '["Vue.js", "JavaScript", "Element UI", "Echarts", "Sass"]', 2),

(1, '字节跳动', '前端实习生', '在抖音前端团队实习，参与短视频播放器和用户界面的开发工作。', '2019-09-01', '2020-06-30', 0, '北京', 
 '["参与了播放器组件的开发，支持多种视频格式", "协助完成了用户个人页面的重构", "学习并应用了现代前端开发最佳实践"]', 
 '["React", "JavaScript", "CSS3", "HTML5"]', 3);

-- ===================================================================
-- 教育背景测试数据
-- ===================================================================
INSERT INTO `resume_education` (`resume_id`, `school`, `degree`, `major`, `start_date`, `end_date`, `is_current`, `gpa`, `description`, `sort_order`) VALUES
(1, '清华大学', '学士学位', '计算机科学与技术', '2016-09-01', '2020-06-30', 0, 3.8, '主修计算机科学与技术，专业排名前10%。参与了多个科研项目，发表了2篇学术论文。', 1),
(1, '清华大学附属中学', '高中毕业', '理科', '2013-09-01', '2016-06-30', 0, NULL, '高考成绩优异，数学和物理成绩突出。担任班级学习委员，组织了多次学习活动。', 2);

-- ===================================================================
-- 技能测试数据
-- ===================================================================
INSERT INTO `resume_skill` (`resume_id`, `name`, `category`, `level`, `description`, `sort_order`) VALUES
(1, 'React', '前端框架', '精通', '5年React开发经验，熟悉Hooks、Context等高级特性', 1),
(1, 'Vue.js', '前端框架', '熟练', '3年Vue.js开发经验，熟悉Vue3 Composition API', 2),
(1, 'TypeScript', '编程语言', '精通', '4年TypeScript开发经验，熟悉类型系统和高级类型', 3),
(1, 'JavaScript', '编程语言', '精通', '6年JavaScript开发经验，ES6+语法熟练', 4),
(1, 'Node.js', '后端技术', '熟练', '3年Node.js开发经验，熟悉Express、Koa框架', 5),
(1, 'Webpack', '构建工具', '熟练', '熟悉Webpack配置和优化，了解Vite等新工具', 6),
(1, 'Git', '版本控制', '精通', '熟练使用Git进行版本控制和团队协作', 7),
(1, 'Docker', '容器技术', '了解', '了解Docker基本概念和使用方法', 8);

-- ===================================================================
-- 项目经验测试数据
-- ===================================================================
INSERT INTO `resume_project` (`resume_id`, `name`, `description`, `role`, `start_date`, `end_date`, `is_current`, `technologies`, `achievements`, `demo_url`, `source_url`, `sort_order`) VALUES
(1, '电商平台前端重构项目', '对公司主要电商平台进行前端架构重构，提升用户体验和开发效率。', '技术负责人', '2023-01-01', '2023-08-31', 0, 
 '["React 18", "TypeScript", "Ant Design", "Redux Toolkit", "Webpack 5"]', 
 '["页面加载速度提升50%", "代码可维护性显著提升", "团队开发效率提高40%", "用户满意度提升至95%"]', 
 'https://demo.ecommerce.com', 'https://github.com/company/ecommerce-frontend', 1),

(1, '移动端组件库开发', '开发了一套适用于移动端的React组件库，支持多主题和国际化。', '核心开发者', '2022-06-01', '2022-12-31', 0, 
 '["React", "TypeScript", "Styled Components", "Storybook", "Jest"]', 
 '["组件库被5个项目采用", "减少重复代码60%", "提供了完整的文档和示例", "支持按需加载，减少包体积"]', 
 'https://ui.company.com', 'https://github.com/company/mobile-ui', 2),

(1, '实时数据可视化平台', '基于WebSocket和Canvas技术开发的实时数据可视化平台，支持大数据量展示。', '前端开发', '2021-09-01', '2022-03-31', 0, 
 '["Vue.js", "D3.js", "Canvas", "WebSocket", "Echarts"]', 
 '["支持10万+数据点实时渲染", "响应时间控制在100ms以内", "提供了丰富的图表类型", "支持自定义主题和配置"]', 
 'https://dataviz.company.com', NULL, 3);

-- ===================================================================
-- 证书资质测试数据
-- ===================================================================
INSERT INTO `resume_certification` (`resume_id`, `name`, `issuer`, `issue_date`, `expiry_date`, `credential_id`, `credential_url`, `sort_order`) VALUES
(1, 'AWS Certified Developer - Associate', 'Amazon Web Services', '2023-05-15', '2026-05-15', 'AWS-CDA-2023051501', 'https://aws.amazon.com/verification/AWS-CDA-2023051501', 1),
(1, 'Google Cloud Professional Cloud Developer', 'Google Cloud', '2022-11-20', '2024-11-20', 'GCP-PCD-2022112001', 'https://cloud.google.com/certification/verify/GCP-PCD-2022112001', 2),
(1, 'Microsoft Certified: Azure Developer Associate', 'Microsoft', '2022-08-10', '2024-08-10', 'MS-AZ204-2022081001', 'https://docs.microsoft.com/learn/certifications/verify/MS-AZ204-2022081001', 3),
(1, 'Oracle Certified Professional, Java SE 11 Developer', 'Oracle', '2021-12-05', NULL, 'OCP-JAVA11-2021120501', 'https://education.oracle.com/verify/OCP-JAVA11-2021120501', 4);

-- ===================================================================
-- 语言能力测试数据
-- ===================================================================
INSERT INTO `resume_language` (`resume_id`, `name`, `level`, `description`, `sort_order`) VALUES
(1, '中文', '母语', '中文母语，普通话标准', 1),
(1, '英语', '流利', 'CET-6 580分，能够流利进行技术交流和文档阅读', 2),
(1, '日语', '初级', 'JLPT N3水平，能够进行基本的日常交流', 3);

SET FOREIGN_KEY_CHECKS = 1;
