/**
 * 原子技能相关类型定义
 * 支持原子技能库、知识图谱、动态学习路径等功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */

export type UUID = string;
export type Timestamp = string;

/**
 * 技能难度级别
 */
export enum DifficultyLevel {
  BEGINNER = 'BEGINNER',
  INTERMEDIATE = 'INTERMEDIATE', 
  ADVANCED = 'ADVANCED',
  EXPERT = 'EXPERT'
}

/**
 * 技能类型
 */
export enum SkillType {
  CORE = 'CORE',           // 核心技能
  SUPPORTING = 'SUPPORTING', // 支撑技能
  BONUS = 'BONUS'          // 加分技能
}

/**
 * 评估方法
 */
export enum AssessmentMethod {
  QUIZ = 'QUIZ',           // 在线测试
  PROJECT = 'PROJECT',     // 项目实战
  PRACTICE = 'PRACTICE',   // 实践练习
  PEER_REVIEW = 'PEER_REVIEW' // 同行评议
}

/**
 * 技能状态
 */
export enum SkillStatus {
  DRAFT = 'DRAFT',         // 草稿
  PUBLISHED = 'PUBLISHED', // 已发布
  DEPRECATED = 'DEPRECATED' // 已废弃
}

/**
 * 后端API响应中的技能状态（与后端Status枚举对应）
 */
export type ApiSkillStatus = 'DRAFT' | 'PUBLISHED' | 'DEPRECATED';

/**
 * 原子技能接口（前端使用，包含解析后的复杂字段）
 */
export interface AtomicSkill {
  id: number;
  skillCode: string;
  name: string;
  description?: string;
  category: string;
  subcategory?: string;

  // 技能属性
  difficultyLevel: DifficultyLevel;
  estimatedHours: number;
  skillType: SkillType;

  // 验证标准
  verificationCriteria?: VerificationCriteria;
  assessmentMethod: AssessmentMethod;
  passThreshold: number; // 0-1

  // 学习资源
  learningResources?: LearningResource[];
  practiceExercises?: PracticeExercise[];

  // 标签和元数据
  tags?: string[];
  keywords?: string;
  industryRelevance?: IndustryRelevance[];

  // 统计信息
  learnerCount: number;
  completionRate: number;
  averageRating: number;

  // 状态管理
  status: SkillStatus;
  version: string;
  isActive: boolean;

  // 时间戳
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: number;
  updatedBy?: number;
}

/**
 * 后端API响应的原子技能接口（包含JSON字符串字段）
 */
export interface AtomicSkillApiResponse {
  id: number;
  skillCode: string;
  name: string;
  description?: string;
  category: string;
  subcategory?: string;

  // 技能属性
  difficultyLevel: DifficultyLevel;
  estimatedHours: number;
  skillType: SkillType;

  // 验证标准（JSON字符串）
  verificationCriteriaJson?: string;
  assessmentMethod: AssessmentMethod;
  passThreshold: number;

  // 学习资源（JSON字符串）
  learningResourcesJson?: string;
  practiceExercisesJson?: string;

  // 标签和元数据（JSON字符串）
  tagsJson?: string;
  keywords?: string;
  industryRelevanceJson?: string;

  // 统计信息
  learnerCount: number;
  completionRate: number;
  averageRating: number;

  // 状态管理
  status: ApiSkillStatus;
  version: string;
  isActive: boolean;

  // 时间戳
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: number;
  updatedBy?: number;
}

/**
 * 验证标准
 */
export interface VerificationCriteria {
  type: 'quiz' | 'project' | 'practice' | 'peer_review';
  criteria: string[];
  passingScore: number;
  timeLimit?: number; // 分钟
}

/**
 * 学习资源
 */
export interface LearningResource {
  id: string;
  type: 'course' | 'video' | 'article' | 'book' | 'documentation';
  title: string;
  url?: string;
  description?: string;
  duration?: number; // 分钟
  difficulty: DifficultyLevel;
  rating?: number;
  isFree: boolean;
}

/**
 * 练习题目
 */
export interface PracticeExercise {
  id: string;
  type: 'multiple_choice' | 'coding' | 'essay' | 'practical';
  title: string;
  description: string;
  difficulty: DifficultyLevel;
  estimatedTime: number; // 分钟
  points: number;
}

/**
 * 行业相关性
 */
export interface IndustryRelevance {
  industry: string;
  relevanceScore: number; // 0-1
  demandLevel: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * 技能关系类型
 */
export enum RelationshipType {
  PREREQUISITE = 'PREREQUISITE', // 前置技能
  COREQUISITE = 'COREQUISITE',   // 并行技能
  SUCCESSOR = 'SUCCESSOR',       // 后续技能
  RELATED = 'RELATED',           // 相关技能
  ALTERNATIVE = 'ALTERNATIVE'    // 替代技能
}

/**
 * 技能关系
 */
export interface SkillRelationship {
  id: number;
  sourceSkillId: number;
  targetSkillId: number;
  relationshipType: RelationshipType;
  relationshipStrength: number; // 0-1
  isMandatory: boolean;
  description?: string;
  learningSequence?: number;
  confidenceScore: number; // 0-1
  source: 'MANUAL' | 'AUTO_GENERATED' | 'ML_INFERRED';
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * 用户技能掌握水平
 */
export enum MasteryLevel {
  NONE = 'NONE',
  BASIC = 'BASIC',
  INTERMEDIATE = 'INTERMEDIATE',
  ADVANCED = 'ADVANCED',
  EXPERT = 'EXPERT'
}

/**
 * 用户原子技能掌握度
 */
export interface UserAtomicSkillMastery {
  id: number;
  userId: number;
  atomicSkillId: number;
  atomicSkill?: AtomicSkill;
  
  // 掌握度信息
  masteryLevel: MasteryLevel;
  masteryScore: number; // 0-100
  confidenceLevel: number; // 0-1
  
  // 学习历史
  learningHours: number;
  practiceCount: number;
  assessmentCount: number;
  lastAssessmentScore?: number;
  
  // 时间记录
  firstLearnedAt?: Timestamp;
  lastPracticedAt?: Timestamp;
  lastAssessedAt?: Timestamp;
  masteryAchievedAt?: Timestamp;
  
  // 学习路径关联
  learnedViaPathId?: number;
  learningContext?: any;
  
  // 状态和元数据
  isCertified: boolean;
  certificationDate?: Timestamp;
  needsRefresh: boolean;
  decayFactor: number; // 0-1
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * 技能图谱节点
 */
export interface SkillGraphNode {
  skill: AtomicSkill;
  x: number;
  y: number;
  level: number;
  connections: number;
}

/**
 * 技能图谱边
 */
export interface SkillGraphEdge {
  relationship: SkillRelationship;
  sourceNode: SkillGraphNode;
  targetNode: SkillGraphNode;
}

/**
 * 技能图谱
 */
export interface SkillGraph {
  rootSkillId: number;
  nodes: SkillGraphNode[];
  edges: SkillGraphEdge[];
  depth: number;
  totalNodes: number;
  totalEdges: number;
  createdAt: Timestamp;
}

/**
 * 技能搜索过滤器
 */
export interface SkillSearchFilter {
  keyword?: string;
  category?: string;
  subcategory?: string;
  difficultyLevel?: DifficultyLevel;
  skillType?: SkillType;
  status?: SkillStatus;
  minRating?: number;
  maxEstimatedHours?: number;
  tags?: string[];
  industries?: string[];
}

/**
 * 技能推荐请求
 */
export interface SkillRecommendationRequest {
  userId: number;
  careerGoalId?: number;
  currentSkillIds?: number[];
  learningStyle?: 'THEORETICAL' | 'PRACTICAL' | 'PROJECT_DRIVEN' | 'MIXED';
  availableHours?: number;
  difficultyPreference?: DifficultyLevel;
  maxRecommendations?: number;
}

/**
 * 技能推荐结果
 */
export interface SkillRecommendation {
  skill: AtomicSkill;
  recommendationScore: number; // 0-1
  recommendationReason: string;
  prerequisites: AtomicSkill[];
  estimatedCompletionTime: number; // 小时
  difficultyMatch: number; // 0-1
  careerRelevance: number; // 0-1
  learningPathSuggestion?: number[]; // 技能ID序列
}

/**
 * 技能评估记录
 */
export interface SkillAssessmentRecord {
  id: number;
  userId: number;
  atomicSkillId: number;
  assessmentType: AssessmentMethod;
  
  // 评估结果
  score: number;
  maxScore: number;
  passStatus: 'PASS' | 'FAIL' | 'PARTIAL';
  masteryLevelAchieved?: MasteryLevel;
  
  // 评估详情
  assessmentData?: any;
  feedback?: string;
  improvementSuggestions?: string[];
  
  // 评估上下文
  assessmentContext?: any;
  timeSpentMinutes?: number;
  attemptNumber: number;
  
  // 评估者信息
  assessorType: 'SYSTEM' | 'PEER' | 'INSTRUCTOR' | 'SELF';
  assessorId?: number;
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * 技能统计信息
 */
export interface SkillStatistics {
  totalSkills: number;
  skillsByCategory: Record<string, number>;
  skillsByDifficulty: Record<DifficultyLevel, number>;
  skillsByType: Record<SkillType, number>;
  averageCompletionRate: number;
  averageRating: number;
  totalLearners: number;
  mostPopularSkills: AtomicSkill[];
  trendingSkills: AtomicSkill[];
}

/**
 * API响应转换工具函数
 */
export namespace AtomicSkillUtils {

  /**
   * 将API响应转换为前端使用的AtomicSkill类型
   */
  export function fromApiResponse(apiResponse: AtomicSkillApiResponse): AtomicSkill {
    return {
      ...apiResponse,
      status: apiResponse.status as SkillStatus,
      verificationCriteria: apiResponse.verificationCriteriaJson
        ? JSON.parse(apiResponse.verificationCriteriaJson)
        : undefined,
      learningResources: apiResponse.learningResourcesJson
        ? JSON.parse(apiResponse.learningResourcesJson)
        : undefined,
      practiceExercises: apiResponse.practiceExercisesJson
        ? JSON.parse(apiResponse.practiceExercisesJson)
        : undefined,
      tags: apiResponse.tagsJson
        ? JSON.parse(apiResponse.tagsJson)
        : undefined,
      industryRelevance: apiResponse.industryRelevanceJson
        ? JSON.parse(apiResponse.industryRelevanceJson)
        : undefined,
    };
  }

  /**
   * 将前端AtomicSkill类型转换为API请求格式
   */
  export function toApiRequest(skill: Partial<AtomicSkill>): Partial<AtomicSkillApiResponse> {
    return {
      ...skill,
      status: skill.status as ApiSkillStatus,
      verificationCriteriaJson: skill.verificationCriteria
        ? JSON.stringify(skill.verificationCriteria)
        : undefined,
      learningResourcesJson: skill.learningResources
        ? JSON.stringify(skill.learningResources)
        : undefined,
      practiceExercisesJson: skill.practiceExercises
        ? JSON.stringify(skill.practiceExercises)
        : undefined,
      tagsJson: skill.tags
        ? JSON.stringify(skill.tags)
        : undefined,
      industryRelevanceJson: skill.industryRelevance
        ? JSON.stringify(skill.industryRelevance)
        : undefined,
    };
  }

  /**
   * 安全解析JSON字符串
   */
  export function safeJsonParse<T>(jsonString?: string, defaultValue: T | null = null): T | null {
    if (!jsonString) return defaultValue;
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.warn('Failed to parse JSON:', jsonString, error);
      return defaultValue;
    }
  }

  /**
   * 获取难度级别的显示文本
   */
  export function getDifficultyLevelText(level: DifficultyLevel): string {
    const textMap = {
      [DifficultyLevel.BEGINNER]: '初级',
      [DifficultyLevel.INTERMEDIATE]: '中级',
      [DifficultyLevel.ADVANCED]: '高级',
      [DifficultyLevel.EXPERT]: '专家'
    };
    return textMap[level];
  }

  /**
   * 获取技能类型的显示文本
   */
  export function getSkillTypeText(type: SkillType): string {
    const textMap = {
      [SkillType.CORE]: '核心技能',
      [SkillType.SUPPORTING]: '支撑技能',
      [SkillType.BONUS]: '加分技能'
    };
    return textMap[type];
  }

  /**
   * 获取评估方法的显示文本
   */
  export function getAssessmentMethodText(method: AssessmentMethod): string {
    const textMap = {
      [AssessmentMethod.QUIZ]: '在线测试',
      [AssessmentMethod.PROJECT]: '项目实战',
      [AssessmentMethod.PRACTICE]: '实践练习',
      [AssessmentMethod.PEER_REVIEW]: '同行评议'
    };
    return textMap[method];
  }

  /**
   * 获取技能状态的显示文本
   */
  export function getSkillStatusText(status: SkillStatus): string {
    const textMap = {
      [SkillStatus.DRAFT]: '草稿',
      [SkillStatus.PUBLISHED]: '已发布',
      [SkillStatus.DEPRECATED]: '已废弃'
    };
    return textMap[status];
  }
}
