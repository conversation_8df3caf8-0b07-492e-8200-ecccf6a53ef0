package com.itbook.repository;

import com.itbook.entity.SkillRelationship;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 技能关系数据访问接口
 * 提供技能关系图谱的数据库操作方法
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Repository
public interface SkillRelationshipRepository extends JpaRepository<SkillRelationship, Long> {

    /**
     * 根据源技能ID查找关系
     */
    List<SkillRelationship> findBySourceSkillId(Long sourceSkillId);

    /**
     * 根据目标技能ID查找关系
     */
    List<SkillRelationship> findByTargetSkillId(Long targetSkillId);

    /**
     * 根据源技能ID和关系类型查找关系
     */
    List<SkillRelationship> findBySourceSkillIdAndRelationshipType(Long sourceSkillId, 
                                                                   SkillRelationship.RelationshipType relationshipType);

    /**
     * 根据目标技能ID和关系类型查找关系
     */
    List<SkillRelationship> findByTargetSkillIdAndRelationshipType(Long targetSkillId, 
                                                                   SkillRelationship.RelationshipType relationshipType);

    /**
     * 根据技能ID查找所有相关关系（作为源或目标）
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "(sr.sourceSkillId = :sourceSkillId OR sr.targetSkillId = :targetSkillId) AND " +
           "sr.isActive = true " +
           "ORDER BY sr.relationshipStrength DESC")
    List<SkillRelationship> findBySourceSkillIdOrTargetSkillId(@Param("sourceSkillId") Long sourceSkillId,
                                                               @Param("targetSkillId") Long targetSkillId);

    /**
     * 根据源技能ID和目标技能ID查找关系
     */
    List<SkillRelationship> findBySourceSkillIdAndTargetSkillId(Long sourceSkillId, Long targetSkillId);

    /**
     * 查找两个技能之间的关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "((sr.sourceSkillId = :skillId1 AND sr.targetSkillId = :skillId2) OR " +
           "(sr.sourceSkillId = :skillId2 AND sr.targetSkillId = :skillId1)) AND " +
           "sr.isActive = true")
    List<SkillRelationship> findRelationshipBetweenSkills(@Param("skillId1") Long skillId1, 
                                                          @Param("skillId2") Long skillId2);

    /**
     * 根据技能ID集合查找相关关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.sourceSkillId IN :skillIds OR sr.targetSkillId IN :skillIds AND " +
           "sr.isActive = true " +
           "ORDER BY sr.relationshipStrength DESC")
    List<SkillRelationship> findBySourceSkillIdInOrTargetSkillIdIn(@Param("skillIds") List<Long> sourceSkillIds, 
                                                                  @Param("skillIds") List<Long> targetSkillIds);

    /**
     * 根据关系类型查找关系
     */
    List<SkillRelationship> findByRelationshipType(SkillRelationship.RelationshipType relationshipType);

    /**
     * 根据关系强度范围查找关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.relationshipStrength BETWEEN :minStrength AND :maxStrength AND " +
           "sr.isActive = true " +
           "ORDER BY sr.relationshipStrength DESC")
    List<SkillRelationship> findByRelationshipStrengthRange(@Param("minStrength") Double minStrength,
                                                           @Param("maxStrength") Double maxStrength);

    /**
     * 查找必需关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.isMandatory = true AND sr.isActive = true " +
           "ORDER BY sr.relationshipStrength DESC")
    List<SkillRelationship> findMandatoryRelationships();

    /**
     * 根据置信度范围查找关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.confidenceScore BETWEEN :minConfidence AND :maxConfidence AND " +
           "sr.isActive = true " +
           "ORDER BY sr.confidenceScore DESC")
    List<SkillRelationship> findByConfidenceRange(@Param("minConfidence") Double minConfidence,
                                                 @Param("maxConfidence") Double maxConfidence);

    /**
     * 根据关系来源查找关系
     */
    List<SkillRelationship> findBySource(SkillRelationship.Source source);

    /**
     * 查找相关技能（RELATED类型）
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "(sr.sourceSkillId = :skillId OR sr.targetSkillId = :skillId) AND " +
           "sr.relationshipType = 'RELATED' AND sr.isActive = true " +
           "ORDER BY sr.relationshipStrength DESC")
    List<SkillRelationship> findRelatedSkills(@Param("skillId") Long skillId);

    /**
     * 查找前置技能关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.targetSkillId = :skillId AND " +
           "sr.relationshipType = 'PREREQUISITE' AND sr.isActive = true " +
           "ORDER BY sr.learningSequence ASC, sr.relationshipStrength DESC")
    List<SkillRelationship> findPrerequisiteRelationships(@Param("skillId") Long skillId);

    /**
     * 查找后续技能关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.sourceSkillId = :skillId AND " +
           "sr.relationshipType = 'SUCCESSOR' AND sr.isActive = true " +
           "ORDER BY sr.learningSequence ASC, sr.relationshipStrength DESC")
    List<SkillRelationship> findSuccessorRelationships(@Param("skillId") Long skillId);

    /**
     * 查找并行技能关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "(sr.sourceSkillId = :skillId OR sr.targetSkillId = :skillId) AND " +
           "sr.relationshipType = 'COREQUISITE' AND sr.isActive = true " +
           "ORDER BY sr.relationshipStrength DESC")
    List<SkillRelationship> findCorequisiteRelationships(@Param("skillId") Long skillId);

    /**
     * 查找替代技能关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "(sr.sourceSkillId = :skillId OR sr.targetSkillId = :skillId) AND " +
           "sr.relationshipType = 'ALTERNATIVE' AND sr.isActive = true " +
           "ORDER BY sr.relationshipStrength DESC")
    List<SkillRelationship> findAlternativeRelationships(@Param("skillId") Long skillId);

    /**
     * 检查是否存在循环依赖
     */
    @Query("SELECT COUNT(sr) > 0 FROM SkillRelationship sr WHERE " +
           "sr.sourceSkillId = :targetSkillId AND sr.targetSkillId = :sourceSkillId AND " +
           "sr.relationshipType = 'PREREQUISITE' AND sr.isActive = true")
    boolean existsCircularDependency(@Param("sourceSkillId") Long sourceSkillId, 
                                   @Param("targetSkillId") Long targetSkillId);

    /**
     * 检查关系是否已存在
     */
    @Query("SELECT COUNT(sr) > 0 FROM SkillRelationship sr WHERE " +
           "sr.sourceSkillId = :sourceSkillId AND sr.targetSkillId = :targetSkillId AND " +
           "sr.relationshipType = :relationshipType AND sr.isActive = true")
    boolean existsRelationship(@Param("sourceSkillId") Long sourceSkillId,
                              @Param("targetSkillId") Long targetSkillId,
                              @Param("relationshipType") SkillRelationship.RelationshipType relationshipType);

    /**
     * 获取技能的入度（作为目标的关系数量）
     */
    @Query("SELECT COUNT(sr) FROM SkillRelationship sr WHERE " +
           "sr.targetSkillId = :skillId AND sr.isActive = true")
    Long getInDegree(@Param("skillId") Long skillId);

    /**
     * 获取技能的出度（作为源的关系数量）
     */
    @Query("SELECT COUNT(sr) FROM SkillRelationship sr WHERE " +
           "sr.sourceSkillId = :skillId AND sr.isActive = true")
    Long getOutDegree(@Param("skillId") Long skillId);

    /**
     * 获取关系类型统计
     */
    @Query("SELECT sr.relationshipType, COUNT(sr) FROM SkillRelationship sr WHERE " +
           "sr.isActive = true GROUP BY sr.relationshipType ORDER BY COUNT(sr) DESC")
    List<Object[]> getRelationshipTypeStatistics();

    /**
     * 获取关系来源统计
     */
    @Query("SELECT sr.source, COUNT(sr) FROM SkillRelationship sr WHERE " +
           "sr.isActive = true GROUP BY sr.source ORDER BY COUNT(sr) DESC")
    List<Object[]> getRelationshipSourceStatistics();

    /**
     * 根据学习序列排序查找前置关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.targetSkillId = :skillId AND " +
           "sr.relationshipType = 'PREREQUISITE' AND sr.isActive = true AND " +
           "sr.learningSequence IS NOT NULL " +
           "ORDER BY sr.learningSequence ASC")
    List<SkillRelationship> findPrerequisitesByLearningSequence(@Param("skillId") Long skillId);

    /**
     * 批量更新关系状态
     */
    @Query("UPDATE SkillRelationship sr SET sr.isActive = :isActive, sr.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE sr.id IN :relationshipIds")
    int updateActiveStatusBatch(@Param("relationshipIds") Set<Long> relationshipIds, 
                               @Param("isActive") Boolean isActive);

    /**
     * 删除技能的所有关系
     */
    @Query("UPDATE SkillRelationship sr SET sr.isActive = false, sr.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE sr.sourceSkillId = :skillId OR sr.targetSkillId = :skillId")
    int deactivateSkillRelationships(@Param("skillId") Long skillId);

    /**
     * 获取高置信度关系
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.confidenceScore >= :minConfidence AND sr.isActive = true " +
           "ORDER BY sr.confidenceScore DESC, sr.relationshipStrength DESC")
    List<SkillRelationship> findHighConfidenceRelationships(@Param("minConfidence") Double minConfidence);

    /**
     * 获取强关系（高强度）
     */
    @Query("SELECT sr FROM SkillRelationship sr WHERE " +
           "sr.relationshipStrength >= :minStrength AND sr.isActive = true " +
           "ORDER BY sr.relationshipStrength DESC")
    List<SkillRelationship> findStrongRelationships(@Param("minStrength") Double minStrength);
}
