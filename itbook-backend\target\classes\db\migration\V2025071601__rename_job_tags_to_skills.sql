-- 数据库迁移脚本：将job表的tags字段重命名为skills字段
-- 创建时间: 2025-07-16
-- 作者: ITBook Team
-- 目的: 统一职位技能要求字段命名，提高代码可读性

-- 重命名job表的tags字段为skills字段
-- 使用ALTER TABLE CHANGE语句保持数据类型和约束不变
ALTER TABLE `job` CHANGE COLUMN `tags` `skills` JSON NULL;

-- 添加注释说明字段用途
ALTER TABLE `job` MODIFY COLUMN `skills` JSON NULL COMMENT '职位技能要求列表，JSON格式存储技能名称数组';

-- 验证字段重命名是否成功（可选，用于调试）
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'flow' AND TABLE_NAME = 'job' AND COLUMN_NAME = 'skills';
