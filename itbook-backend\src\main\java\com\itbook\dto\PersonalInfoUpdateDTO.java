package com.itbook.dto;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 个人信息更新DTO
 * 
 * <AUTHOR> Team
 * @since 2025-07-17
 */
@Data
public class PersonalInfoUpdateDTO {

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Size(max = 100, message = "姓名长度不能超过100个字符")
    private String fullName;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 200, message = "邮箱长度不能超过200个字符")
    private String email;

    /**
     * 电话
     */
    @Size(max = 50, message = "电话长度不能超过50个字符")
    private String phone;

    /**
     * 所在地
     */
    @Size(max = 200, message = "所在地长度不能超过200个字符")
    private String location;

    /**
     * 个人网站
     */
    @Size(max = 500, message = "个人网站长度不能超过500个字符")
    private String website;

    /**
     * LinkedIn地址
     */
    @Size(max = 500, message = "LinkedIn地址长度不能超过500个字符")
    private String linkedin;

    /**
     * GitHub地址
     */
    @Size(max = 500, message = "GitHub地址长度不能超过500个字符")
    private String github;
}
