package com.itbook.service;

import com.itbook.dto.LearningPathDTO;
import com.itbook.dto.SkillTag;
import com.itbook.dto.UserLearningPathProgressDTO;
import com.itbook.entity.*;
import com.itbook.repository.*;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 个性化学习引擎服务
 * 基于用户学习历史、技能掌握情况和职业目标提供个性化的学习路径建议
 *
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PersonalizedLearningService {

    private final UserRepository userRepository;
    private final UserCareerGoalRepository userCareerGoalRepository;
    private final UserAtomicSkillMasteryRepository userAtomicSkillMasteryRepository;
    private final UserLearningPathProgressRepository userLearningPathProgressRepository;
    private final LearningPathRepository learningPathRepository;
    private final AtomicSkillRepository atomicSkillRepository;
    private final SkillRelationshipRepository skillRelationshipRepository;
    private final CareerSkillMappingRepository careerSkillMappingRepository;
    private final SkillGraphService skillGraphService;
    private final SkillRelationshipService skillRelationshipService;

    /**
     * 为用户生成个性化学习路径推荐
     */
    @Transactional(readOnly = true)
    public PersonalizedLearningPlan generatePersonalizedLearningPlan(Long userId) {
        log.debug("为用户生成个性化学习计划: userId={}", userId);
        
        // 获取用户信息
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));
        
        // 分析用户画像
        UserProfile userProfile = analyzeUserProfile(userId);
        
        // 获取建议的学习路径
        List<SuggestedPath> suggestedPaths = generateSuggestedPaths(userProfile);
        
        // 获取建议的技能
        List<SuggestedSkill> suggestedSkills = generateSuggestedSkills(userProfile);
        
        // 获取学习建议
        List<LearningAdvice> learningAdvices = generateLearningAdvices(userProfile);
        
        PersonalizedLearningPlan plan = new PersonalizedLearningPlan();
        plan.setUserId(userId);
        plan.setUserProfile(userProfile);
        plan.setSuggestedPaths(suggestedPaths);
        plan.setSuggestedSkills(suggestedSkills);
        plan.setLearningAdvices(learningAdvices);
        plan.setGeneratedAt(LocalDateTime.now());

        return plan;
    }

    /**
     * 分析用户画像
     */
    private UserProfile analyzeUserProfile(Long userId) {
        log.debug("分析用户画像: userId={}", userId);
        
        UserProfile profile = new UserProfile();
        profile.setUserId(userId);
        
        // 分析职业目标
        List<UserCareerGoal> careerGoals = userCareerGoalRepository.findByUserIdAndPriorityOrderBySetAtDesc(userId, 1);
        profile.setCareerGoals(careerGoals);
        
        // 分析技能掌握情况
        List<UserAtomicSkillMastery> skillMasteries = userAtomicSkillMasteryRepository.findByUserId(userId);
        profile.setSkillMasteries(skillMasteries);
        
        // 计算技能掌握统计
        SkillMasteryStats masteryStats = calculateSkillMasteryStats(skillMasteries);
        profile.setMasteryStats(masteryStats);
        
        // 分析学习历史
        List<UserLearningPathProgress> learningProgress = userLearningPathProgressRepository.findByUserId(userId);
        // 转换为DTO以避免懒加载问题
        List<UserLearningPathProgressDTO> learningProgressDTOs = convertToLearningProgressDTOs(learningProgress);
        profile.setLearningProgress(learningProgressDTOs);
        
        // 计算学习偏好
        LearningPreferences preferences = analyzeLearningPreferences(learningProgress, skillMasteries);
        profile.setLearningPreferences(preferences);
        
        return profile;
    }

    /**
     * 生成建议的学习路径
     */
    private List<SuggestedPath> generateSuggestedPaths(UserProfile userProfile) {
        log.debug("生成建议学习路径: userId={}", userProfile.getUserId());

        List<SuggestedPath> suggestedPaths = new ArrayList<>();
        
        // 基于职业目标建议路径
        for (UserCareerGoal careerGoal : userProfile.getCareerGoals()) {
            List<SuggestedPath> careerBasedPaths = generateCareerBasedPaths(careerGoal, userProfile);
            suggestedPaths.addAll(careerBasedPaths);
        }

        // 基于技能缺口建议路径
        List<SuggestedPath> skillGapPaths = generateSkillGapBasedPaths(userProfile);
        suggestedPaths.addAll(skillGapPaths);

        // 基于学习偏好建议路径
        List<SuggestedPath> preferencePaths = generatePreferenceBasedPaths(userProfile);
        suggestedPaths.addAll(preferencePaths);

        // 排序和去重
        return suggestedPaths.stream()
                .sorted((a, b) -> b.getSuggestionScore().compareTo(a.getSuggestionScore()))
                .limit(10)
                .collect(Collectors.toList());
    }

    /**
     * 生成建议的技能
     */
    private List<SuggestedSkill> generateSuggestedSkills(UserProfile userProfile) {
        log.debug("生成建议技能: userId={}", userProfile.getUserId());

        List<SuggestedSkill> suggestedSkills = new ArrayList<>();
        
        // 获取用户已掌握的技能ID
        Set<Long> masteredSkillIds = userProfile.getSkillMasteries().stream()
                .filter(mastery -> mastery.getMasteryScore().compareTo(new BigDecimal("70")) >= 0) // 掌握分数70以上认为已掌握
                .map(UserAtomicSkillMastery::getAtomicSkillId)
                .collect(Collectors.toSet());
        
        // 基于已掌握技能推荐相关技能
        for (Long masteredSkillId : masteredSkillIds) {
            List<SkillGraphService.SkillRecommendation> graphRecommendations = 
                    skillGraphService.recommendRelatedSkills(masteredSkillId, 5);
            
            for (SkillGraphService.SkillRecommendation graphRec : graphRecommendations) {
                if (!masteredSkillIds.contains(graphRec.getSkill().getId())) {
                    SuggestedSkill skill = new SuggestedSkill();
                    skill.setSkill(graphRec.getSkill());
                    skill.setSuggestionScore(graphRec.getScore());
                    skill.setSuggestionReason("基于已掌握技能 " +
                            atomicSkillRepository.findById(masteredSkillId)
                                    .map(AtomicSkill::getName).orElse("未知技能") + " 的关联建议");
                    skill.setSuggestionType("SKILL_BASED");
                    suggestedSkills.add(skill);
                }
            }
        }
        
        // 基于职业目标推荐技能
        for (UserCareerGoal careerGoal : userProfile.getCareerGoals()) {
            List<CareerSkillMapping> careerSkills = careerSkillMappingRepository
                    .findByCareerGoalId(careerGoal.getCareerGoalId());
            
            for (CareerSkillMapping mapping : careerSkills) {
                if (!masteredSkillIds.contains(mapping.getAtomicSkillId())) {
                    AtomicSkill skill = atomicSkillRepository.findById(mapping.getAtomicSkillId()).orElse(null);
                    if (skill != null) {
                        SuggestedSkill suggestedSkill = new SuggestedSkill();
                        suggestedSkill.setSkill(skillRelationshipService.createCleanSkill(skill));
                        suggestedSkill.setSuggestionScore(
                                mapping.getImportance().equals(CareerSkillMapping.Importance.CRITICAL) ? new BigDecimal("0.9") :
                                mapping.getImportance().equals(CareerSkillMapping.Importance.IMPORTANT) ? new BigDecimal("0.7") :
                                new BigDecimal("0.5"));
                        suggestedSkill.setSuggestionReason("职业目标所需技能");
                        suggestedSkill.setSuggestionType("CAREER_BASED");
                        suggestedSkills.add(suggestedSkill);
                    }
                }
            }
        }
        
        // 排序和去重
        Map<Long, SuggestedSkill> uniqueSkills = new HashMap<>();
        for (SuggestedSkill skill : suggestedSkills) {
            Long skillId = skill.getSkill().getId();
            if (!uniqueSkills.containsKey(skillId) ||
                skill.getSuggestionScore().compareTo(uniqueSkills.get(skillId).getSuggestionScore()) > 0) {
                uniqueSkills.put(skillId, skill);
            }
        }

        return uniqueSkills.values().stream()
                .sorted((a, b) -> b.getSuggestionScore().compareTo(a.getSuggestionScore()))
                .limit(15)
                .collect(Collectors.toList());
    }

    /**
     * 生成学习建议
     */
    private List<LearningAdvice> generateLearningAdvices(UserProfile userProfile) {
        log.debug("生成学习建议: userId={}", userProfile.getUserId());
        
        List<LearningAdvice> advices = new ArrayList<>();
        
        // 基于技能掌握情况的建议
        SkillMasteryStats stats = userProfile.getMasteryStats();
        if (stats.getAverageMasteryLevel() < 0.5) {
            advices.add(new LearningAdvice("SKILL_IMPROVEMENT", "建议加强基础技能学习", 
                    "您的平均技能掌握度较低，建议先巩固基础技能再学习高级技能", "HIGH"));
        }
        
        // 基于学习偏好的建议
        LearningPreferences preferences = userProfile.getLearningPreferences();
        if (preferences.getPreferredDifficulty() != null) {
            advices.add(new LearningAdvice("DIFFICULTY_MATCH", "匹配学习难度", 
                    "根据您的学习历史，建议选择" + preferences.getPreferredDifficulty() + "难度的课程", "MEDIUM"));
        }
        
        // 基于学习进度的建议
        if (userProfile.getLearningProgress().isEmpty()) {
            advices.add(new LearningAdvice("START_LEARNING", "开始学习之旅", 
                    "建议选择一个学习路径开始您的技能提升之旅", "HIGH"));
        } else {
            long inProgressCount = userProfile.getLearningProgress().stream()
                    .filter(progress -> progress.getStatus() == UserLearningPathProgressDTO.Status.IN_PROGRESS)
                    .count();
            if (inProgressCount > 3) {
                advices.add(new LearningAdvice("FOCUS_LEARNING", "专注学习", 
                        "您同时进行的学习路径较多，建议专注完成1-2个路径以提高学习效果", "MEDIUM"));
            }
        }
        
        return advices;
    }

    /**
     * 计算技能掌握统计
     */
    private SkillMasteryStats calculateSkillMasteryStats(List<UserAtomicSkillMastery> skillMasteries) {
        SkillMasteryStats stats = new SkillMasteryStats();
        
        if (skillMasteries.isEmpty()) {
            stats.setTotalSkills(0);
            stats.setMasteredSkills(0);
            stats.setAverageMasteryLevel(0.0);
            return stats;
        }
        
        stats.setTotalSkills(skillMasteries.size());
        stats.setMasteredSkills((int) skillMasteries.stream()
                .filter(mastery -> mastery.getMasteryScore().compareTo(new BigDecimal("70")) >= 0)
                .count());
        stats.setAverageMasteryLevel(skillMasteries.stream()
                .mapToDouble(mastery -> mastery.getMasteryScore().doubleValue())
                .average()
                .orElse(0.0));
        
        // 按类别统计
        Map<String, Long> categoryStats = skillMasteries.stream()
                .collect(Collectors.groupingBy(
                        mastery -> {
                            AtomicSkill skill = atomicSkillRepository.findById(mastery.getAtomicSkillId()).orElse(null);
                            return skill != null ? skill.getCategory() : "unknown";
                        },
                        Collectors.counting()));
        stats.setCategoryDistribution(categoryStats);
        
        return stats;
    }

    /**
     * 分析学习偏好
     */
    private LearningPreferences analyzeLearningPreferences(List<UserLearningPathProgress> learningProgress,
                                                          List<UserAtomicSkillMastery> skillMasteries) {
        LearningPreferences preferences = new LearningPreferences();
        
        // 分析偏好的难度级别
        if (!skillMasteries.isEmpty()) {
            Map<AtomicSkill.DifficultyLevel, Long> difficultyStats = skillMasteries.stream()
                    .filter(mastery -> mastery.getMasteryScore().compareTo(new BigDecimal("70")) >= 0)
                    .collect(Collectors.groupingBy(
                            mastery -> {
                                AtomicSkill skill = atomicSkillRepository.findById(mastery.getAtomicSkillId()).orElse(null);
                                return skill != null ? skill.getDifficultyLevel() : AtomicSkill.DifficultyLevel.BEGINNER;
                            },
                            Collectors.counting()));
            
            AtomicSkill.DifficultyLevel preferredDifficulty = difficultyStats.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(AtomicSkill.DifficultyLevel.BEGINNER);
            preferences.setPreferredDifficulty(preferredDifficulty.toString());
        }
        
        // 分析偏好的技能类别
        if (!skillMasteries.isEmpty()) {
            Map<String, Long> categoryStats = skillMasteries.stream()
                    .collect(Collectors.groupingBy(
                            mastery -> {
                                AtomicSkill skill = atomicSkillRepository.findById(mastery.getAtomicSkillId()).orElse(null);
                                return skill != null ? skill.getCategory() : "unknown";
                            },
                            Collectors.counting()));
            
            String preferredCategory = categoryStats.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse("frontend");
            preferences.setPreferredCategory(preferredCategory);
        }
        
        // 分析学习活跃度
        if (!learningProgress.isEmpty()) {
            long activePathsCount = learningProgress.stream()
                    .filter(progress -> progress.getStatus() == UserLearningPathProgress.Status.IN_PROGRESS)
                    .count();
            preferences.setLearningActivity(activePathsCount > 2 ? "HIGH" : 
                                          activePathsCount > 0 ? "MEDIUM" : "LOW");
        } else {
            preferences.setLearningActivity("LOW");
        }
        
        return preferences;
    }

    // 其他辅助方法将在下一部分实现...
    
    private List<SuggestedPath> generateCareerBasedPaths(UserCareerGoal careerGoal, UserProfile userProfile) {
        // 实现基于职业目标的路径建议
        return new ArrayList<>();
    }

    private List<SuggestedPath> generateSkillGapBasedPaths(UserProfile userProfile) {
        // 实现基于技能缺口的路径建议
        return new ArrayList<>();
    }

    private List<SuggestedPath> generatePreferenceBasedPaths(UserProfile userProfile) {
        // 实现基于学习偏好的路径建议
        return new ArrayList<>();
    }

    // 内部数据结构
    @Data
    public static class PersonalizedLearningPlan {
        private Long userId;
        private UserProfile userProfile;
        private List<SuggestedPath> suggestedPaths;
        private List<SuggestedSkill> suggestedSkills;
        private List<LearningAdvice> learningAdvices;
        private LocalDateTime generatedAt;
    }

    @Data
    public static class UserProfile {
        private Long userId;
        private List<UserCareerGoal> careerGoals;
        private List<UserAtomicSkillMastery> skillMasteries;
        private SkillMasteryStats masteryStats;
        private List<UserLearningPathProgressDTO> learningProgress;
        private LearningPreferences learningPreferences;
    }

    @Data
    public static class SuggestedPath {
        private Long pathId;
        private String pathName;
        private String description;
        private BigDecimal suggestionScore;
        private String suggestionReason;
        private String suggestionType;
        private int estimatedHours;
        private String difficultyLevel;
    }

    @Data
    public static class SuggestedSkill {
        private AtomicSkill skill;
        private BigDecimal suggestionScore;
        private String suggestionReason;
        private String suggestionType;
    }

    @Data
    public static class LearningAdvice {
        private String adviceType;
        private String title;
        private String description;
        private String priority;
        
        public LearningAdvice(String adviceType, String title, String description, String priority) {
            this.adviceType = adviceType;
            this.title = title;
            this.description = description;
            this.priority = priority;
        }
    }

    @Data
    public static class SkillMasteryStats {
        private int totalSkills;
        private int masteredSkills;
        private double averageMasteryLevel;
        private Map<String, Long> categoryDistribution;
    }

    @Data
    public static class LearningPreferences {
        private String preferredDifficulty;
        private String preferredCategory;
        private String learningActivity;
    }

    /**
     * 将UserLearningPathProgress实体列表转换为DTO列表
     * 避免Hibernate懒加载问题
     */
    private List<UserLearningPathProgressDTO> convertToLearningProgressDTOs(List<UserLearningPathProgress> progressList) {
        List<UserLearningPathProgressDTO> dtoList = new ArrayList<>();

        for (UserLearningPathProgress progress : progressList) {
            try {
                UserLearningPathProgressDTO dto = new UserLearningPathProgressDTO();

                // 复制基本字段
                dto.setId(progress.getId());
                dto.setUserId(progress.getUserId());
                dto.setLearningPathId(progress.getLearningPathId());
                dto.setCareerGoalId(progress.getCareerGoalId());
                dto.setStatus(convertProgressStatus(progress.getStatus()));
                dto.setCompletionPercentage(progress.getCompletionPercentage());
                dto.setCompletedSteps(progress.getCompletedSteps());
                dto.setTotalSteps(progress.getTotalSteps());
                dto.setStartedAt(progress.getStartedAt());
                dto.setLastStudiedAt(progress.getLastStudiedAt());
                dto.setCompletedAt(progress.getCompletedAt());
                dto.setEstimatedCompletionAt(progress.getTargetCompletionDate());
                dto.setStudyTimeMinutes(progress.getStudiedMinutes());
                dto.setCreatedAt(progress.getCreatedAt());
                dto.setUpdatedAt(progress.getUpdatedAt());

                // 安全地获取学习路径信息
                if (progress.getLearningPathId() != null) {
                    LearningPath learningPath = learningPathRepository.findById(progress.getLearningPathId()).orElse(null);
                    if (learningPath != null) {
                        dto.setLearningPath(convertToLearningPathDTO(learningPath));
                    }
                }

                dtoList.add(dto);

            } catch (Exception e) {
                log.warn("转换学习进度DTO失败，跳过记录: progressId={}, error={}",
                        progress.getId(), e.getMessage());
            }
        }

        return dtoList;
    }

    /**
     * 将LearningPath实体转换为DTO
     */
    private LearningPathDTO convertToLearningPathDTO(LearningPath learningPath) {
        LearningPathDTO dto = new LearningPathDTO();

        dto.setId(learningPath.getId());
        dto.setName(learningPath.getName());
        dto.setDescription(learningPath.getDescription());
        dto.setPathType(convertPathType(learningPath.getPathType()));
        dto.setDifficultyLevel(convertDifficultyLevel(learningPath.getDifficultyLevel()));
        dto.setEstimatedHours(learningPath.getEstimatedHours());

        // 设置默认值或从实体中获取可用字段
        dto.setIcon(null); // LearningPath实体中没有icon字段
        dto.setCoverImage(null); // LearningPath实体中没有coverImage字段
        dto.setSkillTags(learningPath.getSkillTags()); // 使用实体的getSkillTags方法
        dto.setPrerequisites(null); // LearningPath实体中没有prerequisites字段
        // 将学习目标列表转换为字符串
        List<String> objectives = learningPath.getLearningObjectives();
        dto.setLearningObjectives(objectives != null && !objectives.isEmpty() ? String.join("; ", objectives) : null);
        dto.setTargetPosition(null); // 可以从targetJob中获取
        dto.setSalaryRange(null); // LearningPath实体中没有salaryRange字段
        dto.setCareerProspects(null); // LearningPath实体中没有careerProspects字段
        dto.setIsRecommended(learningPath.getPathType() == LearningPath.PathType.PERSONALIZED); // 根据路径类型判断
        dto.setRecommendationReason(null); // LearningPath实体中没有recommendationReason字段
        dto.setRecommendationScore(null); // LearningPath实体中没有recommendationScore字段
        dto.setSortOrder(null); // LearningPath实体中没有sortOrder字段
        dto.setIsActive(learningPath.getStatus() == LearningPath.Status.PUBLISHED); // 根据状态判断是否激活
        dto.setCreatedAt(learningPath.getCreatedAt());
        dto.setUpdatedAt(learningPath.getUpdatedAt());

        return dto;
    }

    /**
     * 转换进度状态枚举
     */
    private UserLearningPathProgressDTO.Status convertProgressStatus(UserLearningPathProgress.Status status) {
        if (status == null) return null;
        return UserLearningPathProgressDTO.Status.valueOf(status.name());
    }

    /**
     * 转换路径类型枚举
     */
    private LearningPathDTO.PathType convertPathType(LearningPath.PathType pathType) {
        if (pathType == null) return null;
        return LearningPathDTO.PathType.valueOf(pathType.name());
    }

    /**
     * 转换难度级别枚举
     */
    private LearningPathDTO.DifficultyLevel convertDifficultyLevel(LearningPath.DifficultyLevel difficultyLevel) {
        if (difficultyLevel == null) return null;
        return LearningPathDTO.DifficultyLevel.valueOf(difficultyLevel.name());
    }
}
