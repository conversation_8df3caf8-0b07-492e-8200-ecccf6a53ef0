-- ===================================================================
-- ITBook 职业目标数据库表结构设计
-- 用于存储职业目标相关的完整数据，替代前端静态文件
-- 遵循ITBook项目的数字ID和Career命名规范
-- ===================================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 职业目标主表 - 存储职业目标基本信息
-- ----------------------------
DROP TABLE IF EXISTS `career_goal`;
CREATE TABLE `career_goal` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '职业目标ID',
  `name` varchar(100) NOT NULL COMMENT '职业目标名称，如"Java后端工程师"',
  `category` varchar(50) NOT NULL COMMENT '职业分类，如"后端开发"',
  `description` text COMMENT '职业描述',
  `icon` varchar(50) COMMENT '职业图标',
  `is_popular` tinyint(1) DEFAULT 0 COMMENT '是否为热门职业',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_career_goal_name` (`name`),
  KEY `idx_career_goal_category` (`category`),
  KEY `idx_career_goal_popular` (`is_popular`),
  KEY `idx_career_goal_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职业目标主表';

-- ----------------------------
-- 职业技能表 - 存储职业目标所需的技能要求
-- ----------------------------
DROP TABLE IF EXISTS `career_skill`;
CREATE TABLE `career_skill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '技能ID',
  `career_goal_id` bigint NOT NULL COMMENT '职业目标ID',
  `skill_id` varchar(100) NOT NULL COMMENT '技能标识符',
  `skill_name` varchar(100) NOT NULL COMMENT '技能名称',
  `skill_category` varchar(50) COMMENT '技能分类',
  `importance` enum('critical','important','nice-to-have') NOT NULL DEFAULT 'important' COMMENT '重要程度',
  `target_level` enum('beginner','intermediate','advanced','expert') NOT NULL DEFAULT 'intermediate' COMMENT '目标水平',
  `skill_type` enum('core','bonus') NOT NULL DEFAULT 'core' COMMENT '技能类型：核心技能或加分技能',
  `description` text COMMENT '技能描述',
  `learning_resources` json COMMENT '学习资源列表',
  `assessment_criteria` json COMMENT '评估标准',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_career_skill_goal` (`career_goal_id`),
  KEY `idx_career_skill_type` (`skill_type`),
  KEY `idx_career_skill_importance` (`importance`),
  CONSTRAINT `fk_career_skill_goal` FOREIGN KEY (`career_goal_id`) REFERENCES `career_goal` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职业技能表';

-- ----------------------------
-- 职业发展路径表 - 存储不同级别的职业发展路径
-- ----------------------------
DROP TABLE IF EXISTS `career_path`;
CREATE TABLE `career_path` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '路径ID',
  `career_goal_id` bigint NOT NULL COMMENT '职业目标ID',
  `level` enum('junior','mid','senior') NOT NULL COMMENT '职业级别',
  `duration` varchar(50) COMMENT '预期时长',
  `skills` json COMMENT '所需技能列表',
  `projects` json COMMENT '推荐项目列表',
  `salary_min` int COMMENT '薪资范围最小值（千元）',
  `salary_max` int COMMENT '薪资范围最大值（千元）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_career_path_goal_level` (`career_goal_id`, `level`),
  KEY `idx_career_path_level` (`level`),
  CONSTRAINT `fk_career_path_goal` FOREIGN KEY (`career_goal_id`) REFERENCES `career_goal` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职业发展路径表';

-- ----------------------------
-- 职业项目模板表 - 存储不同级别的项目实战模板
-- ----------------------------
DROP TABLE IF EXISTS `career_project`;
CREATE TABLE `career_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `career_goal_id` bigint NOT NULL COMMENT '职业目标ID',
  `project_id` varchar(100) NOT NULL COMMENT '项目标识符',
  `title` varchar(200) NOT NULL COMMENT '项目标题',
  `description` text COMMENT '项目描述',
  `difficulty` enum('junior','mid','senior') NOT NULL COMMENT '项目难度级别',
  `tech_stack` json COMMENT '技术栈',
  `estimated_time` varchar(50) COMMENT '预计完成时间',
  `business_scenario` text COMMENT '业务场景',
  `learning_objectives` json COMMENT '学习目标',
  `deliverables` json COMMENT '交付物',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_career_project_goal` (`career_goal_id`),
  KEY `idx_career_project_difficulty` (`difficulty`),
  CONSTRAINT `fk_career_project_goal` FOREIGN KEY (`career_goal_id`) REFERENCES `career_goal` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职业项目模板表';

-- ----------------------------
-- 职业面试题表 - 存储面试准备相关的题目和话题
-- ----------------------------
DROP TABLE IF EXISTS `career_interview_topic`;
CREATE TABLE `career_interview_topic` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '面试题ID',
  `career_goal_id` bigint NOT NULL COMMENT '职业目标ID',
  `topic_id` varchar(100) NOT NULL COMMENT '话题标识符',
  `category` enum('technical','behavioral','case-study') NOT NULL COMMENT '题目分类',
  `topic` varchar(200) NOT NULL COMMENT '话题名称',
  `questions` json COMMENT '相关问题列表',
  `difficulty` enum('easy','intermediate','hard') NOT NULL DEFAULT 'intermediate' COMMENT '难度级别',
  `importance` enum('critical','important','nice-to-know') NOT NULL DEFAULT 'important' COMMENT '重要程度',
  `preparation_tips` json COMMENT '准备建议',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_career_interview_goal` (`career_goal_id`),
  KEY `idx_career_interview_category` (`category`),
  KEY `idx_career_interview_difficulty` (`difficulty`),
  CONSTRAINT `fk_career_interview_goal` FOREIGN KEY (`career_goal_id`) REFERENCES `career_goal` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职业面试题表';

-- ----------------------------
-- 职业市场数据表 - 存储职业相关的市场分析数据
-- ----------------------------
DROP TABLE IF EXISTS `career_market_data`;
CREATE TABLE `career_market_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '市场数据ID',
  `career_goal_id` bigint NOT NULL COMMENT '职业目标ID',
  `junior_salary_min` int COMMENT '初级薪资最小值',
  `junior_salary_max` int COMMENT '初级薪资最大值',
  `mid_salary_min` int COMMENT '中级薪资最小值',
  `mid_salary_max` int COMMENT '中级薪资最大值',
  `senior_salary_min` int COMMENT '高级薪资最小值',
  `senior_salary_max` int COMMENT '高级薪资最大值',
  `demand_trend` enum('increasing','stable','decreasing') DEFAULT 'stable' COMMENT '需求趋势',
  `popular_cities` json COMMENT '热门城市列表',
  `top_companies` json COMMENT '顶级公司列表',
  `skill_demand` json COMMENT '技能需求度数据',
  `last_updated` date COMMENT '最后更新日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_career_market_goal` (`career_goal_id`),
  CONSTRAINT `fk_career_market_goal` FOREIGN KEY (`career_goal_id`) REFERENCES `career_goal` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职业市场数据表';

-- ----------------------------
-- 职业分类表 - 存储职业目标的分类信息
-- ----------------------------
DROP TABLE IF EXISTS `career_category`;
CREATE TABLE `career_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_id` varchar(50) NOT NULL COMMENT '分类标识符',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `icon` varchar(50) COMMENT '分类图标',
  `description` text COMMENT '分类描述',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_career_category_id` (`category_id`),
  KEY `idx_career_category_active` (`is_active`),
  KEY `idx_career_category_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职业分类表';

SET FOREIGN_KEY_CHECKS = 1;
