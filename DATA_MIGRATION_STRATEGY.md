# ITBook数据迁移策略设计

## 🎯 **迁移目标**

### **核心目标**
1. **平滑迁移**: 将现有粗粒度技能体系转换为细粒度原子技能体系
2. **数据完整性**: 确保所有现有数据在迁移后保持完整和一致
3. **业务连续性**: 迁移过程中不影响现有业务功能
4. **向后兼容**: 新系统能够兼容现有的业务逻辑和API

### **迁移范围**
- **career_skill** → **atomic_skill** + **career_skill_mapping**
- **learning_path.skill_tags** → **atomic_skill** + **skill_relationship**
- **course.tags** → **atomic_skill** + **course关联**
- **用户学习数据** → **user_atomic_skill_mastery**

## 🗺️ **迁移策略总览**

### **三阶段迁移法**
```
阶段一：数据准备和清洗 (1周)
    ↓
阶段二：原子技能生成和映射 (2周)
    ↓
阶段三：关系建立和验证 (1周)
```

## 📊 **阶段一：数据准备和清洗**

### **1.1 数据备份**
```sql
-- 创建备份表
CREATE TABLE career_skill_backup AS SELECT * FROM career_skill;
CREATE TABLE learning_path_backup AS SELECT * FROM learning_path;
CREATE TABLE course_backup AS SELECT * FROM course;

-- 记录迁移开始时间
INSERT INTO migration_log (phase, status, start_time, description) 
VALUES ('PHASE_1', 'STARTED', NOW(), '数据备份完成');
```

### **1.2 数据清洗规则**
```java
// 技能名称标准化
public class SkillNameNormalizer {
    // 去除特殊字符，统一大小写
    // "Java基础" → "java基础"
    // "Spring Boot" → "spring boot"
    // "MySQL数据库" → "mysql数据库"
}

// 分类标准化
public class CategoryNormalizer {
    private static final Map<String, String> CATEGORY_MAPPING = Map.of(
        "编程语言", "programming_language",
        "开发框架", "development_framework", 
        "数据库", "database",
        "Web技术", "web_technology",
        "架构设计", "architecture_design"
    );
}
```

### **1.3 重复数据处理**
```sql
-- 识别重复技能
SELECT skill_name, COUNT(*) as count 
FROM career_skill 
GROUP BY LOWER(TRIM(skill_name)) 
HAVING COUNT(*) > 1;

-- 合并策略：保留最高重要程度的记录
```

## 🔄 **阶段二：原子技能生成和映射**

### **2.1 原子技能生成策略**

#### **策略A：基于career_skill直接映射**
```java
public class CareerSkillToAtomicSkillMapper {
    
    public List<AtomicSkill> mapCareerSkillToAtomicSkills(CareerSkill careerSkill) {
        List<AtomicSkill> atomicSkills = new ArrayList<>();
        
        // 主技能映射
        AtomicSkill mainSkill = createMainAtomicSkill(careerSkill);
        atomicSkills.add(mainSkill);
        
        // 子技能拆分（基于描述和学习资源）
        List<AtomicSkill> subSkills = extractSubSkills(careerSkill);
        atomicSkills.addAll(subSkills);
        
        return atomicSkills;
    }
    
    private AtomicSkill createMainAtomicSkill(CareerSkill careerSkill) {
        AtomicSkill atomicSkill = new AtomicSkill();
        atomicSkill.setSkillCode(generateSkillCode(careerSkill.getSkillName()));
        atomicSkill.setName(careerSkill.getSkillName());
        atomicSkill.setDescription(careerSkill.getDescription());
        atomicSkill.setCategory(normalizeCategory(careerSkill.getSkillCategory()));
        atomicSkill.setDifficultyLevel(mapDifficultyLevel(careerSkill.getTargetLevel()));
        atomicSkill.setSkillType(mapSkillType(careerSkill.getSkillType()));
        atomicSkill.setStatus(AtomicSkill.Status.PUBLISHED);
        atomicSkill.setIsActive(true);
        return atomicSkill;
    }
}
```

#### **策略B：基于learning_path.skill_tags提取**
```java
public class SkillTagsToAtomicSkillMapper {
    
    public List<AtomicSkill> extractFromSkillTags(String skillTagsJson) {
        List<AtomicSkill> atomicSkills = new ArrayList<>();
        
        // 解析JSON技能标签
        List<SkillTag> skillTags = parseSkillTags(skillTagsJson);
        
        for (SkillTag tag : skillTags) {
            AtomicSkill atomicSkill = new AtomicSkill();
            atomicSkill.setSkillCode(generateSkillCode(tag.getName()));
            atomicSkill.setName(tag.getName());
            atomicSkill.setCategory(normalizeCategory(tag.getCategory()));
            atomicSkill.setDifficultyLevel(mapTargetLevel(tag.getTargetLevel()));
            
            // 设置权重相关属性
            atomicSkill.setAverageRating(BigDecimal.valueOf(tag.getWeight() * 5));
            atomicSkill.setIsCore(tag.isCore());
            
            atomicSkills.add(atomicSkill);
        }
        
        return atomicSkills;
    }
}
```

### **2.2 技能编码生成规则**
```java
public class SkillCodeGenerator {
    
    public String generateSkillCode(String skillName) {
        // 规则：分类前缀 + 技能名称缩写 + 序号
        // "Java基础" → "PROG_JAVA_BASIC_001"
        // "Spring Boot" → "FRAM_SPRING_BOOT_001"
        // "MySQL数据库" → "DB_MYSQL_001"
        
        String category = extractCategory(skillName);
        String nameAbbr = generateAbbreviation(skillName);
        String sequence = getNextSequence(category, nameAbbr);
        
        return String.format("%s_%s_%s", category, nameAbbr, sequence);
    }
}
```

### **2.3 映射关系建立**
```java
public class CareerSkillMappingBuilder {
    
    public void createMappings(CareerSkill careerSkill, List<AtomicSkill> atomicSkills) {
        for (AtomicSkill atomicSkill : atomicSkills) {
            CareerSkillMapping mapping = new CareerSkillMapping();
            mapping.setCareerSkillId(careerSkill.getId());
            mapping.setAtomicSkillId(atomicSkill.getId());
            mapping.setWeight(calculateWeight(careerSkill, atomicSkill));
            mapping.setImportance(mapImportance(careerSkill.getImportance()));
            mapping.setRequiredMasteryLevel(mapMasteryLevel(careerSkill.getTargetLevel()));
            mapping.setMappingSource(CareerSkillMapping.MappingSource.AUTO_GENERATED);
            mapping.setConfidenceScore(calculateConfidence(careerSkill, atomicSkill));
            mapping.setMappingReason("基于career_skill自动生成");
            
            careerSkillMappingRepository.save(mapping);
        }
    }
}
```

## 🔗 **阶段三：关系建立和验证**

### **3.1 技能关系推断**
```java
public class SkillRelationshipInferrer {
    
    public void inferRelationships(List<AtomicSkill> atomicSkills) {
        // 基于learning_path中的技能顺序推断前置关系
        inferPrerequisiteRelationships(atomicSkills);
        
        // 基于技能名称相似度推断相关关系
        inferRelatedRelationships(atomicSkills);
        
        // 基于分类推断并行关系
        inferCorequisiteRelationships(atomicSkills);
    }
    
    private void inferPrerequisiteRelationships(List<AtomicSkill> atomicSkills) {
        // 从learning_path的skill_tags中提取技能顺序
        List<LearningPath> paths = learningPathRepository.findAll();
        
        for (LearningPath path : paths) {
            List<SkillTag> skillTags = parseSkillTags(path.getSkillTags());
            
            for (int i = 1; i < skillTags.size(); i++) {
                SkillTag prerequisite = skillTags.get(i - 1);
                SkillTag target = skillTags.get(i);
                
                createPrerequisiteRelationship(prerequisite, target, i);
            }
        }
    }
}
```

### **3.2 用户掌握度迁移**
```java
public class UserMasteryMigrator {
    
    public void migrateUserMasteries() {
        // 基于用户的学习路径进度推断技能掌握度
        List<UserLearningProgress> progresses = userProgressRepository.findAll();
        
        for (UserLearningProgress progress : progresses) {
            List<AtomicSkill> pathSkills = getPathAtomicSkills(progress.getLearningPathId());
            
            for (AtomicSkill skill : pathSkills) {
                UserAtomicSkillMastery mastery = new UserAtomicSkillMastery();
                mastery.setUserId(progress.getUserId());
                mastery.setAtomicSkillId(skill.getId());
                mastery.setMasteryLevel(inferMasteryLevel(progress, skill));
                mastery.setMasteryScore(calculateMasteryScore(progress, skill));
                mastery.setLearnedViaPathId(progress.getLearningPathId());
                
                userAtomicSkillMasteryRepository.save(mastery);
            }
        }
    }
}
```

## 🔄 **回滚策略**

### **回滚触发条件**
1. **数据完整性验证失败**：原始数据与迁移后数据不匹配
2. **业务逻辑验证失败**：关键业务功能异常
3. **性能严重下降**：查询响应时间超过阈值
4. **用户反馈严重问题**：影响用户正常使用

### **回滚执行步骤**
```sql
-- 1. 停止应用服务
-- 2. 清空新表数据
TRUNCATE TABLE atomic_skill;
TRUNCATE TABLE skill_relationship;
TRUNCATE TABLE user_atomic_skill_mastery;
TRUNCATE TABLE career_skill_mapping;

-- 3. 恢复备份数据
INSERT INTO career_skill SELECT * FROM career_skill_backup;
INSERT INTO learning_path SELECT * FROM learning_path_backup;
INSERT INTO course SELECT * FROM course_backup;

-- 4. 重置自增ID
ALTER TABLE career_skill AUTO_INCREMENT = 1;
ALTER TABLE learning_path AUTO_INCREMENT = 1;
ALTER TABLE course AUTO_INCREMENT = 1;

-- 5. 记录回滚日志
INSERT INTO migration_log (phase, status, end_time, description) 
VALUES ('ROLLBACK', 'COMPLETED', NOW(), '数据回滚完成');
```

## 📋 **迁移执行计划**

### **时间安排**
```
第1周：
- 周一-周二：数据备份和清洗
- 周三-周四：迁移脚本开发和测试
- 周五：阶段一验证

第2周：
- 周一-周三：原子技能生成和映射
- 周四-周五：映射关系建立

第3周：
- 周一-周二：技能关系推断
- 周三-周四：用户掌握度迁移
- 周五：阶段二验证

第4周：
- 周一-周二：全面数据验证
- 周三-周四：性能测试和优化
- 周五：迁移完成和文档更新
```

### **风险控制**
1. **分批迁移**：每次处理100条记录，避免大事务
2. **实时监控**：监控迁移进度和系统性能
3. **数据校验**：每个阶段完成后进行数据完整性校验
4. **备用方案**：准备手动修复脚本处理异常情况

### **成功标准**
- ✅ 数据完整性：100%的原始数据有对应的原子技能
- ✅ 映射准确性：95%以上的映射关系通过专家验证
- ✅ 性能要求：查询响应时间不超过原系统的120%
- ✅ 业务连续性：所有现有API功能正常工作

## 📊 **监控和验证**

### **数据完整性验证**
```sql
-- 验证技能数量
SELECT 
    (SELECT COUNT(*) FROM career_skill) as original_skills,
    (SELECT COUNT(*) FROM atomic_skill) as atomic_skills,
    (SELECT COUNT(*) FROM career_skill_mapping) as mappings;

-- 验证映射关系
SELECT cs.skill_name, COUNT(csm.id) as mapping_count
FROM career_skill cs
LEFT JOIN career_skill_mapping csm ON cs.id = csm.career_skill_id
GROUP BY cs.id, cs.skill_name
HAVING mapping_count = 0;
```

### **业务逻辑验证**
```java
@Test
public void testMigrationIntegrity() {
    // 验证每个career_skill都有对应的atomic_skill映射
    List<CareerSkill> careerSkills = careerSkillRepository.findAll();
    for (CareerSkill cs : careerSkills) {
        List<CareerSkillMapping> mappings = mappingRepository.findByCareerSkillId(cs.getId());
        assertFalse("Career skill " + cs.getSkillName() + " has no atomic skill mappings", 
                   mappings.isEmpty());
    }
}
```

这个迁移策略确保了数据的完整性、一致性和业务连续性，为ITBook向原子技能体系的平滑过渡提供了详细的指导。
