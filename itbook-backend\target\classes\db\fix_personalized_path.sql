-- ========================================
-- ITBook 个性化路径表结构修正脚本
-- 修正字段名匹配问题
-- ========================================

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 删除所有相关表
DROP TABLE IF EXISTS `personalized_path_feedback`;
DROP TABLE IF EXISTS `personalized_path`;

-- 重新创建个性化路径表（字段名与实体类完全匹配）
CREATE TABLE `personalized_path` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `job_id` bigint DEFAULT NULL COMMENT '目标岗位ID（可选）',
  `personalized_path_id` bigint NOT NULL COMMENT '个性化学习路径ID',
  `personalization_score` decimal(5,3) DEFAULT 0.000 COMMENT '个性化匹配分数(0-1)',
  `personalization_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '个性化理由',
  `algorithm_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'v1.0' COMMENT '算法版本',
  `personalization_factors` json DEFAULT NULL COMMENT '个性化因子详情',
  `is_accepted` bit(1) DEFAULT NULL COMMENT '用户是否接受个性化路径',
  `feedback_rating` int DEFAULT NULL COMMENT '用户反馈评分(1-5)',
  `user_feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户反馈文本',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_personalized_path_id` (`personalized_path_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_personalization_score` (`personalization_score`),
  CONSTRAINT `fk_personalized_path_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_personalized_path_job` FOREIGN KEY (`job_id`) REFERENCES `job` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_personalized_path_learning_path` FOREIGN KEY (`personalized_path_id`) REFERENCES `learning_path` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个性化学习路径记录表';

-- 插入测试数据
INSERT INTO `personalized_path` (
  `user_id`,
  `job_id`,
  `personalized_path_id`,
  `personalization_score`,
  `personalization_reason`,
  `algorithm_version`,
  `personalization_factors`,
  `is_accepted`,
  `feedback_rating`,
  `user_feedback`,
  `created_at`,
  `updated_at`
) VALUES 
(1, 1, 1, 0.920, '基于你的编程基础和学习偏好，这条路径最适合你快速进入前端开发领域', 'v1.0', 
 '{"skills": ["JavaScript", "React"], "experience": "intermediate", "goal": "frontend"}', 
 NULL, NULL, NULL, NOW(6), NOW(6)),
(1, 2, 2, 0.850, '你的逻辑思维能力强，适合学习后端开发的系统性知识', 'v1.0',
 '{"skills": ["Java", "Spring"], "experience": "beginner", "goal": "backend"}',
 NULL, NULL, NULL, NOW(6), NOW(6)),
(1, 3, 3, 0.780, '数据分析入门门槛较低，适合你的学习时间安排', 'v1.0',
 '{"skills": ["Python"], "experience": "beginner", "goal": "data_analysis"}',
 NULL, NULL, NULL, NOW(6), NOW(6)),
(2, 1, 1, 0.880, '根据你的前端开发兴趣，推荐这条现代前端技术路径', 'v1.0',
 '{"skills": ["HTML", "CSS"], "experience": "beginner", "goal": "frontend"}',
 NULL, NULL, NULL, NOW(6), NOW(6)),
(2, 4, 4, 0.750, '移动开发是当前热门方向，适合有一定编程基础的学习者', 'v1.0',
 '{"skills": ["JavaScript"], "experience": "intermediate", "goal": "mobile"}',
 NULL, NULL, NULL, NOW(6), NOW(6));

-- 更新 recommendation_feedback 表的外键引用（如果存在）
-- 检查表是否存在
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'recommendation_feedback' AND table_schema = 'itbook_dev');

-- 如果表存在，更新外键约束
SET @sql = IF(@table_exists > 0, 
  'ALTER TABLE recommendation_feedback DROP FOREIGN KEY IF EXISTS fk_recommendation_feedback_recommendation;
   ALTER TABLE recommendation_feedback ADD CONSTRAINT fk_recommendation_feedback_personalized_path 
   FOREIGN KEY (recommendation_id) REFERENCES personalized_path (id) ON DELETE SET NULL ON UPDATE RESTRICT;',
  'SELECT "recommendation_feedback table does not exist" as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证数据
SELECT COUNT(*) as personalized_path_count FROM `personalized_path`;
SELECT id, user_id, job_id, personalized_path_id, personalization_score, personalization_reason 
FROM `personalized_path` LIMIT 5;

COMMIT;
