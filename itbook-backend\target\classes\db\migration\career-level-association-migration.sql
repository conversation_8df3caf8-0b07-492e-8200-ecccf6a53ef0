-- =====================================================
-- ITBook项目 - UserCareerGoal级别字段重构迁移脚本
-- 
-- 目标：将target_level枚举字段改为career_level_id关联字段
-- 原因：充分利用career_level表的丰富信息，实现真正的关系型设计
-- 作者：ITBook Team
-- 日期：2025-07-13
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 第一步：备份现有数据
-- =====================================================

-- 创建备份表
CREATE TABLE `user_career_goal_level_backup` AS SELECT * FROM `user_career_goal`;

-- 记录迁移开始时间
INSERT INTO migration_log (migration_name, description, start_time, status) 
VALUES ('career-level-association-migration', 
        '将UserCareerGoal的target_level枚举改为career_level_id关联', 
        NOW(), 'STARTED');

-- =====================================================
-- 第二步：确保career_level表数据完整
-- =====================================================

-- 检查career_level表是否存在必要的级别数据
-- 如果不存在，先创建基础级别数据

-- 为career_goal_id=1（假设存在）创建基础级别（如果不存在）
INSERT IGNORE INTO career_level (career_goal_id, level_code, level_name, description, 
                                min_experience_years, max_experience_years, 
                                salary_range_min, salary_range_max, sort_order, is_active) 
VALUES 
(1, 'junior', '初级工程师', '0-2年经验，掌握基础技能', 0, 2, 8000, 15000, 1, TRUE),
(1, 'mid', '中级工程师', '2-5年经验，具备独立开发能力', 2, 5, 15000, 25000, 2, TRUE),
(1, 'senior', '高级工程师', '5-8年经验，具备架构设计能力', 5, 8, 25000, 40000, 3, TRUE);

-- 为career_goal_id=2（如果存在）创建基础级别
INSERT IGNORE INTO career_level (career_goal_id, level_code, level_name, description, 
                                min_experience_years, max_experience_years, 
                                salary_range_min, salary_range_max, sort_order, is_active) 
VALUES 
(2, 'junior', '初级前端工程师', '0-2年经验，掌握基础前端技能', 0, 2, 7000, 14000, 1, TRUE),
(2, 'mid', '中级前端工程师', '2-5年经验，具备独立前端开发能力', 2, 5, 14000, 23000, 2, TRUE),
(2, 'senior', '高级前端工程师', '5-8年经验，具备前端架构设计能力', 5, 8, 23000, 38000, 3, TRUE);

-- =====================================================
-- 第三步：添加新的career_level_id字段
-- =====================================================

-- 添加新字段（先允许NULL）
ALTER TABLE `user_career_goal` 
ADD COLUMN `career_level_id` BIGINT(20) NULL COMMENT '职业级别ID（关联career_level表）' 
AFTER `career_goal_id`;

-- =====================================================
-- 第四步：数据迁移 - 将枚举值转换为关联ID
-- =====================================================

-- 创建临时函数来查找对应的career_level_id
DELIMITER $$

CREATE FUNCTION GetCareerLevelId(p_career_goal_id BIGINT, p_level_code VARCHAR(50)) 
RETURNS BIGINT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE level_id BIGINT DEFAULT NULL;
    
    SELECT id INTO level_id 
    FROM career_level 
    WHERE career_goal_id = p_career_goal_id 
      AND level_code = p_level_code 
      AND is_active = TRUE
    LIMIT 1;
    
    -- 如果找不到对应的级别，返回默认的mid级别
    IF level_id IS NULL THEN
        SELECT id INTO level_id 
        FROM career_level 
        WHERE career_goal_id = p_career_goal_id 
          AND level_code = 'mid' 
          AND is_active = TRUE
        LIMIT 1;
    END IF;
    
    RETURN level_id;
END$$

DELIMITER ;

-- 执行数据迁移
UPDATE `user_career_goal` 
SET `career_level_id` = GetCareerLevelId(
    COALESCE(career_goal_id, 1),  -- 如果career_goal_id为NULL，默认使用1
    LOWER(target_level)           -- 将枚举值转换为小写
)
WHERE `career_level_id` IS NULL;

-- 删除临时函数
DROP FUNCTION GetCareerLevelId;

-- =====================================================
-- 第五步：处理无法匹配的数据
-- =====================================================

-- 对于仍然为NULL的记录，设置为默认值
UPDATE `user_career_goal` 
SET `career_level_id` = (
    SELECT id FROM career_level 
    WHERE level_code = 'mid' 
    AND is_active = TRUE 
    LIMIT 1
)
WHERE `career_level_id` IS NULL;

-- =====================================================
-- 第六步：添加约束和索引
-- =====================================================

-- 将字段设置为NOT NULL
ALTER TABLE `user_career_goal` 
MODIFY COLUMN `career_level_id` BIGINT(20) NOT NULL COMMENT '职业级别ID（关联career_level表）';

-- 添加外键约束
ALTER TABLE `user_career_goal` 
ADD CONSTRAINT `fk_user_career_goal_career_level` 
FOREIGN KEY (`career_level_id`) REFERENCES `career_level` (`id`) 
ON DELETE RESTRICT ON UPDATE RESTRICT;

-- 添加索引
ALTER TABLE `user_career_goal` 
ADD INDEX `idx_career_level_id` (`career_level_id`);

-- =====================================================
-- 第七步：验证数据迁移
-- =====================================================

-- 检查数据迁移结果
SELECT 
    '数据迁移验证' as check_type,
    COUNT(*) as total_records,
    COUNT(career_level_id) as records_with_career_level_id,
    COUNT(*) - COUNT(career_level_id) as null_career_level_id_count
FROM user_career_goal;

-- 检查级别分布
SELECT 
    cl.level_code,
    cl.level_name,
    COUNT(ucg.id) as user_count
FROM user_career_goal ucg
JOIN career_level cl ON ucg.career_level_id = cl.id
GROUP BY cl.level_code, cl.level_name
ORDER BY cl.sort_order;

-- 检查外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_career_goal' 
AND CONSTRAINT_NAME = 'fk_user_career_goal_career_level';

-- =====================================================
-- 第八步：记录迁移完成（暂不删除旧字段）
-- =====================================================

-- 更新迁移日志
UPDATE migration_log 
SET end_time = NOW(), status = 'COMPLETED',
    description = CONCAT(description, ' - 新字段career_level_id已添加，旧字段target_level保留待验证')
WHERE migration_name = 'career-level-association-migration';

-- 提交事务
COMMIT;

-- =====================================================
-- 第九步：清理旧字段（需要单独执行，确认无问题后）
-- =====================================================

/*
-- 在确认新字段工作正常后，执行以下脚本删除旧字段：

START TRANSACTION;

-- 删除旧的target_level字段
ALTER TABLE `user_career_goal` DROP COLUMN `target_level`;

-- 更新迁移日志
UPDATE migration_log 
SET description = CONCAT(description, ' - 旧字段target_level已删除')
WHERE migration_name = 'career-level-association-migration';

COMMIT;
*/

-- =====================================================
-- 回滚脚本（如果需要）
-- =====================================================

/*
-- 如果需要回滚，执行以下脚本：

START TRANSACTION;

-- 删除外键约束
ALTER TABLE `user_career_goal` DROP FOREIGN KEY `fk_user_career_goal_career_level`;

-- 删除索引
ALTER TABLE `user_career_goal` DROP INDEX `idx_career_level_id`;

-- 删除新字段
ALTER TABLE `user_career_goal` DROP COLUMN `career_level_id`;

-- 恢复数据（如果需要）
-- 可以从备份表恢复

-- 更新迁移日志
UPDATE migration_log 
SET end_time = NOW(), status = 'ROLLBACK' 
WHERE migration_name = 'career-level-association-migration';

COMMIT;
*/

-- =====================================================
-- 迁移说明
-- =====================================================

/*
本迁移脚本完成以下任务：

1. 数据备份：创建user_career_goal_level_backup表
2. 添加新字段：career_level_id关联career_level表
3. 数据迁移：将target_level枚举值转换为career_level_id
4. 约束添加：添加外键约束和索引
5. 数据验证：验证迁移后的数据完整性

优势：
- 充分利用career_level表的丰富信息（薪资范围、描述等）
- 支持动态级别管理
- 实现真正的关系型数据库设计
- 支持不同职业的不同级别体系

注意事项：
- 旧字段target_level暂时保留，待验证无问题后再删除
- 确保career_level表有足够的基础数据
- 建议在测试环境充分验证后再在生产环境执行

后续步骤：
1. 更新Java实体类的字段映射
2. 更新相关Service和Repository
3. 更新前端TypeScript接口
4. 验证前后端联调正常
5. 删除旧的target_level字段
*/
