package com.itbook.repository;

import com.itbook.entity.UserAtomicSkillMastery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 用户原子技能掌握度数据访问接口
 * 提供用户技能掌握情况的数据库操作方法
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Repository
public interface UserAtomicSkillMasteryRepository extends JpaRepository<UserAtomicSkillMastery, Long> {

    /**
     * 根据用户ID查找所有技能掌握记录
     */
    List<UserAtomicSkillMastery> findByUserId(Long userId);

    /**
     * 根据用户ID和技能ID查找掌握记录
     */
    Optional<UserAtomicSkillMastery> findByUserIdAndAtomicSkillId(Long userId, Long atomicSkillId);

    /**
     * 根据用户ID和掌握水平查找技能
     */
    List<UserAtomicSkillMastery> findByUserIdAndMasteryLevel(Long userId, UserAtomicSkillMastery.MasteryLevel masteryLevel);

    /**
     * 根据用户ID查找非零掌握水平的技能
     */
    List<UserAtomicSkillMastery> findByUserIdAndMasteryLevelNot(Long userId, UserAtomicSkillMastery.MasteryLevel masteryLevel);

    /**
     * 根据技能ID查找所有用户的掌握记录
     */
    List<UserAtomicSkillMastery> findByAtomicSkillId(Long atomicSkillId);

    /**
     * 根据用户ID和掌握分数范围查找技能
     */
    @Query("SELECT uasm FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND " +
           "uasm.masteryScore BETWEEN :minScore AND :maxScore " +
           "ORDER BY uasm.masteryScore DESC")
    List<UserAtomicSkillMastery> findByUserIdAndMasteryScoreRange(@Param("userId") Long userId,
                                                                 @Param("minScore") BigDecimal minScore,
                                                                 @Param("maxScore") BigDecimal maxScore);

    /**
     * 根据用户ID和置信度范围查找技能
     */
    @Query("SELECT uasm FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND " +
           "uasm.confidenceLevel BETWEEN :minConfidence AND :maxConfidence " +
           "ORDER BY uasm.confidenceLevel DESC")
    List<UserAtomicSkillMastery> findByUserIdAndConfidenceRange(@Param("userId") Long userId,
                                                               @Param("minConfidence") BigDecimal minConfidence,
                                                               @Param("maxConfidence") BigDecimal maxConfidence);

    /**
     * 根据用户ID查找已认证的技能
     */
    List<UserAtomicSkillMastery> findByUserIdAndIsCertified(Long userId, Boolean isCertified);

    /**
     * 根据用户ID查找需要复习的技能
     */
    List<UserAtomicSkillMastery> findByUserIdAndNeedsRefresh(Long userId, Boolean needsRefresh);

    /**
     * 根据用户ID和学习路径ID查找技能
     */
    List<UserAtomicSkillMastery> findByUserIdAndLearnedViaPathId(Long userId, Long pathId);

    /**
     * 获取用户的技能掌握统计
     */
    @Query("SELECT uasm.masteryLevel, COUNT(uasm) FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId GROUP BY uasm.masteryLevel ORDER BY uasm.masteryLevel")
    List<Object[]> getUserMasteryStatistics(@Param("userId") Long userId);

    /**
     * 获取用户的平均掌握分数
     */
    @Query("SELECT AVG(uasm.masteryScore) FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND uasm.masteryLevel != 'NONE'")
    BigDecimal getUserAverageMasteryScore(@Param("userId") Long userId);

    /**
     * 获取用户掌握的技能总数
     */
    @Query("SELECT COUNT(uasm) FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND uasm.masteryLevel != 'NONE'")
    Long getUserMasteredSkillCount(@Param("userId") Long userId);

    /**
     * 获取用户的总学习时长
     */
    @Query("SELECT SUM(uasm.learningHours) FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND uasm.learningHours IS NOT NULL")
    BigDecimal getUserTotalLearningHours(@Param("userId") Long userId);

    /**
     * 获取用户最近练习的技能
     */
    @Query("SELECT uasm FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND uasm.lastPracticedAt IS NOT NULL " +
           "ORDER BY uasm.lastPracticedAt DESC")
    List<UserAtomicSkillMastery> getUserRecentPracticedSkills(@Param("userId") Long userId);

    /**
     * 获取用户最近评估的技能
     */
    @Query("SELECT uasm FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND uasm.lastAssessedAt IS NOT NULL " +
           "ORDER BY uasm.lastAssessedAt DESC")
    List<UserAtomicSkillMastery> getUserRecentAssessedSkills(@Param("userId") Long userId);

    /**
     * 根据技能分类获取用户掌握情况
     */
    @Query("SELECT a.category, AVG(uasm.masteryScore), COUNT(uasm) FROM UserAtomicSkillMastery uasm " +
           "JOIN AtomicSkill a ON uasm.atomicSkillId = a.id WHERE " +
           "uasm.userId = :userId AND uasm.masteryLevel != 'NONE' " +
           "GROUP BY a.category ORDER BY AVG(uasm.masteryScore) DESC")
    List<Object[]> getUserMasteryByCategory(@Param("userId") Long userId);

    /**
     * 根据难度级别获取用户掌握情况
     */
    @Query("SELECT a.difficultyLevel, AVG(uasm.masteryScore), COUNT(uasm) FROM UserAtomicSkillMastery uasm " +
           "JOIN AtomicSkill a ON uasm.atomicSkillId = a.id WHERE " +
           "uasm.userId = :userId AND uasm.masteryLevel != 'NONE' " +
           "GROUP BY a.difficultyLevel ORDER BY a.difficultyLevel")
    List<Object[]> getUserMasteryByDifficulty(@Param("userId") Long userId);

    /**
     * 获取用户强项技能分类
     */
    @Query("SELECT a.category FROM UserAtomicSkillMastery uasm " +
           "JOIN AtomicSkill a ON uasm.atomicSkillId = a.id WHERE " +
           "uasm.userId = :userId AND uasm.masteryScore >= :minScore " +
           "GROUP BY a.category HAVING COUNT(uasm) >= :minSkillCount " +
           "ORDER BY AVG(uasm.masteryScore) DESC")
    List<String> getUserStrongCategories(@Param("userId") Long userId,
                                        @Param("minScore") BigDecimal minScore,
                                        @Param("minSkillCount") Long minSkillCount);

    /**
     * 获取用户弱项技能分类
     */
    @Query("SELECT a.category FROM UserAtomicSkillMastery uasm " +
           "JOIN AtomicSkill a ON uasm.atomicSkillId = a.id WHERE " +
           "uasm.userId = :userId AND uasm.masteryScore < :maxScore " +
           "GROUP BY a.category HAVING COUNT(uasm) >= :minSkillCount " +
           "ORDER BY AVG(uasm.masteryScore) ASC")
    List<String> getUserWeakCategories(@Param("userId") Long userId,
                                      @Param("maxScore") BigDecimal maxScore,
                                      @Param("minSkillCount") Long minSkillCount);

    /**
     * 获取需要复习的技能（基于遗忘衰减）
     */
    @Query("SELECT uasm FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND " +
           "uasm.decayFactor < :decayThreshold AND " +
           "uasm.masteryLevel != 'NONE' " +
           "ORDER BY uasm.decayFactor ASC, uasm.lastPracticedAt ASC")
    List<UserAtomicSkillMastery> getSkillsNeedingRefresh(@Param("userId") Long userId,
                                                        @Param("decayThreshold") BigDecimal decayThreshold);

    /**
     * 获取用户学习连续天数
     */
    @Query("SELECT COUNT(DISTINCT DATE(uasm.lastPracticedAt)) FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND " +
           "uasm.lastPracticedAt >= :startDate AND uasm.lastPracticedAt <= :endDate")
    Long getUserLearningStreak(@Param("userId") Long userId,
                              @Param("startDate") LocalDateTime startDate,
                              @Param("endDate") LocalDateTime endDate);

    /**
     * 根据技能ID集合查找用户掌握情况
     */
    @Query("SELECT uasm FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.userId = :userId AND uasm.atomicSkillId IN :skillIds")
    List<UserAtomicSkillMastery> findByUserIdAndAtomicSkillIdIn(@Param("userId") Long userId,
                                                               @Param("skillIds") Set<Long> skillIds);

    /**
     * 批量更新掌握度
     */
    @Query("UPDATE UserAtomicSkillMastery uasm SET " +
           "uasm.masteryLevel = :masteryLevel, " +
           "uasm.masteryScore = :masteryScore, " +
           "uasm.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE uasm.userId = :userId AND uasm.atomicSkillId IN :skillIds")
    int updateMasteryBatch(@Param("userId") Long userId,
                          @Param("skillIds") Set<Long> skillIds,
                          @Param("masteryLevel") UserAtomicSkillMastery.MasteryLevel masteryLevel,
                          @Param("masteryScore") BigDecimal masteryScore);

    /**
     * 更新最后练习时间
     */
    @Query("UPDATE UserAtomicSkillMastery uasm SET " +
           "uasm.lastPracticedAt = :practiceTime, " +
           "uasm.practiceCount = uasm.practiceCount + 1, " +
           "uasm.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE uasm.userId = :userId AND uasm.atomicSkillId = :skillId")
    int updateLastPracticeTime(@Param("userId") Long userId,
                              @Param("skillId") Long skillId,
                              @Param("practiceTime") LocalDateTime practiceTime);

    /**
     * 更新学习时长
     */
    @Query("UPDATE UserAtomicSkillMastery uasm SET " +
           "uasm.learningHours = uasm.learningHours + :additionalHours, " +
           "uasm.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE uasm.userId = :userId AND uasm.atomicSkillId = :skillId")
    int addLearningHours(@Param("userId") Long userId,
                        @Param("skillId") Long skillId,
                        @Param("additionalHours") BigDecimal additionalHours);

    /**
     * 获取技能的平均掌握分数
     */
    @Query("SELECT AVG(uasm.masteryScore) FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.atomicSkillId = :skillId AND uasm.masteryLevel != 'NONE'")
    BigDecimal getSkillAverageMasteryScore(@Param("skillId") Long skillId);

    /**
     * 获取技能的掌握人数
     */
    @Query("SELECT COUNT(uasm) FROM UserAtomicSkillMastery uasm WHERE " +
           "uasm.atomicSkillId = :skillId AND uasm.masteryLevel != 'NONE'")
    Long getSkillMasteryCount(@Param("skillId") Long skillId);

    /**
     * 删除用户的所有掌握记录
     */
    void deleteByUserId(Long userId);

    /**
     * 删除技能的所有掌握记录
     */
    void deleteByAtomicSkillId(Long atomicSkillId);
}
