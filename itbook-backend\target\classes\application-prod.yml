# Production环境配置 - 生产部署环境
# 用于生产部署，优化性能和安全配置

spring:
  # 数据库配置 - 连接MySQL生产数据库
  datasource:
    url: "****************************************************************************************************************************************************************************************************************************************************************"
    username: "root"
    password: "NW1M5@18N1YYzNlNz"
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 30
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      connection-test-query: SELECT 1
  
  # JPA配置 - 生产环境验证表结构
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        '[format_sql]': false
        '[use_sql_comments]': false
        '[generate_statistics]': false
        jdbc:
          '[batch_size]': 50
        '[order_inserts]': true
        '[order_updates]': true
        '[batch_versioned_data]': true

# Production环境日志配置
logging:
  level:
    '[com.itbook]': INFO
    '[org.springframework.security]': WARN
    '[org.springframework.web]': WARN
    '[org.hibernate.SQL]': WARN
    '[org.springframework.transaction]': WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/itbook/itbook-production.log
    max-size: 500MB
    max-history: 90
    total-size-cap: 10GB

# Production环境应用配置
app:
  environment: production

  # 生产环境服务器配置
  server:
    # 生产环境基础URL - 从环境变量读取，支持HTTPS
    base-url: "${APP_SERVER_BASE_URL:https://api.itbook.com}"

  features:
    enable-analytics: true
    enable-monitoring: true
    enable-cache: true
    enable-rate-limiting: true
  
  # 生产环境安全配置
  security:
    cors:
      allowed-origins: "${CORS_ALLOWED_ORIGINS:https://itbook.com,https://www.itbook.com}"
      allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
      allowed-headers: "*"
      allow-credentials: true
    
    # 生产环境强密码策略
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-numbers: true
      require-special-chars: true

# JWT配置 - 生产环境使用环境变量
jwt:
  secret: "${JWT_SECRET:production-jwt-secret-key-must-be-set-via-environment-variable}"
  expiration: 86400000  # 24小时
  refresh-expiration: 604800000  # 7天

# API文档配置 - 生产环境禁用
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# 生产环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
  health:
    db:
      enabled: true
    diskspace:
      enabled: true

# 生产环境服务器配置
server:
  port: 8888
  servlet:
    context-path: /api
  tomcat:
    threads:
      max: 200
      min-spare: 20
    connection-timeout: 20000
    max-connections: 8192
    accept-count: 100
  compression:
    enabled: true
    min-response-size: 2048
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json

# 生产环境缓存配置
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=1800s

# 生产环境文件上传配置
spring.servlet.multipart:
  max-file-size: 10MB
  max-request-size: 10MB
