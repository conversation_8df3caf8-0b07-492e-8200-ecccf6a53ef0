@echo off
REM ITBook原子技能系统数据备份脚本 (Windows版本)
REM 作者: ITBook Team
REM 版本: 1.0
REM 创建时间: 2025-07-21

setlocal enabledelayedexpansion

REM 配置变量
set DB_USER=root
set DB_PASS=NW1M5@18N1YYzNlNz
set DB_NAME=itbook_dev
set BACKUP_DIR=backup
set DATE=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set DATE=%DATE: =0%
set LOG_FILE=%BACKUP_DIR%\backup.log

REM 创建备份目录
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
if not exist "%BACKUP_DIR%\full" mkdir "%BACKUP_DIR%\full"
if not exist "%BACKUP_DIR%\realtime" mkdir "%BACKUP_DIR%\realtime"
if not exist "%BACKUP_DIR%\incremental" mkdir "%BACKUP_DIR%\incremental"

REM 日志函数
:log
echo [%date% %time%] %~1 >> "%LOG_FILE%"
echo [%date% %time%] %~1
goto :eof

REM 测试数据库连接
:test_connection
call :log "测试数据库连接..."
mysql -u %DB_USER% -p%DB_PASS% %DB_NAME% -e "SELECT 1;" >nul 2>&1
if %errorlevel% equ 0 (
    call :log "数据库连接正常"
    exit /b 0
) else (
    call :log "错误: 数据库连接失败"
    exit /b 1
)

REM 全量备份函数
:full_backup
call :log "开始全量备份..."

set BACKUP_FILE=%BACKUP_DIR%\full\%DB_NAME%_%DATE%.sql

mysqldump -u %DB_USER% -p%DB_PASS% --single-transaction --routines --triggers --events --hex-blob --default-character-set=utf8mb4 --add-drop-table --create-options --disable-keys --extended-insert --quick --lock-tables=false %DB_NAME% > "%BACKUP_FILE%" 2>>"%LOG_FILE%"

if %errorlevel% equ 0 (
    call :log "全量备份成功: %BACKUP_FILE%"
    
    REM 显示备份文件大小
    for %%A in ("%BACKUP_FILE%") do (
        call :log "备份文件大小: %%~zA bytes"
    )
    
    exit /b 0
) else (
    call :log "错误: 全量备份失败"
    exit /b 1
)

REM 核心表备份函数
:core_tables_backup
call :log "开始核心表备份..."

REM 原子技能系统核心表
set TABLES=atomic_skill skill_relationship user_atomic_skill_mastery dynamic_learning_path dynamic_path_step career_skill_mapping skill_assessment_record

for %%T in (%TABLES%) do (
    set BACKUP_FILE=%BACKUP_DIR%\realtime\%%T_%DATE%.sql
    
    mysqldump -u %DB_USER% -p%DB_PASS% --single-transaction --add-drop-table --create-options --disable-keys --extended-insert --quick %DB_NAME% %%T > "!BACKUP_FILE!" 2>>"%LOG_FILE%"
    
    if !errorlevel! equ 0 (
        call :log "表 %%T 备份成功"
    ) else (
        call :log "错误: 表 %%T 备份失败"
    )
)

exit /b 0

REM 备份状态检查函数
:check_backup_status
call :log "检查备份状态..."

REM 统计全量备份文件
set FULL_COUNT=0
for %%F in ("%BACKUP_DIR%\full\*.sql") do (
    set /a FULL_COUNT+=1
)
call :log "全量备份文件数量: %FULL_COUNT%"

REM 统计实时备份文件
set REAL_COUNT=0
for %%F in ("%BACKUP_DIR%\realtime\*.sql") do (
    set /a REAL_COUNT+=1
)
call :log "实时备份文件数量: %REAL_COUNT%"

exit /b 0

REM 清理旧备份函数
:cleanup_old_backups
call :log "开始清理旧备份文件..."

REM 删除7天前的文件 (Windows下使用forfiles命令)
forfiles /p "%BACKUP_DIR%\full" /s /m *.sql /d -7 /c "cmd /c del @path" 2>nul
forfiles /p "%BACKUP_DIR%\realtime" /s /m *.sql /d -1 /c "cmd /c del @path" 2>nul
forfiles /p "%BACKUP_DIR%\incremental" /s /m *.sql /d -7 /c "cmd /c del @path" 2>nul

call :log "旧备份文件清理完成"
exit /b 0

REM 验证备份文件函数
:verify_backup
set BACKUP_FILE=%~1

if not exist "%BACKUP_FILE%" (
    call :log "错误: 备份文件不存在 - %BACKUP_FILE%"
    exit /b 1
)

for %%A in ("%BACKUP_FILE%") do (
    if %%~zA lss 1000 (
        call :log "错误: 备份文件过小 - %BACKUP_FILE% (%%~zA bytes)"
        exit /b 1
    ) else (
        call :log "备份文件验证通过 - %BACKUP_FILE% (%%~zA bytes)"
        exit /b 0
    )
)

REM 显示帮助信息
:show_help
echo ITBook数据备份脚本 (Windows版本)
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   full        执行全量备份
echo   core        执行核心表备份
echo   cleanup     清理旧备份文件
echo   status      检查备份状态
echo   verify FILE 验证备份文件
echo   test        测试数据库连接
echo   help        显示此帮助信息
echo.
echo 示例:
echo   %~nx0 full                    # 执行全量备份
echo   %~nx0 core                    # 执行核心表备份
echo   %~nx0 verify backup.sql       # 验证备份文件
goto :eof

REM 主执行逻辑
if "%~1"=="" (
    call :show_help
    exit /b 1
)

REM 创建日志文件
if not exist "%LOG_FILE%" (
    echo. > "%LOG_FILE%"
)

if /i "%~1"=="full" (
    call :log "=== 开始全量备份任务 ==="
    call :test_connection
    if !errorlevel! equ 0 (
        call :full_backup
        set EXIT_CODE=!errorlevel!
    ) else (
        set EXIT_CODE=1
    )
    call :log "=== 全量备份任务结束 ==="
) else if /i "%~1"=="core" (
    call :log "=== 开始核心表备份任务 ==="
    call :test_connection
    if !errorlevel! equ 0 (
        call :core_tables_backup
        set EXIT_CODE=!errorlevel!
    ) else (
        set EXIT_CODE=1
    )
    call :log "=== 核心表备份任务结束 ==="
) else if /i "%~1"=="cleanup" (
    call :log "=== 开始清理任务 ==="
    call :cleanup_old_backups
    set EXIT_CODE=!errorlevel!
    call :log "=== 清理任务结束 ==="
) else if /i "%~1"=="status" (
    call :log "=== 备份状态检查 ==="
    call :check_backup_status
    set EXIT_CODE=!errorlevel!
    call :log "=== 状态检查结束 ==="
) else if /i "%~1"=="verify" (
    if "%~2"=="" (
        echo 错误: 请指定要验证的备份文件
        exit /b 1
    )
    call :verify_backup "%~2"
    set EXIT_CODE=!errorlevel!
) else if /i "%~1"=="test" (
    call :test_connection
    set EXIT_CODE=!errorlevel!
) else if /i "%~1"=="help" (
    call :show_help
    set EXIT_CODE=0
) else (
    echo 错误: 未知选项 '%~1'
    call :show_help
    set EXIT_CODE=1
)

exit /b %EXIT_CODE%
