package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.*;
import com.itbook.service.LearningProgressTracker.LearningEvent;
import com.itbook.service.LearningProgressTracker.LearningEventType;
import com.itbook.service.LearningProgressTracker.LearningBehaviorAnalysis;
import com.itbook.service.PathAdjustmentEngine.PathAdjustmentRequest;
import com.itbook.service.PathAdjustmentEngine.PathAdjustmentResult;
import com.itbook.service.PathAdjustmentEngine.AdjustmentTrigger;
import com.itbook.service.PathAdjustmentEngine.AdjustmentStrategy;
import com.itbook.service.FeedbackProcessor.UserFeedback;
import com.itbook.service.FeedbackProcessor.FeedbackType;
import com.itbook.service.FeedbackProcessor.FeedbackSentiment;
import com.itbook.service.FeedbackProcessor.FeedbackAnalysis;
import com.itbook.service.EffectivenessEvaluator.EffectivenessEvaluation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态调整统一API控制器
 * 
 * 整合学习进度跟踪、路径调整、用户反馈处理、学习效果评估等功能，
 * 提供完整的动态学习路径调整API服务
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/dynamic-adjustment")
@RequiredArgsConstructor
@Tag(name = "动态调整", description = "动态学习路径调整统一API接口")
public class DynamicAdjustmentController {

    private final LearningProgressTracker learningProgressTracker;
    private final PathAdjustmentEngine pathAdjustmentEngine;
    private final FeedbackProcessor feedbackProcessor;
    private final EffectivenessEvaluator effectivenessEvaluator;

    // ==================== 学习进度跟踪相关API ====================

    /**
     * 记录学习行为事件
     */
    @PostMapping("/learning-events")
    @Operation(summary = "记录学习行为事件", description = "记录用户的学习行为事件，用于进度跟踪和行为分析")
    public ResponseEntity<ApiResponse<Map<String, Object>>> recordLearningEvent(
            @Parameter(description = "学习事件数据", required = true)
            @Valid @RequestBody LearningEventRequest request) {
        
        log.info("📝 记录学习行为事件: userId={}, eventType={}, stepId={}", 
                request.getUserId(), request.getEventType(), request.getStepId());
        
        try {
            // 构建学习事件
            LearningEvent event = new LearningEvent();
            event.setUserId(request.getUserId());
            event.setPathId(request.getPathId());
            event.setStepId(request.getStepId());
            event.setEventType(LearningEventType.valueOf(request.getEventType()));
            event.setTimestamp(LocalDateTime.now());
            event.setEventData(request.getEventData() != null ? request.getEventData() : new HashMap<>());

            // 记录事件
            learningProgressTracker.recordLearningEvent(event);

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("eventId", System.currentTimeMillis());
            responseData.put("userId", request.getUserId());
            responseData.put("eventType", request.getEventType());
            responseData.put("timestamp", event.getTimestamp());
            responseData.put("status", "recorded");

            return ResponseEntity.ok(ApiResponse.success("学习行为事件记录成功", responseData));

        } catch (Exception e) {
            log.error("❌ 学习行为事件记录失败: userId={}, eventType={}", 
                    request.getUserId(), request.getEventType(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习行为事件记录失败: " + e.getMessage()));
        }
    }

    /**
     * 分析用户学习行为
     */
    @GetMapping("/users/{userId}/paths/{pathId}/behavior-analysis")
    @Operation(summary = "分析用户学习行为", description = "分析用户在特定学习路径中的学习行为和模式")
    public ResponseEntity<ApiResponse<LearningBehaviorAnalysis>> analyzeLearningBehavior(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("🔍 分析用户学习行为: userId={}, pathId={}", userId, pathId);
        
        try {
            LearningBehaviorAnalysis analysis = learningProgressTracker.analyzeLearningBehavior(userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("学习行为分析完成", analysis));

        } catch (Exception e) {
            log.error("❌ 学习行为分析失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习行为分析失败: " + e.getMessage()));
        }
    }

    // ==================== 路径调整相关API ====================

    /**
     * 执行路径调整
     */
    @PostMapping("/path-adjustment")
    @Operation(summary = "执行路径调整", description = "基于用户学习情况和反馈调整学习路径")
    public ResponseEntity<ApiResponse<PathAdjustmentResult>> adjustPath(
            @Parameter(description = "路径调整请求", required = true)
            @Valid @RequestBody PathAdjustmentRequest request) {
        
        log.info("🔧 执行路径调整: userId={}, pathId={}, trigger={}", 
                request.getUserId(), request.getPathId(), request.getTrigger());
        
        try {
            PathAdjustmentResult result = pathAdjustmentEngine.adjustPath(request);
            return ResponseEntity.ok(ApiResponse.success("路径调整完成", result));

        } catch (Exception e) {
            log.error("❌ 路径调整失败: userId={}, pathId={}", 
                    request.getUserId(), request.getPathId(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径调整失败: " + e.getMessage()));
        }
    }

    /**
     * 自动路径调整
     */
    @PostMapping("/users/{userId}/paths/{pathId}/auto-adjust")
    @Operation(summary = "自动路径调整", description = "自动检测并执行路径调整")
    public ResponseEntity<ApiResponse<List<PathAdjustmentResult>>> autoAdjustPath(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("🤖 自动路径调整: userId={}, pathId={}", userId, pathId);
        
        try {
            List<PathAdjustmentResult> results = pathAdjustmentEngine.autoAdjustPath(userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("自动路径调整完成", results));

        } catch (Exception e) {
            log.error("❌ 自动路径调整失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("自动路径调整失败: " + e.getMessage()));
        }
    }

    /**
     * 评估调整必要性
     */
    @GetMapping("/users/{userId}/paths/{pathId}/adjustment-evaluation")
    @Operation(summary = "评估调整必要性", description = "评估学习路径是否需要调整")
    public ResponseEntity<ApiResponse<Map<String, Object>>> evaluateAdjustmentNeed(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 评估路径调整必要性: userId={}, pathId={}", userId, pathId);
        
        try {
            Map<String, Object> evaluation = pathAdjustmentEngine.evaluateAdjustmentNeed(userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("路径调整必要性评估完成", evaluation));

        } catch (Exception e) {
            log.error("❌ 路径调整必要性评估失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径调整必要性评估失败: " + e.getMessage()));
        }
    }

    // ==================== 用户反馈相关API ====================

    /**
     * 提交用户反馈
     */
    @PostMapping("/feedback")
    @Operation(summary = "提交用户反馈", description = "收集用户对学习内容的反馈")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitFeedback(
            @Parameter(description = "用户反馈数据", required = true)
            @Valid @RequestBody FeedbackRequest request) {
        
        log.info("📝 提交用户反馈: userId={}, pathId={}, stepId={}, type={}", 
                request.getUserId(), request.getPathId(), request.getStepId(), request.getFeedbackType());
        
        try {
            // 构建反馈对象
            UserFeedback feedback = new UserFeedback();
            feedback.setUserId(request.getUserId());
            feedback.setPathId(request.getPathId());
            feedback.setStepId(request.getStepId());
            feedback.setFeedbackType(FeedbackType.valueOf(request.getFeedbackType()));
            feedback.setRating(request.getRating());
            feedback.setComment(request.getComment());
            
            if (request.getSentiment() != null) {
                feedback.setSentiment(FeedbackSentiment.valueOf(request.getSentiment()));
            }
            
            if (request.getMetadata() != null) {
                feedback.setMetadata(request.getMetadata());
            }

            // 处理反馈
            Map<String, Object> result = feedbackProcessor.processFeedback(feedback);
            return ResponseEntity.ok(ApiResponse.success("用户反馈提交成功", result));

        } catch (Exception e) {
            log.error("❌ 用户反馈提交失败: userId={}, pathId={}", 
                    request.getUserId(), request.getPathId(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("用户反馈提交失败: " + e.getMessage()));
        }
    }

    /**
     * 分析路径反馈
     */
    @GetMapping("/paths/{pathId}/feedback-analysis")
    @Operation(summary = "分析路径反馈", description = "分析特定学习路径的用户反馈")
    public ResponseEntity<ApiResponse<FeedbackAnalysis>> analyzeFeedback(
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 分析路径反馈: pathId={}", pathId);
        
        try {
            FeedbackAnalysis analysis = feedbackProcessor.analyzeFeedback(pathId);
            return ResponseEntity.ok(ApiResponse.success("路径反馈分析完成", analysis));

        } catch (Exception e) {
            log.error("❌ 路径反馈分析失败: pathId={}", pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径反馈分析失败: " + e.getMessage()));
        }
    }

    // ==================== 学习效果评估相关API ====================

    /**
     * 评估学习效果
     */
    @GetMapping("/users/{userId}/paths/{pathId}/effectiveness-evaluation")
    @Operation(summary = "评估学习效果", description = "评估用户在特定学习路径中的学习效果")
    public ResponseEntity<ApiResponse<EffectivenessEvaluation>> evaluateEffectiveness(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 评估学习效果: userId={}, pathId={}", userId, pathId);
        
        try {
            EffectivenessEvaluation evaluation = effectivenessEvaluator.evaluateEffectiveness(userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("学习效果评估完成", evaluation));

        } catch (Exception e) {
            log.error("❌ 学习效果评估失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习效果评估失败: " + e.getMessage()));
        }
    }

    /**
     * 获取评估摘要
     */
    @GetMapping("/users/{userId}/evaluation-summary")
    @Operation(summary = "获取评估摘要", description = "获取用户的学习效果评估摘要统计")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEvaluationSummary(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        log.info("📈 获取评估摘要: userId={}", userId);
        
        try {
            Map<String, Object> summary = effectivenessEvaluator.getEvaluationSummary(userId);
            return ResponseEntity.ok(ApiResponse.success("评估摘要获取成功", summary));

        } catch (Exception e) {
            log.error("❌ 评估摘要获取失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("评估摘要获取失败: " + e.getMessage()));
        }
    }

    // ==================== 综合分析API ====================

    /**
     * 综合分析用户学习状况
     */
    @GetMapping("/users/{userId}/paths/{pathId}/comprehensive-analysis")
    @Operation(summary = "综合分析用户学习状况", description = "综合分析用户的学习行为、反馈、效果等，提供全面的学习状况报告")
    public ResponseEntity<ApiResponse<Map<String, Object>>> comprehensiveAnalysis(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {

        log.info("🔍 综合分析用户学习状况: userId={}, pathId={}", userId, pathId);

        try {
            Map<String, Object> comprehensiveReport = new HashMap<>();

            // 1. 学习行为分析
            LearningBehaviorAnalysis behaviorAnalysis = learningProgressTracker.analyzeLearningBehavior(userId, pathId);
            comprehensiveReport.put("behaviorAnalysis", behaviorAnalysis);

            // 2. 反馈分析
            FeedbackAnalysis feedbackAnalysis = feedbackProcessor.analyzeFeedback(pathId);
            comprehensiveReport.put("feedbackAnalysis", feedbackAnalysis);

            // 3. 学习效果评估
            EffectivenessEvaluation effectivenessEvaluation = effectivenessEvaluator.evaluateEffectiveness(userId, pathId);
            comprehensiveReport.put("effectivenessEvaluation", effectivenessEvaluation);

            // 4. 调整建议评估
            Map<String, Object> adjustmentEvaluation = pathAdjustmentEngine.evaluateAdjustmentNeed(userId, pathId);
            comprehensiveReport.put("adjustmentEvaluation", adjustmentEvaluation);

            // 5. 综合建议
            Map<String, Object> recommendations = generateComprehensiveRecommendations(
                    behaviorAnalysis, feedbackAnalysis, effectivenessEvaluation, adjustmentEvaluation);
            comprehensiveReport.put("recommendations", recommendations);

            // 6. 报告元数据
            comprehensiveReport.put("analysisTime", LocalDateTime.now());
            comprehensiveReport.put("userId", userId);
            comprehensiveReport.put("pathId", pathId);

            log.info("✅ 综合分析完成: userId={}, pathId={}", userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("综合分析完成", comprehensiveReport));

        } catch (Exception e) {
            log.error("❌ 综合分析失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("综合分析失败: " + e.getMessage()));
        }
    }

    /**
     * 智能调整建议
     */
    @PostMapping("/users/{userId}/paths/{pathId}/smart-adjustment")
    @Operation(summary = "智能调整建议", description = "基于综合分析结果，提供智能的路径调整建议")
    public ResponseEntity<ApiResponse<Map<String, Object>>> smartAdjustment(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId,
            @Parameter(description = "调整选项", required = false) @RequestBody(required = false) SmartAdjustmentRequest request) {

        log.info("🧠 智能调整建议: userId={}, pathId={}", userId, pathId);

        try {
            Map<String, Object> smartReport = new HashMap<>();

            // 1. 执行综合分析
            LearningBehaviorAnalysis behaviorAnalysis = learningProgressTracker.analyzeLearningBehavior(userId, pathId);
            EffectivenessEvaluation effectivenessEvaluation = effectivenessEvaluator.evaluateEffectiveness(userId, pathId);

            // 2. 生成智能建议
            Map<String, Object> intelligentSuggestions = generateIntelligentSuggestions(
                    behaviorAnalysis, effectivenessEvaluation, request);
            smartReport.put("intelligentSuggestions", intelligentSuggestions);

            // 3. 如果需要，自动执行调整
            if (request != null && request.isAutoExecute()) {
                List<PathAdjustmentResult> adjustmentResults = pathAdjustmentEngine.autoAdjustPath(userId, pathId);
                smartReport.put("adjustmentResults", adjustmentResults);
                smartReport.put("autoExecuted", true);
            } else {
                smartReport.put("autoExecuted", false);
            }

            // 4. 预期效果预测
            Map<String, Object> expectedOutcomes = predictExpectedOutcomes(behaviorAnalysis, effectivenessEvaluation);
            smartReport.put("expectedOutcomes", expectedOutcomes);

            smartReport.put("analysisTime", LocalDateTime.now());
            smartReport.put("userId", userId);
            smartReport.put("pathId", pathId);

            log.info("✅ 智能调整建议完成: userId={}, pathId={}", userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("智能调整建议完成", smartReport));

        } catch (Exception e) {
            log.error("❌ 智能调整建议失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("智能调整建议失败: " + e.getMessage()));
        }
    }

    /**
     * 获取系统状态
     */
    @GetMapping("/system-status")
    @Operation(summary = "获取系统状态", description = "获取动态调整系统的运行状态和统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemStatus() {

        log.info("📊 获取系统状态");

        try {
            Map<String, Object> systemStatus = new HashMap<>();

            // 系统基本信息
            systemStatus.put("systemName", "ITBook动态调整引擎");
            systemStatus.put("version", "1.0.0");
            systemStatus.put("status", "RUNNING");
            systemStatus.put("uptime", LocalDateTime.now());

            // 功能模块状态
            Map<String, String> moduleStatus = new HashMap<>();
            moduleStatus.put("learningProgressTracker", "ACTIVE");
            moduleStatus.put("pathAdjustmentEngine", "ACTIVE");
            moduleStatus.put("feedbackProcessor", "ACTIVE");
            moduleStatus.put("effectivenessEvaluator", "ACTIVE");
            systemStatus.put("modules", moduleStatus);

            // 统计信息（模拟数据）
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalUsers", 1250);
            statistics.put("activePaths", 3420);
            statistics.put("adjustmentsToday", 156);
            statistics.put("feedbacksToday", 89);
            statistics.put("averageEffectiveness", 0.78);
            systemStatus.put("statistics", statistics);

            return ResponseEntity.ok(ApiResponse.success("系统状态获取成功", systemStatus));

        } catch (Exception e) {
            log.error("❌ 系统状态获取失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("系统状态获取失败: " + e.getMessage()));
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 生成综合建议
     */
    private Map<String, Object> generateComprehensiveRecommendations(
            LearningBehaviorAnalysis behaviorAnalysis,
            FeedbackAnalysis feedbackAnalysis,
            EffectivenessEvaluation effectivenessEvaluation,
            Map<String, Object> adjustmentEvaluation) {

        Map<String, Object> recommendations = new HashMap<>();

        // 基于学习行为的建议
        if ("HIGH".equals(behaviorAnalysis.getRiskLevel())) {
            recommendations.put("priority", "HIGH");
            recommendations.put("primaryAction", "立即调整学习策略");
        } else if ("MEDIUM".equals(behaviorAnalysis.getRiskLevel())) {
            recommendations.put("priority", "MEDIUM");
            recommendations.put("primaryAction", "优化学习计划");
        } else {
            recommendations.put("priority", "LOW");
            recommendations.put("primaryAction", "保持当前学习状态");
        }

        // 基于效果评估的建议
        if (effectivenessEvaluation.getOverallEffectiveness() < 0.6) {
            recommendations.put("effectivenessAction", "需要重新设计学习路径");
        } else if (effectivenessEvaluation.getOverallEffectiveness() < 0.8) {
            recommendations.put("effectivenessAction", "适当调整学习内容");
        } else {
            recommendations.put("effectivenessAction", "继续当前学习路径");
        }

        return recommendations;
    }

    /**
     * 生成智能建议
     */
    private Map<String, Object> generateIntelligentSuggestions(
            LearningBehaviorAnalysis behaviorAnalysis,
            EffectivenessEvaluation effectivenessEvaluation,
            SmartAdjustmentRequest request) {

        Map<String, Object> suggestions = new HashMap<>();

        // AI驱动的个性化建议
        suggestions.put("learningStyleOptimization", "基于您的学习模式，建议采用" + behaviorAnalysis.getLearningPattern() + "式学习");
        suggestions.put("difficultyAdjustment", "当前难度感知为" + effectivenessEvaluation.getOverallEffectiveness() + "，建议适当调整");
        suggestions.put("timeManagement", "建议每日学习时间控制在" + behaviorAnalysis.getAverageSessionDuration() + "分钟左右");

        return suggestions;
    }

    /**
     * 预测预期效果
     */
    private Map<String, Object> predictExpectedOutcomes(
            LearningBehaviorAnalysis behaviorAnalysis,
            EffectivenessEvaluation effectivenessEvaluation) {

        Map<String, Object> outcomes = new HashMap<>();

        // 基于当前数据预测未来效果
        double currentEffectiveness = effectivenessEvaluation.getOverallEffectiveness();
        double predictedImprovement = Math.min(0.95, currentEffectiveness + 0.15);

        outcomes.put("predictedEffectiveness", predictedImprovement);
        outcomes.put("estimatedCompletionTime", behaviorAnalysis.getEstimatedRemainingDays());
        outcomes.put("confidenceLevel", 0.85);

        return outcomes;
    }

    // ==================== 请求参数类 ====================

    /**
     * 学习事件请求参数
     */
    public static class LearningEventRequest {
        private Long userId;
        private Long pathId;
        private Long stepId;
        private String eventType;
        private Map<String, Object> eventData;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }

        public Long getStepId() { return stepId; }
        public void setStepId(Long stepId) { this.stepId = stepId; }

        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }

        public Map<String, Object> getEventData() { return eventData; }
        public void setEventData(Map<String, Object> eventData) { this.eventData = eventData; }
    }

    /**
     * 反馈请求参数
     */
    public static class FeedbackRequest {
        private Long userId;
        private Long pathId;
        private Long stepId;
        private String feedbackType;
        private Integer rating;
        private String comment;
        private String sentiment;
        private Map<String, Object> metadata;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }

        public Long getStepId() { return stepId; }
        public void setStepId(Long stepId) { this.stepId = stepId; }

        public String getFeedbackType() { return feedbackType; }
        public void setFeedbackType(String feedbackType) { this.feedbackType = feedbackType; }

        public Integer getRating() { return rating; }
        public void setRating(Integer rating) { this.rating = rating; }

        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }

        public String getSentiment() { return sentiment; }
        public void setSentiment(String sentiment) { this.sentiment = sentiment; }

        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 智能调整请求参数
     */
    public static class SmartAdjustmentRequest {
        private boolean autoExecute = false;
        private String preferredStrategy;
        private Map<String, Object> options;

        // Getters and Setters
        public boolean isAutoExecute() { return autoExecute; }
        public void setAutoExecute(boolean autoExecute) { this.autoExecute = autoExecute; }

        public String getPreferredStrategy() { return preferredStrategy; }
        public void setPreferredStrategy(String preferredStrategy) { this.preferredStrategy = preferredStrategy; }

        public Map<String, Object> getOptions() { return options; }
        public void setOptions(Map<String, Object> options) { this.options = options; }
    }
}
