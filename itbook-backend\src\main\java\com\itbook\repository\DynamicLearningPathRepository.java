package com.itbook.repository;

import com.itbook.entity.DynamicLearningPath;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 动态学习路径数据访问接口
 * 提供动态学习路径的数据库操作方法
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Repository
public interface DynamicLearningPathRepository extends JpaRepository<DynamicLearningPath, Long> {

    /**
     * 根据用户ID查找学习路径
     */
    List<DynamicLearningPath> findByUserId(Long userId);

    /**
     * 根据用户ID和状态查找学习路径
     */
    List<DynamicLearningPath> findByUserIdAndStatus(Long userId, DynamicLearningPath.Status status);

    /**
     * 根据职业目标ID查找学习路径
     */
    List<DynamicLearningPath> findByCareerGoalId(Long careerGoalId);

    /**
     * 根据基础路径ID查找学习路径
     */
    List<DynamicLearningPath> findByBasePathId(Long basePathId);

    /**
     * 根据路径类型查找学习路径
     */
    List<DynamicLearningPath> findByPathType(DynamicLearningPath.PathType pathType);

    /**
     * 根据学习风格查找学习路径
     */
    List<DynamicLearningPath> findByLearningStyle(DynamicLearningPath.LearningStyle learningStyle);

    /**
     * 根据用户ID分页查询学习路径
     */
    Page<DynamicLearningPath> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户ID查找进行中的学习路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.userId = :userId AND dlp.status = 'ACTIVE' " +
           "ORDER BY dlp.updatedAt DESC")
    List<DynamicLearningPath> findActivePathsByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查找已完成的学习路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.userId = :userId AND dlp.status = 'COMPLETED' " +
           "ORDER BY dlp.completedAt DESC")
    List<DynamicLearningPath> findCompletedPathsByUserId(@Param("userId") Long userId);

    /**
     * 根据质量分数范围查找学习路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.qualityScore BETWEEN :minScore AND :maxScore " +
           "ORDER BY dlp.qualityScore DESC")
    List<DynamicLearningPath> findByQualityScoreRange(@Param("minScore") BigDecimal minScore,
                                                      @Param("maxScore") BigDecimal maxScore);

    /**
     * 根据完成率范围查找学习路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.completionRate BETWEEN :minRate AND :maxRate " +
           "ORDER BY dlp.completionRate DESC")
    List<DynamicLearningPath> findByCompletionRateRange(@Param("minRate") BigDecimal minRate,
                                                        @Param("maxRate") BigDecimal maxRate);

    /**
     * 根据预计时长范围查找学习路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.estimatedHours BETWEEN :minHours AND :maxHours " +
           "ORDER BY dlp.estimatedHours ASC")
    List<DynamicLearningPath> findByEstimatedHoursRange(@Param("minHours") Integer minHours,
                                                        @Param("maxHours") Integer maxHours);

    /**
     * 查找模板路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.isTemplate = true AND dlp.status = 'ACTIVE' " +
           "ORDER BY dlp.qualityScore DESC, dlp.createdAt DESC")
    List<DynamicLearningPath> findTemplates();

    /**
     * 根据生成算法查找学习路径
     */
    List<DynamicLearningPath> findByGenerationAlgorithm(String generationAlgorithm);

    /**
     * 获取用户的学习路径统计
     */
    @Query("SELECT dlp.status, COUNT(dlp) FROM DynamicLearningPath dlp WHERE " +
           "dlp.userId = :userId GROUP BY dlp.status ORDER BY dlp.status")
    List<Object[]> getUserPathStatistics(@Param("userId") Long userId);

    /**
     * 获取用户的平均完成率
     */
    @Query("SELECT AVG(dlp.completionRate) FROM DynamicLearningPath dlp WHERE " +
           "dlp.userId = :userId AND dlp.completionRate > 0")
    BigDecimal getUserAverageCompletionRate(@Param("userId") Long userId);

    /**
     * 获取用户的总学习技能数
     */
    @Query("SELECT SUM(dlp.totalSkills) FROM DynamicLearningPath dlp WHERE " +
           "dlp.userId = :userId AND dlp.status != 'ABANDONED'")
    Long getUserTotalSkills(@Param("userId") Long userId);

    /**
     * 获取用户的总预计学习时长
     */
    @Query("SELECT SUM(dlp.estimatedHours) FROM DynamicLearningPath dlp WHERE " +
           "dlp.userId = :userId AND dlp.status != 'ABANDONED'")
    Long getUserTotalEstimatedHours(@Param("userId") Long userId);

    /**
     * 查找高质量路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.qualityScore >= :minQualityScore " +
           "ORDER BY dlp.qualityScore DESC, dlp.completionRate DESC")
    List<DynamicLearningPath> findHighQualityPaths(@Param("minQualityScore") BigDecimal minQualityScore);

    /**
     * 查找推荐路径（基于质量和完成率）
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.qualityScore >= :minQualityScore AND " +
           "dlp.completionRate >= :minCompletionRate AND " +
           "dlp.status = 'ACTIVE' " +
           "ORDER BY dlp.qualityScore DESC, dlp.completionRate DESC")
    List<DynamicLearningPath> findRecommendedPaths(@Param("minQualityScore") BigDecimal minQualityScore,
                                                   @Param("minCompletionRate") BigDecimal minCompletionRate);

    /**
     * 根据职业目标查找相似路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.careerGoalId = :careerGoalId AND " +
           "dlp.userId != :excludeUserId AND " +
           "dlp.status = 'COMPLETED' " +
           "ORDER BY dlp.qualityScore DESC, dlp.completionRate DESC")
    List<DynamicLearningPath> findSimilarPathsByCareerGoal(@Param("careerGoalId") Long careerGoalId,
                                                           @Param("excludeUserId") Long excludeUserId);

    /**
     * 根据学习风格查找相似路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.learningStyle = :learningStyle AND " +
           "dlp.userId != :excludeUserId AND " +
           "dlp.status = 'COMPLETED' " +
           "ORDER BY dlp.qualityScore DESC, dlp.completionRate DESC")
    List<DynamicLearningPath> findSimilarPathsByLearningStyle(@Param("learningStyle") DynamicLearningPath.LearningStyle learningStyle,
                                                              @Param("excludeUserId") Long excludeUserId);

    /**
     * 获取路径类型统计
     */
    @Query("SELECT dlp.pathType, COUNT(dlp) FROM DynamicLearningPath dlp " +
           "GROUP BY dlp.pathType ORDER BY COUNT(dlp) DESC")
    List<Object[]> getPathTypeStatistics();

    /**
     * 获取学习风格统计
     */
    @Query("SELECT dlp.learningStyle, COUNT(dlp) FROM DynamicLearningPath dlp " +
           "GROUP BY dlp.learningStyle ORDER BY COUNT(dlp) DESC")
    List<Object[]> getLearningStyleStatistics();

    /**
     * 获取状态统计
     */
    @Query("SELECT dlp.status, COUNT(dlp) FROM DynamicLearningPath dlp " +
           "GROUP BY dlp.status ORDER BY dlp.status")
    List<Object[]> getStatusStatistics();

    /**
     * 查找需要优化的路径（低质量分数）
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.qualityScore < :maxQualityScore AND " +
           "dlp.status = 'ACTIVE' " +
           "ORDER BY dlp.qualityScore ASC, dlp.updatedAt ASC")
    List<DynamicLearningPath> findPathsNeedingOptimization(@Param("maxQualityScore") BigDecimal maxQualityScore);

    /**
     * 查找停滞的路径（长时间未更新）
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.status = 'ACTIVE' AND " +
           "dlp.updatedAt < :cutoffDate " +
           "ORDER BY dlp.updatedAt ASC")
    List<DynamicLearningPath> findStalePaths(@Param("cutoffDate") java.time.LocalDateTime cutoffDate);

    /**
     * 批量更新路径状态
     */
    @Query("UPDATE DynamicLearningPath dlp SET " +
           "dlp.status = :status, " +
           "dlp.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dlp.id IN :pathIds")
    int updateStatusBatch(@Param("pathIds") Set<Long> pathIds,
                         @Param("status") DynamicLearningPath.Status status);

    /**
     * 批量更新完成率
     */
    @Query("UPDATE DynamicLearningPath dlp SET " +
           "dlp.completionRate = :completionRate, " +
           "dlp.currentStep = :currentStep, " +
           "dlp.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dlp.id = :pathId")
    int updateProgress(@Param("pathId") Long pathId,
                      @Param("completionRate") BigDecimal completionRate,
                      @Param("currentStep") Integer currentStep);

    /**
     * 更新质量分数
     */
    @Query("UPDATE DynamicLearningPath dlp SET " +
           "dlp.qualityScore = :qualityScore, " +
           "dlp.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dlp.id = :pathId")
    int updateQualityScore(@Param("pathId") Long pathId,
                          @Param("qualityScore") BigDecimal qualityScore);

    /**
     * 删除用户的所有路径
     */
    void deleteByUserId(Long userId);

    /**
     * 根据基础路径删除相关动态路径
     */
    void deleteByBasePathId(Long basePathId);

    /**
     * 获取最受欢迎的路径模板
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.isTemplate = true " +
           "ORDER BY dlp.qualityScore DESC, dlp.totalSkills DESC")
    List<DynamicLearningPath> findPopularTemplates(Pageable pageable);

    /**
     * 获取最新创建的路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.status != 'ABANDONED' " +
           "ORDER BY dlp.createdAt DESC")
    List<DynamicLearningPath> findRecentPaths(Pageable pageable);

    /**
     * 根据时间约束查找路径
     */
    @Query("SELECT dlp FROM DynamicLearningPath dlp WHERE " +
           "dlp.timeConstraint <= :maxTimeConstraint AND " +
           "dlp.status = 'ACTIVE' " +
           "ORDER BY dlp.timeConstraint ASC, dlp.qualityScore DESC")
    List<DynamicLearningPath> findPathsByTimeConstraint(@Param("maxTimeConstraint") Integer maxTimeConstraint);
}
