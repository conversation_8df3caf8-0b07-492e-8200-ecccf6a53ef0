import { ApiService } from './ApiService';
import { SkillNode, SkillEdge, GraphData } from '../components/visualization';

/**
 * 技能图谱服务类
 * 提供技能图谱相关的API调用功能
 */
export class SkillGraphService {
  
  /**
   * 获取技能图谱统计信息
   */
  static async getGraphStatistics() {
    try {
      const response = await ApiService.get('/skill-graph/statistics');
      return response;
    } catch (error) {
      console.error('获取图谱统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取技能学习路径
   */
  static async getLearningPath(skillId: number) {
    try {
      const response = await ApiService.get(`/skill-graph/learning-path/${skillId}`);
      return response;
    } catch (error) {
      console.error('获取学习路径失败:', error);
      throw error;
    }
  }

  /**
   * 检测循环依赖
   */
  static async detectCycles() {
    try {
      const response = await ApiService.get('/skill-graph/cycles');
      return response;
    } catch (error) {
      console.error('检测循环依赖失败:', error);
      throw error;
    }
  }

  /**
   * 查找技能路径
   */
  static async findSkillPath(sourceSkillId: number, targetSkillId: number) {
    try {
      const response = await ApiService.get('/skill-graph/path', {
        sourceSkillId,
        targetSkillId
      });
      return response;
    } catch (error) {
      console.error('查找技能路径失败:', error);
      throw error;
    }
  }

  /**
   * 获取技能聚类
   */
  static async getSkillClusters() {
    try {
      const response = await ApiService.get('/skill-graph/clusters');
      return response;
    } catch (error) {
      console.error('获取技能聚类失败:', error);
      throw error;
    }
  }

  /**
   * 分析技能重要性
   */
  static async analyzeSkillImportance(skillId: number) {
    try {
      const response = await ApiService.get(`/skill-graph/importance/${skillId}`);
      return response;
    } catch (error) {
      console.error('分析技能重要性失败:', error);
      throw error;
    }
  }

  /**
   * 获取完整的技能图谱数据
   */
  static async getCompleteGraphData(): Promise<GraphData> {
    try {
      // 并行获取所有需要的数据
      const [
        skillsResponse,
        relationshipsResponse,
        statisticsResponse,
        clustersResponse
      ] = await Promise.all([
        ApiService.get('/atomic-skills'),
        ApiService.get('/skill-relationships'),
        this.getGraphStatistics(),
        this.getSkillClusters()
      ]);

      // 构建节点数据
      const nodes: SkillNode[] = skillsResponse.code === 20000 
        ? skillsResponse.data.content.map((skill: any) => ({
            id: skill.id,
            name: skill.name,
            category: skill.category,
            difficultyLevel: skill.difficultyLevel,
            importance: Math.random() * 3 + 1 // 临时使用随机值，实际应该从重要性分析API获取
          }))
        : [];

      // 构建边数据
      const edges: SkillEdge[] = relationshipsResponse.code === 20000
        ? relationshipsResponse.data.map((relationship: any) => ({
            sourceId: relationship.sourceSkillId,
            targetId: relationship.targetSkillId,
            relationshipType: relationship.relationshipType,
            strength: relationship.strength || 1
          }))
        : [];

      // 构建完整图谱数据
      const graphData: GraphData = {
        nodes,
        edges,
        statistics: statisticsResponse.code === 20000 ? {
          ...statisticsResponse.data,
          clusters: clustersResponse.code === 20000 ? clustersResponse.data : []
        } : undefined
      };

      return graphData;
    } catch (error) {
      console.error('获取完整图谱数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取技能推荐
   */
  static async getSkillRecommendations(skillId: number, limit: number = 5) {
    try {
      const response = await ApiService.get(`/skill-graph/recommendations/${skillId}`, {
        limit
      });
      return response;
    } catch (error) {
      console.error('获取技能推荐失败:', error);
      throw error;
    }
  }

  /**
   * 获取技能前置依赖
   */
  static async getSkillPrerequisites(skillId: number) {
    try {
      const response = await ApiService.get(`/skill-graph/prerequisites/${skillId}`);
      return response;
    } catch (error) {
      console.error('获取技能前置依赖失败:', error);
      throw error;
    }
  }

  /**
   * 获取技能后续技能
   */
  static async getSkillSuccessors(skillId: number) {
    try {
      const response = await ApiService.get(`/skill-graph/successors/${skillId}`);
      return response;
    } catch (error) {
      console.error('获取技能后续技能失败:', error);
      throw error;
    }
  }

  /**
   * 验证学习路径
   */
  static async validateLearningPath(skillIds: number[]) {
    try {
      const response = await ApiService.post('/skill-graph/validate-path', {
        skillIds
      });
      return response;
    } catch (error) {
      console.error('验证学习路径失败:', error);
      throw error;
    }
  }

  /**
   * 优化学习路径
   */
  static async optimizeLearningPath(skillIds: number[]) {
    try {
      const response = await ApiService.post('/skill-graph/optimize-path', {
        skillIds
      });
      return response;
    } catch (error) {
      console.error('优化学习路径失败:', error);
      throw error;
    }
  }
}
