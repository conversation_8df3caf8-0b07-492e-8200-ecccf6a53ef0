package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.*;
import com.itbook.service.LearningProgressTracker.LearningEvent;
import com.itbook.service.LearningProgressTracker.LearningEventType;
import com.itbook.service.LearningProgressTracker.LearningBehaviorAnalysis;
import com.itbook.service.PathAdjustmentEngine.PathAdjustmentRequest;
import com.itbook.service.PathAdjustmentEngine.PathAdjustmentResult;
import com.itbook.service.PathAdjustmentEngine.AdjustmentTrigger;
import com.itbook.service.PathAdjustmentEngine.AdjustmentStrategy;
import com.itbook.service.FeedbackProcessor.UserFeedback;
import com.itbook.service.FeedbackProcessor.FeedbackType;
import com.itbook.service.FeedbackProcessor.FeedbackSentiment;
import com.itbook.service.FeedbackProcessor.FeedbackAnalysis;
import com.itbook.service.EffectivenessEvaluator.EffectivenessEvaluation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态调整统一API控制器
 * 
 * 整合学习进度跟踪、路径调整、用户反馈处理、学习效果评估等功能，
 * 提供完整的动态学习路径调整API服务
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/dynamic-adjustment")
@RequiredArgsConstructor
@Tag(name = "动态调整", description = "动态学习路径调整统一API接口")
public class DynamicAdjustmentController {

    private final LearningProgressTracker learningProgressTracker;
    private final PathAdjustmentEngine pathAdjustmentEngine;
    private final FeedbackProcessor feedbackProcessor;
    private final EffectivenessEvaluator effectivenessEvaluator;

    // ==================== 学习进度跟踪相关API ====================

    /**
     * 记录学习行为事件
     */
    @PostMapping("/learning-events")
    @Operation(summary = "记录学习行为事件", description = "记录用户的学习行为事件，用于进度跟踪和行为分析")
    public ResponseEntity<ApiResponse<Map<String, Object>>> recordLearningEvent(
            @Parameter(description = "学习事件数据", required = true)
            @Valid @RequestBody LearningEventRequest request) {
        
        log.info("📝 记录学习行为事件: userId={}, eventType={}, stepId={}", 
                request.getUserId(), request.getEventType(), request.getStepId());
        
        try {
            // 构建学习事件
            LearningEvent event = new LearningEvent();
            event.setUserId(request.getUserId());
            event.setPathId(request.getPathId());
            event.setStepId(request.getStepId());
            event.setEventType(LearningEventType.valueOf(request.getEventType()));
            event.setTimestamp(LocalDateTime.now());
            event.setEventData(request.getEventData() != null ? request.getEventData() : new HashMap<>());

            // 记录事件
            learningProgressTracker.recordLearningEvent(event);

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("eventId", System.currentTimeMillis());
            responseData.put("userId", request.getUserId());
            responseData.put("eventType", request.getEventType());
            responseData.put("timestamp", event.getTimestamp());
            responseData.put("status", "recorded");

            return ResponseEntity.ok(ApiResponse.success("学习行为事件记录成功", responseData));

        } catch (Exception e) {
            log.error("❌ 学习行为事件记录失败: userId={}, eventType={}", 
                    request.getUserId(), request.getEventType(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习行为事件记录失败: " + e.getMessage()));
        }
    }

    /**
     * 分析用户学习行为
     */
    @GetMapping("/users/{userId}/paths/{pathId}/behavior-analysis")
    @Operation(summary = "分析用户学习行为", description = "分析用户在特定学习路径中的学习行为和模式")
    public ResponseEntity<ApiResponse<LearningBehaviorAnalysis>> analyzeLearningBehavior(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("🔍 分析用户学习行为: userId={}, pathId={}", userId, pathId);
        
        try {
            LearningBehaviorAnalysis analysis = learningProgressTracker.analyzeLearningBehavior(userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("学习行为分析完成", analysis));

        } catch (Exception e) {
            log.error("❌ 学习行为分析失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习行为分析失败: " + e.getMessage()));
        }
    }

    // ==================== 路径调整相关API ====================

    /**
     * 执行路径调整
     */
    @PostMapping("/path-adjustment")
    @Operation(summary = "执行路径调整", description = "基于用户学习情况和反馈调整学习路径")
    public ResponseEntity<ApiResponse<PathAdjustmentResult>> adjustPath(
            @Parameter(description = "路径调整请求", required = true)
            @Valid @RequestBody PathAdjustmentRequest request) {
        
        log.info("🔧 执行路径调整: userId={}, pathId={}, trigger={}", 
                request.getUserId(), request.getPathId(), request.getTrigger());
        
        try {
            PathAdjustmentResult result = pathAdjustmentEngine.adjustPath(request);
            return ResponseEntity.ok(ApiResponse.success("路径调整完成", result));

        } catch (Exception e) {
            log.error("❌ 路径调整失败: userId={}, pathId={}", 
                    request.getUserId(), request.getPathId(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径调整失败: " + e.getMessage()));
        }
    }

    /**
     * 自动路径调整
     */
    @PostMapping("/users/{userId}/paths/{pathId}/auto-adjust")
    @Operation(summary = "自动路径调整", description = "自动检测并执行路径调整")
    public ResponseEntity<ApiResponse<List<PathAdjustmentResult>>> autoAdjustPath(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("🤖 自动路径调整: userId={}, pathId={}", userId, pathId);
        
        try {
            List<PathAdjustmentResult> results = pathAdjustmentEngine.autoAdjustPath(userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("自动路径调整完成", results));

        } catch (Exception e) {
            log.error("❌ 自动路径调整失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("自动路径调整失败: " + e.getMessage()));
        }
    }

    /**
     * 评估调整必要性
     */
    @GetMapping("/users/{userId}/paths/{pathId}/adjustment-evaluation")
    @Operation(summary = "评估调整必要性", description = "评估学习路径是否需要调整")
    public ResponseEntity<ApiResponse<Map<String, Object>>> evaluateAdjustmentNeed(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 评估路径调整必要性: userId={}, pathId={}", userId, pathId);
        
        try {
            Map<String, Object> evaluation = pathAdjustmentEngine.evaluateAdjustmentNeed(userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("路径调整必要性评估完成", evaluation));

        } catch (Exception e) {
            log.error("❌ 路径调整必要性评估失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径调整必要性评估失败: " + e.getMessage()));
        }
    }

    // ==================== 用户反馈相关API ====================

    /**
     * 提交用户反馈
     */
    @PostMapping("/feedback")
    @Operation(summary = "提交用户反馈", description = "收集用户对学习内容的反馈")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitFeedback(
            @Parameter(description = "用户反馈数据", required = true)
            @Valid @RequestBody FeedbackRequest request) {
        
        log.info("📝 提交用户反馈: userId={}, pathId={}, stepId={}, type={}", 
                request.getUserId(), request.getPathId(), request.getStepId(), request.getFeedbackType());
        
        try {
            // 构建反馈对象
            UserFeedback feedback = new UserFeedback();
            feedback.setUserId(request.getUserId());
            feedback.setPathId(request.getPathId());
            feedback.setStepId(request.getStepId());
            feedback.setFeedbackType(FeedbackType.valueOf(request.getFeedbackType()));
            feedback.setRating(request.getRating());
            feedback.setComment(request.getComment());
            
            if (request.getSentiment() != null) {
                feedback.setSentiment(FeedbackSentiment.valueOf(request.getSentiment()));
            }
            
            if (request.getMetadata() != null) {
                feedback.setMetadata(request.getMetadata());
            }

            // 处理反馈
            Map<String, Object> result = feedbackProcessor.processFeedback(feedback);
            return ResponseEntity.ok(ApiResponse.success("用户反馈提交成功", result));

        } catch (Exception e) {
            log.error("❌ 用户反馈提交失败: userId={}, pathId={}", 
                    request.getUserId(), request.getPathId(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("用户反馈提交失败: " + e.getMessage()));
        }
    }

    /**
     * 分析路径反馈
     */
    @GetMapping("/paths/{pathId}/feedback-analysis")
    @Operation(summary = "分析路径反馈", description = "分析特定学习路径的用户反馈")
    public ResponseEntity<ApiResponse<FeedbackAnalysis>> analyzeFeedback(
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 分析路径反馈: pathId={}", pathId);
        
        try {
            FeedbackAnalysis analysis = feedbackProcessor.analyzeFeedback(pathId);
            return ResponseEntity.ok(ApiResponse.success("路径反馈分析完成", analysis));

        } catch (Exception e) {
            log.error("❌ 路径反馈分析失败: pathId={}", pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径反馈分析失败: " + e.getMessage()));
        }
    }

    // ==================== 学习效果评估相关API ====================

    /**
     * 评估学习效果
     */
    @GetMapping("/users/{userId}/paths/{pathId}/effectiveness-evaluation")
    @Operation(summary = "评估学习效果", description = "评估用户在特定学习路径中的学习效果")
    public ResponseEntity<ApiResponse<EffectivenessEvaluation>> evaluateEffectiveness(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 评估学习效果: userId={}, pathId={}", userId, pathId);
        
        try {
            EffectivenessEvaluation evaluation = effectivenessEvaluator.evaluateEffectiveness(userId, pathId);
            return ResponseEntity.ok(ApiResponse.success("学习效果评估完成", evaluation));

        } catch (Exception e) {
            log.error("❌ 学习效果评估失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习效果评估失败: " + e.getMessage()));
        }
    }

    /**
     * 获取评估摘要
     */
    @GetMapping("/users/{userId}/evaluation-summary")
    @Operation(summary = "获取评估摘要", description = "获取用户的学习效果评估摘要统计")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEvaluationSummary(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        log.info("📈 获取评估摘要: userId={}", userId);
        
        try {
            Map<String, Object> summary = effectivenessEvaluator.getEvaluationSummary(userId);
            return ResponseEntity.ok(ApiResponse.success("评估摘要获取成功", summary));

        } catch (Exception e) {
            log.error("❌ 评估摘要获取失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("评估摘要获取失败: " + e.getMessage()));
        }
    }
}
