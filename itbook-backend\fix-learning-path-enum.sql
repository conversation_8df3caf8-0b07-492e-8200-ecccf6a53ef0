-- =====================================================
-- ITBook项目 - 修复学习路径枚举值问题
-- 
-- 问题：learning_path表中存在无效的path_type值'STRUCTURED'
-- 解决方案：将'STRUCTURED'替换为'STANDARD'
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 查看当前有问题的数据
SELECT id, name, path_type, status 
FROM learning_path 
WHERE path_type = 'STRUCTURED';

-- 将'STRUCTURED'替换为'STANDARD'
UPDATE learning_path 
SET path_type = 'STANDARD' 
WHERE path_type = 'STRUCTURED';

-- 验证修复结果
SELECT id, name, path_type, status 
FROM learning_path 
WHERE path_type = 'STANDARD';

-- 检查是否还有其他无效的枚举值
SELECT DISTINCT path_type 
FROM learning_path 
WHERE path_type NOT IN ('STANDARD', 'PERSONALIZED', 'TEMPLATE', 'CUSTOM');

-- 提交事务
COMMIT;

-- 显示修复摘要
SELECT 
    'learning_path表path_type字段修复完成' as message,
    COUNT(*) as total_records,
    SUM(CASE WHEN path_type = 'STANDARD' THEN 1 ELSE 0 END) as standard_count,
    SUM(CASE WHEN path_type = 'PERSONALIZED' THEN 1 ELSE 0 END) as personalized_count,
    SUM(CASE WHEN path_type = 'TEMPLATE' THEN 1 ELSE 0 END) as template_count,
    SUM(CASE WHEN path_type = 'CUSTOM' THEN 1 ELSE 0 END) as custom_count
FROM learning_path;
