-- ===================================================================
-- ITBook数据库表结构重构 - 清理脚本
-- 创建时间: 2025-07-14
-- 作者: ITBook Team
-- 
-- 删除已迁移的career_path和project表
-- 仅在数据迁移验证完成后执行
-- ===================================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===================================================================
-- 最终数据验证
-- ===================================================================

-- 验证career_level表包含所有必要数据
SELECT 
    'Final Validation - career_level' as check_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT level_code) as unique_levels,
    SUM(CASE WHEN skills IS NOT NULL THEN 1 ELSE 0 END) as records_with_skills,
    SUM(CASE WHEN projects IS NOT NULL THEN 1 ELSE 0 END) as records_with_projects,
    SUM(CASE WHEN salary_range_min IS NOT NULL THEN 1 ELSE 0 END) as records_with_salary
FROM career_level;

-- 验证career_project表包含所有必要数据
SELECT 
    'Final Validation - career_project' as check_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT difficulty) as unique_difficulties,
    SUM(CASE WHEN image_url IS NOT NULL THEN 1 ELSE 0 END) as records_with_images,
    SUM(participants) as total_participants,
    AVG(rating) as average_rating,
    SUM(view_count) as total_views
FROM career_project;

-- ===================================================================
-- 删除外键约束（如果存在）
-- ===================================================================

-- 检查并删除引用career_path表的外键约束
-- 注意：需要根据实际的外键约束名称调整

-- 检查并删除引用project表的外键约束
-- 注意：需要根据实际的外键约束名称调整

-- ===================================================================
-- 删除已迁移的表
-- ===================================================================

-- 删除career_path表
DROP TABLE IF EXISTS `career_path`;

-- 删除project表
DROP TABLE IF EXISTS `project`;

-- ===================================================================
-- 创建索引优化
-- ===================================================================

-- 为career_level表的新字段创建索引
ALTER TABLE `career_level` 
ADD INDEX `idx_career_level_duration` (`duration`),
ADD INDEX `idx_career_level_salary_range` (`salary_range_min`, `salary_range_max`);

-- 为career_project表的新字段创建索引
ALTER TABLE `career_project`
ADD INDEX `idx_career_project_status` (`status`),
ADD INDEX `idx_career_project_type` (`type`),
ADD INDEX `idx_career_project_featured` (`is_featured`),
ADD INDEX `idx_career_project_trending` (`is_trending`),
ADD INDEX `idx_career_project_rating` (`rating`),
ADD INDEX `idx_career_project_participants` (`participants`);

-- ===================================================================
-- 更新表注释
-- ===================================================================

-- 更新career_level表注释
ALTER TABLE `career_level` COMMENT = '职业级别配置表 - 整合了职业发展路径信息';

-- 更新career_project表注释
ALTER TABLE `career_project` COMMENT = '职业项目模板表 - 整合了项目展示功能';

SET FOREIGN_KEY_CHECKS = 1;

-- ===================================================================
-- 清理完成验证
-- ===================================================================

-- 验证表已删除
SELECT 
    TABLE_NAME,
    TABLE_COMMENT
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('career_path', 'project', 'career_level', 'career_project')
ORDER BY TABLE_NAME;

-- ===================================================================
-- 清理完成提示
-- ===================================================================
SELECT 'Database restructure cleanup completed successfully!' as status,
       'career_path and project tables have been removed' as details,
       'career_level and career_project tables have been enhanced' as enhancement;
