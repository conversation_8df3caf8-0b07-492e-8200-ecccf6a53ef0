-- =====================================================
-- ITBook项目 - 简化的职业级别数据迁移脚本
-- 
-- 目标：修复现有数据，正确关联career_goal_id和career_level_id
-- 作者：ITBook Team
-- 日期：2025-07-13
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 第一步：修复career_goal_id
-- =====================================================

-- 将所有NULL的career_goal_id设置为默认值（假设大部分用户选择React前端工程师）
UPDATE user_career_goal 
SET career_goal_id = 3 
WHERE career_goal_id IS NULL;

-- =====================================================
-- 第二步：修复career_level_id
-- =====================================================

-- 根据target_level和career_goal_id设置正确的career_level_id
UPDATE user_career_goal ucg
JOIN career_level cl ON cl.career_goal_id = ucg.career_goal_id
SET ucg.career_level_id = cl.id
WHERE ucg.target_level = 'JUNIOR' AND cl.level_code = 'junior';

UPDATE user_career_goal ucg
JOIN career_level cl ON cl.career_goal_id = ucg.career_goal_id
SET ucg.career_level_id = cl.id
WHERE ucg.target_level = 'MID' AND cl.level_code = 'mid';

UPDATE user_career_goal ucg
JOIN career_level cl ON cl.career_goal_id = ucg.career_goal_id
SET ucg.career_level_id = cl.id
WHERE ucg.target_level = 'SENIOR' AND cl.level_code = 'senior';

-- =====================================================
-- 第三步：验证数据迁移
-- =====================================================

-- 检查迁移结果
SELECT 
    '数据迁移验证' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN career_goal_id IS NOT NULL THEN 1 END) as records_with_career_goal_id,
    COUNT(CASE WHEN career_level_id > 0 THEN 1 END) as records_with_career_level_id,
    COUNT(CASE WHEN career_goal_id IS NULL OR career_level_id = 0 THEN 1 END) as problematic_records
FROM user_career_goal;

-- 检查级别分布
SELECT
    cl.level_code,
    cl.level_name,
    cl.sort_order,
    COUNT(ucg.id) as user_count
FROM user_career_goal ucg
JOIN career_level cl ON ucg.career_level_id = cl.id
GROUP BY cl.level_code, cl.level_name, cl.sort_order
ORDER BY cl.sort_order;

-- 提交事务
COMMIT;

-- =====================================================
-- 迁移说明
-- =====================================================

/*
本迁移脚本完成以下任务：

1. 修复career_goal_id：将NULL值设置为默认的career_goal_id=3（React前端工程师）
2. 修复career_level_id：根据target_level枚举值设置正确的career_level_id
3. 数据验证：验证迁移后的数据完整性

注意事项：
- 这是一个简化的迁移，假设大部分用户选择React前端工程师
- target_level字段暂时保留，待验证无问题后再删除
- 如果需要更精确的career_goal_id映射，可以根据实际业务需求调整

后续步骤：
1. 验证前后端联调正常
2. 确认新的关联关系工作正常
3. 删除旧的target_level字段（可选）
*/
