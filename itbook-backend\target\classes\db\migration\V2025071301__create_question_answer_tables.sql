-- 创建问答系统相关表

-- 问题表
CREATE TABLE question (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '问题标题',
    content TEXT NOT NULL COMMENT '问题内容',
    type VARCHAR(50) NOT NULL DEFAULT 'GENERAL' COMMENT '问题类型：TECHNICAL, CAREER, LEARNING, PROJECT, TOOLS, GENERAL',
    status VARCHAR(50) NOT NULL DEFAULT 'OPEN' COMMENT '问题状态：OPEN, ANSWERED, CLOSED, RESOLVED',
    author_id BIGINT NOT NULL COMMENT '提问者ID',
    tags JSON COMMENT '标签列表',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    answer_count INT DEFAULT 0 COMMENT '回答数',
    follow_count INT DEFAULT 0 COMMENT '关注数',
    has_accepted_answer BOOLEAN DEFAULT FALSE COMMENT '是否有被采纳的答案',
    accepted_answer_id BIGINT COMMENT '被采纳的答案ID',
    bounty INT DEFAULT 0 COMMENT '悬赏积分',
    difficulty VARCHAR(50) COMMENT '难度级别：BEGINNER, INTERMEDIATE, ADVANCED, EXPERT',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    
    INDEX idx_author_id (author_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_last_activity_at (last_activity_at),
    INDEX idx_view_count (view_count),
    INDEX idx_like_count (like_count),
    
    FOREIGN KEY (author_id) REFERENCES user(id) ON DELETE CASCADE
);

-- 回答表
CREATE TABLE answer (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    content TEXT NOT NULL COMMENT '回答内容',
    question_id BIGINT NOT NULL COMMENT '问题ID',
    author_id BIGINT NOT NULL COMMENT '回答者ID',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    dislike_count INT DEFAULT 0 COMMENT '踩数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    is_accepted BOOLEAN DEFAULT FALSE COMMENT '是否被采纳',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_question_id (question_id),
    INDEX idx_author_id (author_id),
    INDEX idx_created_at (created_at),
    INDEX idx_like_count (like_count),
    INDEX idx_is_accepted (is_accepted),
    
    FOREIGN KEY (question_id) REFERENCES question(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES user(id) ON DELETE CASCADE
);

-- 问题关注表
CREATE TABLE question_follow (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    question_id BIGINT NOT NULL COMMENT '问题ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    
    UNIQUE KEY uk_question_user (question_id, user_id),
    INDEX idx_question_id (question_id),
    INDEX idx_user_id (user_id),
    
    FOREIGN KEY (question_id) REFERENCES question(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
);

-- 问题点赞表
CREATE TABLE question_like (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    question_id BIGINT NOT NULL COMMENT '问题ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
    
    UNIQUE KEY uk_question_user (question_id, user_id),
    INDEX idx_question_id (question_id),
    INDEX idx_user_id (user_id),
    
    FOREIGN KEY (question_id) REFERENCES question(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
);

-- 回答点赞表
CREATE TABLE answer_like (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    answer_id BIGINT NOT NULL COMMENT '回答ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    is_like BOOLEAN NOT NULL COMMENT 'true为点赞，false为踩',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    UNIQUE KEY uk_answer_user (answer_id, user_id),
    INDEX idx_answer_id (answer_id),
    INDEX idx_user_id (user_id),
    
    FOREIGN KEY (answer_id) REFERENCES answer(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
);

-- 插入测试数据
INSERT INTO question (title, content, type, status, author_id, tags, view_count, like_count, answer_count, follow_count, difficulty) VALUES
('React Hooks的最佳实践有哪些？', '我在使用React Hooks时遇到了一些性能问题，想了解一下React Hooks的最佳实践，特别是useEffect和useState的使用技巧。', 'TECHNICAL', 'OPEN', 1, '["React", "Hooks", "性能优化", "前端开发"]', 156, 23, 3, 8, 'INTERMEDIATE'),
('如何从Java后端开发转向全栈开发？', '我有3年Java后端开发经验，想转向全栈开发，应该学习哪些前端技术？有什么好的学习路径推荐吗？', 'CAREER', 'ANSWERED', 1, '["职业发展", "全栈开发", "Java", "前端技术"]', 289, 45, 7, 15, 'BEGINNER'),
('Spring Boot项目如何集成Redis缓存？', '想在Spring Boot项目中集成Redis作为缓存，需要注意哪些配置和最佳实践？', 'TECHNICAL', 'RESOLVED', 1, '["Spring Boot", "Redis", "缓存", "后端开发"]', 234, 31, 5, 12, 'INTERMEDIATE'),
('MySQL数据库性能优化有哪些方法？', '我的MySQL数据库查询比较慢，想了解一下数据库性能优化的方法，包括索引优化、查询优化等。', 'TECHNICAL', 'OPEN', 1, '["MySQL", "性能优化", "数据库", "索引"]', 178, 28, 4, 9, 'ADVANCED'),
('新手程序员如何提高编程能力？', '刚入行的程序员，想快速提高编程能力，有什么好的学习方法和资源推荐？', 'LEARNING', 'ANSWERED', 1, '["编程学习", "新手指南", "技能提升"]', 345, 52, 8, 20, 'BEGINNER');

INSERT INTO answer (content, question_id, author_id, like_count, dislike_count, comment_count, is_accepted) VALUES
('React Hooks最佳实践包括：1. 使用useCallback和useMemo优化性能 2. 合理使用useEffect的依赖数组 3. 自定义Hook复用逻辑 4. 避免在循环、条件或嵌套函数中调用Hook', 1, 1, 15, 1, 3, false),
('建议学习路径：1. 先掌握HTML/CSS/JavaScript基础 2. 学习React或Vue框架 3. 了解前端工程化工具 4. 学习TypeScript 5. 实践项目整合前后端', 2, 1, 28, 2, 5, true),
('Spring Boot集成Redis步骤：1. 添加spring-boot-starter-data-redis依赖 2. 配置Redis连接信息 3. 使用@Cacheable等注解 4. 自定义缓存配置', 3, 1, 22, 0, 4, true);
