package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.FeedbackProcessor;
import com.itbook.service.FeedbackProcessor.UserFeedback;
import com.itbook.service.FeedbackProcessor.FeedbackType;
import com.itbook.service.FeedbackProcessor.FeedbackSentiment;
import com.itbook.service.FeedbackProcessor.FeedbackAnalysis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户反馈控制器
 * 
 * 提供用户反馈收集和处理的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/feedback")
@RequiredArgsConstructor
@Tag(name = "用户反馈", description = "用户反馈收集和处理相关接口")
public class FeedbackController {

    private final FeedbackProcessor feedbackProcessor;

    /**
     * 提交用户反馈
     */
    @PostMapping("/submit")
    @Operation(summary = "提交用户反馈", description = "收集用户对学习内容的反馈")
    public ResponseEntity<ApiResponse<Map<String, Object>>> submitFeedback(
            @Parameter(description = "用户反馈数据", required = true)
            @Valid @RequestBody FeedbackRequest request) {
        
        log.info("📝 提交用户反馈: userId={}, pathId={}, stepId={}, type={}", 
                request.getUserId(), request.getPathId(), request.getStepId(), request.getFeedbackType());
        
        try {
            // 构建反馈对象
            UserFeedback feedback = new UserFeedback();
            feedback.setUserId(request.getUserId());
            feedback.setPathId(request.getPathId());
            feedback.setStepId(request.getStepId());
            feedback.setFeedbackType(FeedbackType.valueOf(request.getFeedbackType()));
            feedback.setRating(request.getRating());
            feedback.setComment(request.getComment());
            
            if (request.getSentiment() != null) {
                feedback.setSentiment(FeedbackSentiment.valueOf(request.getSentiment()));
            }
            
            if (request.getMetadata() != null) {
                feedback.setMetadata(request.getMetadata());
            }

            // 处理反馈
            Map<String, Object> result = feedbackProcessor.processFeedback(feedback);

            log.info("✅ 用户反馈提交成功: userId={}, adjustmentTriggered={}", 
                    request.getUserId(), result.get("adjustmentTriggered"));

            return ResponseEntity.ok(ApiResponse.success("用户反馈提交成功", result));

        } catch (Exception e) {
            log.error("❌ 用户反馈提交失败: userId={}, pathId={}", 
                    request.getUserId(), request.getPathId(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("用户反馈提交失败: " + e.getMessage()));
        }
    }

    /**
     * 分析路径反馈
     */
    @GetMapping("/paths/{pathId}/analysis")
    @Operation(summary = "分析路径反馈", description = "分析特定学习路径的用户反馈")
    public ResponseEntity<ApiResponse<FeedbackAnalysis>> analyzeFeedback(
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 分析路径反馈: pathId={}", pathId);
        
        try {
            // 执行反馈分析
            FeedbackAnalysis analysis = feedbackProcessor.analyzeFeedback(pathId);

            log.info("✅ 路径反馈分析完成: pathId={}, satisfaction={}", 
                    pathId, analysis.getOverallSatisfaction());

            return ResponseEntity.ok(ApiResponse.success("路径反馈分析完成", analysis));

        } catch (Exception e) {
            log.error("❌ 路径反馈分析失败: pathId={}", pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径反馈分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取反馈统计
     */
    @GetMapping("/paths/{pathId}/statistics")
    @Operation(summary = "获取反馈统计", description = "获取特定学习路径的反馈统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFeedbackStatistics(
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📈 获取反馈统计: pathId={}", pathId);
        
        try {
            // 获取统计信息
            Map<String, Object> statistics = feedbackProcessor.getFeedbackStatistics(pathId);

            log.info("✅ 反馈统计获取完成: pathId={}, totalFeedbacks={}", 
                    pathId, statistics.get("totalFeedbacks"));

            return ResponseEntity.ok(ApiResponse.success("反馈统计获取成功", statistics));

        } catch (Exception e) {
            log.error("❌ 反馈统计获取失败: pathId={}", pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("反馈统计获取失败: " + e.getMessage()));
        }
    }

    /**
     * 获取反馈类型列表
     */
    @GetMapping("/types")
    @Operation(summary = "获取反馈类型", description = "获取所有支持的反馈类型")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFeedbackTypes() {
        
        log.info("📋 获取反馈类型列表");
        
        try {
            Map<String, Object> feedbackTypes = new HashMap<>();
            
            for (FeedbackType feedbackType : FeedbackType.values()) {
                Map<String, String> typeInfo = new HashMap<>();
                switch (feedbackType) {
                    case DIFFICULTY_RATING:
                        typeInfo.put("name", "难度评分");
                        typeInfo.put("description", "对学习内容难度的评分（1-5分）");
                        typeInfo.put("requiresRating", "true");
                        break;
                    case QUALITY_RATING:
                        typeInfo.put("name", "质量评分");
                        typeInfo.put("description", "对学习内容质量的评分（1-5分）");
                        typeInfo.put("requiresRating", "true");
                        break;
                    case CONTENT_FEEDBACK:
                        typeInfo.put("name", "内容反馈");
                        typeInfo.put("description", "对学习内容的具体反馈和建议");
                        typeInfo.put("requiresRating", "false");
                        break;
                    case LEARNING_EXPERIENCE:
                        typeInfo.put("name", "学习体验");
                        typeInfo.put("description", "对整体学习体验的反馈");
                        typeInfo.put("requiresRating", "false");
                        break;
                    case SUGGESTION:
                        typeInfo.put("name", "改进建议");
                        typeInfo.put("description", "对学习内容或系统的改进建议");
                        typeInfo.put("requiresRating", "false");
                        break;
                    case BUG_REPORT:
                        typeInfo.put("name", "问题报告");
                        typeInfo.put("description", "报告系统或内容中的问题");
                        typeInfo.put("requiresRating", "false");
                        break;
                    case GENERAL_COMMENT:
                        typeInfo.put("name", "一般评论");
                        typeInfo.put("description", "一般性的评论和想法");
                        typeInfo.put("requiresRating", "false");
                        break;
                }
                feedbackTypes.put(feedbackType.name(), typeInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("feedbackTypes", feedbackTypes);
            responseData.put("totalCount", feedbackTypes.size());

            return ResponseEntity.ok(ApiResponse.success("获取反馈类型成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取反馈类型失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取反馈类型失败: " + e.getMessage()));
        }
    }

    /**
     * 获取情感类型列表
     */
    @GetMapping("/sentiments")
    @Operation(summary = "获取情感类型", description = "获取所有支持的反馈情感类型")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFeedbackSentiments() {
        
        log.info("📋 获取反馈情感类型列表");
        
        try {
            Map<String, Object> sentiments = new HashMap<>();
            
            for (FeedbackSentiment sentiment : FeedbackSentiment.values()) {
                Map<String, String> sentimentInfo = new HashMap<>();
                switch (sentiment) {
                    case POSITIVE:
                        sentimentInfo.put("name", "积极");
                        sentimentInfo.put("description", "积极正面的反馈");
                        sentimentInfo.put("color", "#4CAF50");
                        break;
                    case NEUTRAL:
                        sentimentInfo.put("name", "中性");
                        sentimentInfo.put("description", "中性客观的反馈");
                        sentimentInfo.put("color", "#FFC107");
                        break;
                    case NEGATIVE:
                        sentimentInfo.put("name", "消极");
                        sentimentInfo.put("description", "消极负面的反馈");
                        sentimentInfo.put("color", "#F44336");
                        break;
                }
                sentiments.put(sentiment.name(), sentimentInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("sentiments", sentiments);
            responseData.put("totalCount", sentiments.size());

            return ResponseEntity.ok(ApiResponse.success("获取反馈情感类型成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取反馈情感类型失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取反馈情感类型失败: " + e.getMessage()));
        }
    }

    /**
     * 反馈请求参数
     */
    public static class FeedbackRequest {
        private Long userId;
        private Long pathId;
        private Long stepId;
        private String feedbackType;
        private Integer rating;
        private String comment;
        private String sentiment;
        private Map<String, Object> metadata;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public Long getStepId() { return stepId; }
        public void setStepId(Long stepId) { this.stepId = stepId; }
        
        public String getFeedbackType() { return feedbackType; }
        public void setFeedbackType(String feedbackType) { this.feedbackType = feedbackType; }
        
        public Integer getRating() { return rating; }
        public void setRating(Integer rating) { this.rating = rating; }
        
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        
        public String getSentiment() { return sentiment; }
        public void setSentiment(String sentiment) { this.sentiment = sentiment; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }
}
