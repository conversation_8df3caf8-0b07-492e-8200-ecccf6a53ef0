/**
 * 综合分析报告页面
 * 
 * 展示用户学习状况的综合分析报告
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */

import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, ActivityIndicator, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { DynamicAdjustmentService } from '../../services/DynamicAdjustmentService';
import { DynamicAdjustmentCard } from '../../components/dynamic-adjustment/DynamicAdjustmentCard';
import { EffectivenessChart } from '../../components/dynamic-adjustment/EffectivenessChart';
import { FeedbackForm } from '../../components/dynamic-adjustment/FeedbackForm';
import {
  AnalysisReportProps,
  ComprehensiveAnalysis,
  SmartAdjustmentResult
} from '../../types/DynamicAdjustment';

interface AnalysisReportScreenProps {
  route: {
    params: {
      userId: number;
      pathId: number;
      pathName?: string;
    };
  };
  navigation: any;
}

/**
 * 综合分析报告页面
 */
export const AnalysisReportScreen: React.FC<AnalysisReportScreenProps> = ({
  route,
  navigation
}) => {
  const colors = useThemeColors();
  const { userId, pathId, pathName = '学习路径' } = route.params;
  
  const [analysis, setAnalysis] = useState<ComprehensiveAnalysis | null>(null);
  const [smartSuggestion, setSmartSuggestion] = useState<SmartAdjustmentResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'effectiveness' | 'feedback'>('overview');

  useEffect(() => {
    loadAnalysisData();
  }, [userId, pathId]);

  const loadAnalysisData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      // 并行加载综合分析和智能建议
      const [analysisData, smartData] = await Promise.all([
        DynamicAdjustmentService.comprehensiveAnalysis(userId, pathId),
        DynamicAdjustmentService.smartAdjustment(userId, pathId, { autoExecute: false })
      ]);

      setAnalysis(analysisData);
      setSmartSuggestion(smartData);

    } catch (error) {
      console.error('加载分析数据失败:', error);
      Alert.alert('错误', '加载分析数据失败，请稍后重试');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadAnalysisData(true);
  };

  const handleActionTaken = (action: string) => {
    console.log('执行操作:', action);
    // 重新加载数据
    loadAnalysisData();
  };

  const renderTabBar = () => {
    const tabs = [
      { key: 'overview', label: '概览', icon: 'analytics-outline' },
      { key: 'effectiveness', label: '效果分析', icon: 'trending-up-outline' },
      { key: 'feedback', label: '反馈', icon: 'chatbubble-outline' }
    ];

    return (
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.surface,
        borderRadius: tokens.radius('md'),
        margin: tokens.spacing('md'),
        padding: tokens.spacing('xs')
      }}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: tokens.spacing('sm'),
              paddingHorizontal: tokens.spacing('md'),
              borderRadius: tokens.radius('sm'),
              backgroundColor: activeTab === tab.key ? colors.primaryContainer : 'transparent'
            }}
            onPress={() => setActiveTab(tab.key as any)}
            activeOpacity={0.7}
          >
            <Ionicons
              name={tab.icon as any}
              size={18}
              color={activeTab === tab.key ? colors.onPrimaryContainer : colors.onSurface}
            />
            <Text style={{
              color: activeTab === tab.key ? colors.onPrimaryContainer : colors.onSurface,
              fontSize: tokens.fontSize('sm'),
              fontWeight: tokens.fontWeight('medium'),
              marginLeft: tokens.spacing('xs')
            }}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderOverviewTab = () => {
    if (!analysis) return null;

    return (
      <View>
        {/* 动态调整卡片 */}
        <DynamicAdjustmentCard
          userId={userId}
          pathId={pathId}
          onAdjustmentComplete={handleActionTaken}
        />

        {/* 智能建议卡片 */}
        {smartSuggestion && (
          <View style={{
            backgroundColor: colors.surface,
            borderRadius: tokens.radius('md'),
            padding: tokens.spacing('lg'),
            margin: tokens.spacing('md'),
            marginTop: 0
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: tokens.spacing('md')
            }}>
              <Ionicons name="bulb" size={24} color={colors.primary} />
              <Text style={{
                color: colors.text,
                fontSize: tokens.fontSize('title-sm'),
                fontWeight: tokens.fontWeight('bold'),
                marginLeft: tokens.spacing('sm')
              }}>
                智能建议
              </Text>
            </View>

            <View style={{
              backgroundColor: colors.primaryContainer,
              borderRadius: tokens.radius('sm'),
              padding: tokens.spacing('md'),
              marginBottom: tokens.spacing('md')
            }}>
              <Text style={{
                color: colors.onPrimaryContainer,
                fontSize: tokens.fontSize('sm'),
                lineHeight: 20
              }}>
                {smartSuggestion.intelligentSuggestions.learningStyleOptimization}
              </Text>
            </View>

            <View style={{
              backgroundColor: colors.surfaceVariant,
              borderRadius: tokens.radius('sm'),
              padding: tokens.spacing('md'),
              marginBottom: tokens.spacing('md')
            }}>
              <Text style={{
                color: colors.onSurfaceVariant,
                fontSize: tokens.fontSize('sm'),
                lineHeight: 20
              }}>
                {smartSuggestion.intelligentSuggestions.difficultyAdjustment}
              </Text>
            </View>

            <View style={{
              backgroundColor: colors.secondaryContainer,
              borderRadius: tokens.radius('sm'),
              padding: tokens.spacing('md')
            }}>
              <Text style={{
                color: colors.onSecondaryContainer,
                fontSize: tokens.fontSize('sm'),
                lineHeight: 20
              }}>
                {smartSuggestion.intelligentSuggestions.timeManagement}
              </Text>
            </View>
          </View>
        )}

        {/* 关键指标卡片 */}
        <View style={{
          backgroundColor: colors.surface,
          borderRadius: tokens.radius('md'),
          padding: tokens.spacing('lg'),
          margin: tokens.spacing('md'),
          marginTop: 0
        }}>
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('title-sm'),
            fontWeight: tokens.fontWeight('bold'),
            marginBottom: tokens.spacing('md')
          }}>
            关键指标
          </Text>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between'
          }}>
            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text style={{
                color: colors.primary,
                fontSize: tokens.fontSize('title-md'),
                fontWeight: tokens.fontWeight('bold')
              }}>
                {Math.round(analysis.behaviorAnalysis.completionProbability * 100)}%
              </Text>
              <Text style={{
                color: colors.textSecondary,
                fontSize: tokens.fontSize('xs'),
                textAlign: 'center'
              }}>
                完成概率
              </Text>
            </View>

            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text style={{
                color: colors.primary,
                fontSize: tokens.fontSize('title-md'),
                fontWeight: tokens.fontWeight('bold')
              }}>
                {analysis.behaviorAnalysis.estimatedRemainingDays}
              </Text>
              <Text style={{
                color: colors.textSecondary,
                fontSize: tokens.fontSize('xs'),
                textAlign: 'center'
              }}>
                预计剩余天数
              </Text>
            </View>

            <View style={{ flex: 1, alignItems: 'center' }}>
              <Text style={{
                color: colors.primary,
                fontSize: tokens.fontSize('title-md'),
                fontWeight: tokens.fontWeight('bold')
              }}>
                {Math.round(analysis.effectivenessEvaluation.overallEffectiveness * 100)}%
              </Text>
              <Text style={{
                color: colors.textSecondary,
                fontSize: tokens.fontSize('xs'),
                textAlign: 'center'
              }}>
                整体效果
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderEffectivenessTab = () => {
    if (!analysis) return null;

    return (
      <EffectivenessChart
        evaluation={analysis.effectivenessEvaluation}
        showDetails={true}
      />
    );
  };

  const renderFeedbackTab = () => {
    return (
      <FeedbackForm
        userId={userId}
        pathId={pathId}
        onFeedbackSubmit={(success) => {
          if (success) {
            // 重新加载分析数据
            loadAnalysisData();
          }
        }}
      />
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewTab();
      case 'effectiveness':
        return renderEffectivenessTab();
      case 'feedback':
        return renderFeedbackTab();
      default:
        return renderOverviewTab();
    }
  };

  if (isLoading) {
    return (
      <View style={{
        flex: 1,
        backgroundColor: colors.background,
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={{
          color: colors.textSecondary,
          fontSize: tokens.fontSize('md'),
          marginTop: tokens.spacing('md')
        }}>
          正在加载分析报告...
        </Text>
      </View>
    );
  }

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background
    }}>
      {/* 标题栏 */}
      <View style={{
        backgroundColor: colors.surface,
        paddingTop: 44,
        paddingBottom: tokens.spacing('md'),
        paddingHorizontal: tokens.spacing('md'),
        borderBottomWidth: 1,
        borderBottomColor: colors.outline
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{ padding: tokens.spacing('sm') }}
            activeOpacity={0.7}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>

          <View style={{ flex: 1, alignItems: 'center' }}>
            <Text style={{
              color: colors.text,
              fontSize: tokens.fontSize('title-sm'),
              fontWeight: tokens.fontWeight('bold')
            }}>
              学习分析报告
            </Text>
            <Text style={{
              color: colors.textSecondary,
              fontSize: tokens.fontSize('sm')
            }}>
              {pathName}
            </Text>
          </View>

          <TouchableOpacity
            onPress={handleRefresh}
            style={{ padding: tokens.spacing('sm') }}
            activeOpacity={0.7}
          >
            <Ionicons name="refresh" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab栏 */}
      {renderTabBar()}

      {/* 内容区域 */}
      <ScrollView
        style={{ flex: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {renderContent()}
      </ScrollView>
    </View>
  );
};
