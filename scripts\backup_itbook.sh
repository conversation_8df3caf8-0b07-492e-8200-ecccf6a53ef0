#!/bin/bash
# ITBook原子技能系统数据备份脚本
# 作者: ITBook Team
# 版本: 1.0
# 创建时间: 2025-07-21

# 配置变量
DB_USER="root"
DB_PASS="NW1M5@18N1YYzNlNz"
DB_NAME="itbook_dev"
BACKUP_DIR="./backup"
DATE=$(date +%Y%m%d_%H%M%S)
LOG_FILE="./backup/backup.log"

# 创建备份目录
mkdir -p $BACKUP_DIR/{full,incremental,realtime,logs}

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# 全量备份函数
full_backup() {
    log "开始全量备份..."
    
    BACKUP_FILE="$BACKUP_DIR/full/${DB_NAME}_${DATE}.sql"
    
    mysqldump -u $DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        --add-drop-table \
        --create-options \
        --disable-keys \
        --extended-insert \
        --quick \
        --lock-tables=false \
        $DB_NAME > $BACKUP_FILE 2>>$LOG_FILE
    
    if [ $? -eq 0 ]; then
        # 压缩备份文件
        gzip $BACKUP_FILE
        COMPRESSED_FILE="${BACKUP_FILE}.gz"
        
        # 验证备份文件
        if verify_backup "$COMPRESSED_FILE"; then
            log "全量备份成功: $(basename $COMPRESSED_FILE)"
            log "备份文件大小: $(du -h $COMPRESSED_FILE | cut -f1)"
        else
            log "错误: 备份文件验证失败"
            return 1
        fi
    else
        log "错误: 全量备份失败"
        return 1
    fi
}

# 核心表备份函数
core_tables_backup() {
    log "开始核心表备份..."
    
    # 原子技能系统核心表
    TABLES="atomic_skill skill_relationship user_atomic_skill_mastery dynamic_learning_path dynamic_path_step career_skill_mapping skill_assessment_record"
    
    for table in $TABLES; do
        BACKUP_FILE="$BACKUP_DIR/realtime/${table}_${DATE}.sql"
        
        mysqldump -u $DB_USER -p$DB_PASS \
            --single-transaction \
            --add-drop-table \
            --create-options \
            --disable-keys \
            --extended-insert \
            --quick \
            $DB_NAME $table > $BACKUP_FILE 2>>$LOG_FILE
        
        if [ $? -eq 0 ]; then
            log "表 $table 备份成功"
        else
            log "错误: 表 $table 备份失败"
        fi
    done
}

# 增量备份函数 (基于时间戳)
incremental_backup() {
    log "开始增量备份..."
    
    # 获取最后一次备份时间
    LAST_BACKUP_TIME=$(date -d '1 hour ago' '+%Y-%m-%d %H:00:00')
    CURRENT_TIME=$(date '+%Y-%m-%d %H:00:00')
    
    # 备份在指定时间范围内更新的数据
    TABLES="atomic_skill skill_relationship user_atomic_skill_mastery dynamic_learning_path dynamic_path_step career_skill_mapping skill_assessment_record"
    
    for table in $TABLES; do
        BACKUP_FILE="$BACKUP_DIR/incremental/${table}_incremental_${DATE}.sql"
        
        # 检查表是否有updated_at字段
        HAS_UPDATED_AT=$(mysql -u $DB_USER -p$DB_PASS $DB_NAME -e "SHOW COLUMNS FROM $table LIKE 'updated_at';" 2>/dev/null | wc -l)
        
        if [ $HAS_UPDATED_AT -gt 1 ]; then
            mysqldump -u $DB_USER -p$DB_PASS \
                --single-transaction \
                --where="updated_at >= '$LAST_BACKUP_TIME' AND updated_at < '$CURRENT_TIME'" \
                $DB_NAME $table > $BACKUP_FILE 2>>$LOG_FILE
            
            if [ $? -eq 0 ] && [ -s $BACKUP_FILE ]; then
                log "表 $table 增量备份成功"
            else
                log "表 $table 无增量数据或备份失败"
                rm -f $BACKUP_FILE
            fi
        else
            log "表 $table 不支持增量备份 (无updated_at字段)"
        fi
    done
}

# 备份验证函数
verify_backup() {
    local BACKUP_FILE=$1
    
    if [ ! -f "$BACKUP_FILE" ]; then
        log "错误: 备份文件不存在 - $BACKUP_FILE"
        return 1
    fi
    
    # 检查文件大小
    local SIZE=$(stat -c%s "$BACKUP_FILE" 2>/dev/null || stat -f%z "$BACKUP_FILE" 2>/dev/null)
    if [ $SIZE -lt 1000 ]; then
        log "错误: 备份文件过小 - $BACKUP_FILE (${SIZE} bytes)"
        return 1
    fi
    
    # 如果是gzip文件，检查压缩完整性
    if [[ "$BACKUP_FILE" == *.gz ]]; then
        if ! gzip -t "$BACKUP_FILE" 2>/dev/null; then
            log "错误: 压缩文件损坏 - $BACKUP_FILE"
            return 1
        fi
    fi
    
    log "备份文件验证通过 - $BACKUP_FILE (${SIZE} bytes)"
    return 0
}

# 清理旧备份函数
cleanup_old_backups() {
    log "开始清理旧备份文件..."
    
    # 删除30天前的全量备份
    DELETED_FULL=$(find $BACKUP_DIR/full -name "*.sql.gz" -mtime +30 -delete -print | wc -l)
    log "清理全量备份文件: $DELETED_FULL 个"
    
    # 删除7天前的增量备份
    DELETED_INCR=$(find $BACKUP_DIR/incremental -name "*.sql" -mtime +7 -delete -print | wc -l)
    log "清理增量备份文件: $DELETED_INCR 个"
    
    # 删除24小时前的实时备份
    DELETED_REAL=$(find $BACKUP_DIR/realtime -name "*.sql" -mtime +1 -delete -print | wc -l)
    log "清理实时备份文件: $DELETED_REAL 个"
    
    # 清理30天前的日志文件
    find $BACKUP_DIR/logs -name "*.log" -mtime +30 -delete
}

# 备份状态检查函数
check_backup_status() {
    log "检查备份状态..."
    
    # 检查最近24小时内是否有全量备份
    LATEST_FULL=$(find $BACKUP_DIR/full -name "*.sql.gz" -mtime -1 | head -1)
    if [ -z "$LATEST_FULL" ]; then
        log "警告: 24小时内没有全量备份!"
    else
        log "最新全量备份: $(basename $LATEST_FULL)"
    fi
    
    # 检查最近1小时内是否有核心表备份
    LATEST_CORE=$(find $BACKUP_DIR/realtime -name "*.sql" -mtime -0.04 | head -1)
    if [ -z "$LATEST_CORE" ]; then
        log "警告: 1小时内没有核心表备份!"
    else
        log "最新核心表备份: $(basename $LATEST_CORE)"
    fi
    
    # 统计备份文件数量和大小
    FULL_COUNT=$(find $BACKUP_DIR/full -name "*.sql.gz" | wc -l)
    FULL_SIZE=$(du -sh $BACKUP_DIR/full 2>/dev/null | cut -f1)
    log "全量备份: $FULL_COUNT 个文件, 总大小: $FULL_SIZE"
    
    REAL_COUNT=$(find $BACKUP_DIR/realtime -name "*.sql" | wc -l)
    REAL_SIZE=$(du -sh $BACKUP_DIR/realtime 2>/dev/null | cut -f1)
    log "实时备份: $REAL_COUNT 个文件, 总大小: $REAL_SIZE"
}

# 数据库连接测试函数
test_connection() {
    log "测试数据库连接..."
    
    mysql -u $DB_USER -p$DB_PASS $DB_NAME -e "SELECT 1;" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log "数据库连接正常"
        return 0
    else
        log "错误: 数据库连接失败"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "ITBook数据备份脚本"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  full        执行全量备份"
    echo "  core        执行核心表备份"
    echo "  incremental 执行增量备份"
    echo "  cleanup     清理旧备份文件"
    echo "  status      检查备份状态"
    echo "  verify FILE 验证备份文件"
    echo "  test        测试数据库连接"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 full                    # 执行全量备份"
    echo "  $0 core                    # 执行核心表备份"
    echo "  $0 verify backup.sql.gz    # 验证备份文件"
}

# 主执行逻辑
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    # 创建日志文件
    touch $LOG_FILE
    
    case "$1" in
        "full")
            log "=== 开始全量备份任务 ==="
            if test_connection; then
                full_backup
                EXIT_CODE=$?
            else
                EXIT_CODE=1
            fi
            log "=== 全量备份任务结束 ==="
            ;;
        "core")
            log "=== 开始核心表备份任务 ==="
            if test_connection; then
                core_tables_backup
                EXIT_CODE=$?
            else
                EXIT_CODE=1
            fi
            log "=== 核心表备份任务结束 ==="
            ;;
        "incremental")
            log "=== 开始增量备份任务 ==="
            if test_connection; then
                incremental_backup
                EXIT_CODE=$?
            else
                EXIT_CODE=1
            fi
            log "=== 增量备份任务结束 ==="
            ;;
        "cleanup")
            log "=== 开始清理任务 ==="
            cleanup_old_backups
            EXIT_CODE=$?
            log "=== 清理任务结束 ==="
            ;;
        "status")
            log "=== 备份状态检查 ==="
            check_backup_status
            EXIT_CODE=$?
            log "=== 状态检查结束 ==="
            ;;
        "verify")
            if [ -z "$2" ]; then
                echo "错误: 请指定要验证的备份文件"
                exit 1
            fi
            verify_backup "$2"
            EXIT_CODE=$?
            ;;
        "test")
            test_connection
            EXIT_CODE=$?
            ;;
        "help"|"-h"|"--help")
            show_help
            EXIT_CODE=0
            ;;
        *)
            echo "错误: 未知选项 '$1'"
            show_help
            EXIT_CODE=1
            ;;
    esac
    
    exit $EXIT_CODE
}

# 执行主函数
main "$@"
