package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户反馈处理器
 * 
 * 核心功能：
 * 1. 收集和处理用户对学习内容的反馈
 * 2. 分析反馈数据，提取改进建议
 * 3. 基于反馈触发路径调整
 * 4. 提供反馈统计和分析报告
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackProcessor {

    private final UserStepProgressRepository stepProgressRepository;
    private final DynamicLearningPathRepository dynamicPathRepository;
    private final DynamicPathStepRepository dynamicStepRepository;
    private final PathAdjustmentEngine pathAdjustmentEngine;

    /**
     * 反馈类型枚举
     */
    public enum FeedbackType {
        DIFFICULTY_RATING,      // 难度评分
        QUALITY_RATING,         // 质量评分
        CONTENT_FEEDBACK,       // 内容反馈
        LEARNING_EXPERIENCE,    // 学习体验
        SUGGESTION,             // 改进建议
        BUG_REPORT,            // 问题报告
        GENERAL_COMMENT        // 一般评论
    }

    /**
     * 反馈情感枚举
     */
    public enum FeedbackSentiment {
        POSITIVE,    // 积极
        NEUTRAL,     // 中性
        NEGATIVE     // 消极
    }

    /**
     * 用户反馈数据
     */
    public static class UserFeedback {
        private Long userId;
        private Long pathId;
        private Long stepId;
        private FeedbackType feedbackType;
        private Integer rating;              // 评分 (1-5)
        private String comment;              // 文字反馈
        private FeedbackSentiment sentiment; // 情感倾向
        private LocalDateTime feedbackTime;
        private Map<String, Object> metadata; // 额外数据

        // Constructors
        public UserFeedback() {
            this.feedbackTime = LocalDateTime.now();
            this.metadata = new HashMap<>();
        }

        public UserFeedback(Long userId, Long pathId, Long stepId, FeedbackType feedbackType) {
            this();
            this.userId = userId;
            this.pathId = pathId;
            this.stepId = stepId;
            this.feedbackType = feedbackType;
        }

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public Long getStepId() { return stepId; }
        public void setStepId(Long stepId) { this.stepId = stepId; }
        
        public FeedbackType getFeedbackType() { return feedbackType; }
        public void setFeedbackType(FeedbackType feedbackType) { this.feedbackType = feedbackType; }
        
        public Integer getRating() { return rating; }
        public void setRating(Integer rating) { this.rating = rating; }
        
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        
        public FeedbackSentiment getSentiment() { return sentiment; }
        public void setSentiment(FeedbackSentiment sentiment) { this.sentiment = sentiment; }
        
        public LocalDateTime getFeedbackTime() { return feedbackTime; }
        public void setFeedbackTime(LocalDateTime feedbackTime) { this.feedbackTime = feedbackTime; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 反馈分析结果
     */
    public static class FeedbackAnalysis {
        private Long pathId;
        private LocalDateTime analysisTime;
        
        // 整体统计
        private Integer totalFeedbackCount;
        private Double averageRating;
        private Map<FeedbackType, Integer> feedbackTypeDistribution;
        private Map<FeedbackSentiment, Integer> sentimentDistribution;
        
        // 问题识别
        private List<String> identifiedIssues;
        private List<Long> problematicSteps;
        private Double overallSatisfaction;
        
        // 改进建议
        private List<String> improvementSuggestions;
        private List<String> userSuggestions;
        private String priorityAction;
        
        // 趋势分析
        private String feedbackTrend;
        private Double satisfactionTrend;

        // Getters and Setters
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public LocalDateTime getAnalysisTime() { return analysisTime; }
        public void setAnalysisTime(LocalDateTime analysisTime) { this.analysisTime = analysisTime; }
        
        public Integer getTotalFeedbackCount() { return totalFeedbackCount; }
        public void setTotalFeedbackCount(Integer totalFeedbackCount) { this.totalFeedbackCount = totalFeedbackCount; }
        
        public Double getAverageRating() { return averageRating; }
        public void setAverageRating(Double averageRating) { this.averageRating = averageRating; }
        
        public Map<FeedbackType, Integer> getFeedbackTypeDistribution() { return feedbackTypeDistribution; }
        public void setFeedbackTypeDistribution(Map<FeedbackType, Integer> feedbackTypeDistribution) { this.feedbackTypeDistribution = feedbackTypeDistribution; }
        
        public Map<FeedbackSentiment, Integer> getSentimentDistribution() { return sentimentDistribution; }
        public void setSentimentDistribution(Map<FeedbackSentiment, Integer> sentimentDistribution) { this.sentimentDistribution = sentimentDistribution; }
        
        public List<String> getIdentifiedIssues() { return identifiedIssues; }
        public void setIdentifiedIssues(List<String> identifiedIssues) { this.identifiedIssues = identifiedIssues; }
        
        public List<Long> getProblematicSteps() { return problematicSteps; }
        public void setProblematicSteps(List<Long> problematicSteps) { this.problematicSteps = problematicSteps; }
        
        public Double getOverallSatisfaction() { return overallSatisfaction; }
        public void setOverallSatisfaction(Double overallSatisfaction) { this.overallSatisfaction = overallSatisfaction; }
        
        public List<String> getImprovementSuggestions() { return improvementSuggestions; }
        public void setImprovementSuggestions(List<String> improvementSuggestions) { this.improvementSuggestions = improvementSuggestions; }
        
        public List<String> getUserSuggestions() { return userSuggestions; }
        public void setUserSuggestions(List<String> userSuggestions) { this.userSuggestions = userSuggestions; }
        
        public String getPriorityAction() { return priorityAction; }
        public void setPriorityAction(String priorityAction) { this.priorityAction = priorityAction; }
        
        public String getFeedbackTrend() { return feedbackTrend; }
        public void setFeedbackTrend(String feedbackTrend) { this.feedbackTrend = feedbackTrend; }
        
        public Double getSatisfactionTrend() { return satisfactionTrend; }
        public void setSatisfactionTrend(Double satisfactionTrend) { this.satisfactionTrend = satisfactionTrend; }
    }

    /**
     * 处理用户反馈
     * 
     * @param feedback 用户反馈
     * @return 处理结果
     */
    @Transactional
    public Map<String, Object> processFeedback(UserFeedback feedback) {
        log.info("📝 处理用户反馈: userId={}, pathId={}, stepId={}, type={}", 
                feedback.getUserId(), feedback.getPathId(), feedback.getStepId(), feedback.getFeedbackType());

        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 验证反馈数据
            validateFeedback(feedback);

            // 2. 分析反馈情感
            if (feedback.getSentiment() == null && feedback.getComment() != null) {
                FeedbackSentiment sentiment = analyzeSentiment(feedback.getComment());
                feedback.setSentiment(sentiment);
            }

            // 3. 存储反馈到相关实体
            storeFeedback(feedback);

            // 4. 检查是否需要触发路径调整
            boolean adjustmentTriggered = checkAdjustmentTrigger(feedback);

            // 5. 构建处理结果
            result.put("feedbackId", System.currentTimeMillis()); // 临时ID
            result.put("userId", feedback.getUserId());
            result.put("pathId", feedback.getPathId());
            result.put("stepId", feedback.getStepId());
            result.put("feedbackType", feedback.getFeedbackType());
            result.put("sentiment", feedback.getSentiment());
            result.put("adjustmentTriggered", adjustmentTriggered);
            result.put("processedAt", feedback.getFeedbackTime());
            result.put("status", "processed");

            log.info("✅ 用户反馈处理完成: userId={}, adjustmentTriggered={}", 
                    feedback.getUserId(), adjustmentTriggered);

            return result;

        } catch (Exception e) {
            log.error("❌ 用户反馈处理失败: userId={}, pathId={}", 
                    feedback.getUserId(), feedback.getPathId(), e);
            throw new RuntimeException("用户反馈处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 分析路径反馈
     * 
     * @param pathId 路径ID
     * @return 反馈分析结果
     */
    public FeedbackAnalysis analyzeFeedback(Long pathId) {
        log.info("📊 分析路径反馈: pathId={}", pathId);

        try {
            FeedbackAnalysis analysis = new FeedbackAnalysis();
            analysis.setPathId(pathId);
            analysis.setAnalysisTime(LocalDateTime.now());

            // 获取路径的所有反馈数据（从步骤进度中获取）
            List<UserStepProgress> stepProgresses = getStepProgressesForPath(pathId);
            
            if (stepProgresses.isEmpty()) {
                log.warn("⚠️ 路径暂无反馈数据: pathId={}", pathId);
                return createDefaultFeedbackAnalysis(analysis);
            }

            // 分析反馈统计
            analyzeFeedbackStatistics(analysis, stepProgresses);
            
            // 识别问题和改进点
            identifyIssuesAndImprovements(analysis, stepProgresses);
            
            // 生成改进建议
            generateImprovementSuggestions(analysis, stepProgresses);

            log.info("✅ 路径反馈分析完成: pathId={}, satisfaction={}", 
                    pathId, analysis.getOverallSatisfaction());

            return analysis;

        } catch (Exception e) {
            log.error("❌ 路径反馈分析失败: pathId={}", pathId, e);
            throw new RuntimeException("路径反馈分析失败", e);
        }
    }

    /**
     * 获取反馈统计信息
     * 
     * @param pathId 路径ID
     * @return 统计信息
     */
    public Map<String, Object> getFeedbackStatistics(Long pathId) {
        log.info("📈 获取反馈统计: pathId={}", pathId);

        try {
            Map<String, Object> statistics = new HashMap<>();

            // 获取步骤进度数据
            List<UserStepProgress> stepProgresses = getStepProgressesForPath(pathId);

            // 计算基础统计
            int totalFeedbacks = (int) stepProgresses.stream()
                    .filter(p -> p.getDifficultyRating() != null || p.getQualityRating() != null)
                    .count();

            double avgDifficulty = stepProgresses.stream()
                    .filter(p -> p.getDifficultyRating() != null)
                    .mapToInt(UserStepProgress::getDifficultyRating)
                    .average()
                    .orElse(0.0);

            double avgQuality = stepProgresses.stream()
                    .filter(p -> p.getQualityRating() != null)
                    .mapToInt(UserStepProgress::getQualityRating)
                    .average()
                    .orElse(0.0);

            // 构建统计结果
            statistics.put("pathId", pathId);
            statistics.put("totalFeedbacks", totalFeedbacks);
            statistics.put("averageDifficultyRating", BigDecimal.valueOf(avgDifficulty).setScale(2, RoundingMode.HALF_UP));
            statistics.put("averageQualityRating", BigDecimal.valueOf(avgQuality).setScale(2, RoundingMode.HALF_UP));
            statistics.put("overallSatisfaction", calculateOverallSatisfaction(avgDifficulty, avgQuality));
            statistics.put("statisticsTime", LocalDateTime.now());

            // 评分分布
            Map<Integer, Long> difficultyDistribution = stepProgresses.stream()
                    .filter(p -> p.getDifficultyRating() != null)
                    .collect(Collectors.groupingBy(UserStepProgress::getDifficultyRating, Collectors.counting()));
            
            Map<Integer, Long> qualityDistribution = stepProgresses.stream()
                    .filter(p -> p.getQualityRating() != null)
                    .collect(Collectors.groupingBy(UserStepProgress::getQualityRating, Collectors.counting()));

            statistics.put("difficultyDistribution", difficultyDistribution);
            statistics.put("qualityDistribution", qualityDistribution);

            log.info("✅ 反馈统计获取完成: pathId={}, totalFeedbacks={}", pathId, totalFeedbacks);

            return statistics;

        } catch (Exception e) {
            log.error("❌ 反馈统计获取失败: pathId={}", pathId, e);
            throw new RuntimeException("反馈统计获取失败", e);
        }
    }

    /**
     * 验证反馈数据
     */
    private void validateFeedback(UserFeedback feedback) {
        if (feedback.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (feedback.getFeedbackType() == null) {
            throw new IllegalArgumentException("反馈类型不能为空");
        }
        if (feedback.getRating() != null && (feedback.getRating() < 1 || feedback.getRating() > 5)) {
            throw new IllegalArgumentException("评分必须在1-5之间");
        }
    }

    /**
     * 分析反馈情感
     */
    private FeedbackSentiment analyzeSentiment(String comment) {
        if (comment == null || comment.trim().isEmpty()) {
            return FeedbackSentiment.NEUTRAL;
        }

        // 简化的情感分析（实际项目中可以使用更复杂的NLP算法）
        String lowerComment = comment.toLowerCase();

        // 积极词汇
        String[] positiveWords = {"好", "棒", "优秀", "满意", "喜欢", "推荐", "清楚", "有用", "helpful", "good", "great", "excellent"};
        // 消极词汇
        String[] negativeWords = {"差", "糟糕", "困难", "不满", "讨厌", "问题", "错误", "难懂", "bad", "poor", "difficult", "confusing"};

        int positiveCount = 0;
        int negativeCount = 0;

        for (String word : positiveWords) {
            if (lowerComment.contains(word)) {
                positiveCount++;
            }
        }

        for (String word : negativeWords) {
            if (lowerComment.contains(word)) {
                negativeCount++;
            }
        }

        if (positiveCount > negativeCount) {
            return FeedbackSentiment.POSITIVE;
        } else if (negativeCount > positiveCount) {
            return FeedbackSentiment.NEGATIVE;
        } else {
            return FeedbackSentiment.NEUTRAL;
        }
    }

    /**
     * 存储反馈
     */
    private void storeFeedback(UserFeedback feedback) {
        if (feedback.getStepId() != null) {
            // 更新步骤进度中的反馈信息
            Optional<UserStepProgress> progressOpt = stepProgressRepository
                    .findByUserIdAndStepId(feedback.getUserId(), feedback.getStepId());

            if (progressOpt.isPresent()) {
                UserStepProgress progress = progressOpt.get();

                // 根据反馈类型更新相应字段
                switch (feedback.getFeedbackType()) {
                    case DIFFICULTY_RATING:
                        if (feedback.getRating() != null) {
                            progress.setDifficultyRating(feedback.getRating());
                        }
                        break;
                    case QUALITY_RATING:
                        if (feedback.getRating() != null) {
                            progress.setQualityRating(feedback.getRating());
                        }
                        break;
                    case CONTENT_FEEDBACK:
                    case GENERAL_COMMENT:
                    case LEARNING_EXPERIENCE:
                    case SUGGESTION:
                    case BUG_REPORT:
                        if (feedback.getComment() != null) {
                            progress.setNotes(feedback.getComment());
                        }
                        break;
                }

                stepProgressRepository.save(progress);
                log.debug("更新步骤反馈: stepId={}, type={}", feedback.getStepId(), feedback.getFeedbackType());
            }
        }
    }

    /**
     * 检查是否需要触发路径调整
     */
    private boolean checkAdjustmentTrigger(UserFeedback feedback) {
        boolean shouldTrigger = false;

        // 检查触发条件
        if (feedback.getFeedbackType() == FeedbackType.DIFFICULTY_RATING && feedback.getRating() != null) {
            if (feedback.getRating() >= 4) {
                // 难度评分过高，触发调整
                triggerPathAdjustment(feedback, PathAdjustmentEngine.AdjustmentTrigger.HIGH_DIFFICULTY_RATING);
                shouldTrigger = true;
            }
        }

        if (feedback.getFeedbackType() == FeedbackType.QUALITY_RATING && feedback.getRating() != null) {
            if (feedback.getRating() <= 2) {
                // 质量评分过低，触发调整
                triggerPathAdjustment(feedback, PathAdjustmentEngine.AdjustmentTrigger.LOW_QUALITY_RATING);
                shouldTrigger = true;
            }
        }

        if (feedback.getSentiment() == FeedbackSentiment.NEGATIVE) {
            // 消极反馈，触发调整
            triggerPathAdjustment(feedback, PathAdjustmentEngine.AdjustmentTrigger.USER_FEEDBACK);
            shouldTrigger = true;
        }

        return shouldTrigger;
    }

    /**
     * 触发路径调整
     */
    private void triggerPathAdjustment(UserFeedback feedback, PathAdjustmentEngine.AdjustmentTrigger trigger) {
        try {
            PathAdjustmentEngine.PathAdjustmentRequest request =
                    new PathAdjustmentEngine.PathAdjustmentRequest(feedback.getUserId(), feedback.getPathId(), trigger);
            request.setReason("基于用户反馈触发: " + feedback.getFeedbackType() + " - " + feedback.getComment());

            // 异步触发调整（这里简化为同步调用）
            pathAdjustmentEngine.adjustPath(request);

            log.info("🔧 基于反馈触发路径调整: userId={}, pathId={}, trigger={}",
                    feedback.getUserId(), feedback.getPathId(), trigger);

        } catch (Exception e) {
            log.error("❌ 基于反馈触发路径调整失败: userId={}, pathId={}",
                    feedback.getUserId(), feedback.getPathId(), e);
        }
    }

    /**
     * 获取路径的步骤进度列表
     */
    private List<UserStepProgress> getStepProgressesForPath(Long pathId) {
        // 这里需要根据pathId查找对应的步骤，然后获取用户的进度
        // 暂时返回所有步骤进度，后续可以优化为按路径过滤
        return stepProgressRepository.findAll().stream()
                .limit(20) // 限制数量避免性能问题
                .collect(Collectors.toList());
    }

    /**
     * 创建默认反馈分析
     */
    private FeedbackAnalysis createDefaultFeedbackAnalysis(FeedbackAnalysis analysis) {
        analysis.setTotalFeedbackCount(0);
        analysis.setAverageRating(0.0);
        analysis.setFeedbackTypeDistribution(new HashMap<>());
        analysis.setSentimentDistribution(new HashMap<>());
        analysis.setIdentifiedIssues(Arrays.asList("暂无反馈数据"));
        analysis.setProblematicSteps(new ArrayList<>());
        analysis.setOverallSatisfaction(0.0);
        analysis.setImprovementSuggestions(Arrays.asList("收集更多用户反馈"));
        analysis.setUserSuggestions(new ArrayList<>());
        analysis.setPriorityAction("等待用户反馈");
        analysis.setFeedbackTrend("UNKNOWN");
        analysis.setSatisfactionTrend(0.0);

        return analysis;
    }

    /**
     * 分析反馈统计
     */
    private void analyzeFeedbackStatistics(FeedbackAnalysis analysis, List<UserStepProgress> stepProgresses) {
        // 计算总反馈数
        int totalFeedbacks = (int) stepProgresses.stream()
                .filter(p -> p.getDifficultyRating() != null || p.getQualityRating() != null)
                .count();
        analysis.setTotalFeedbackCount(totalFeedbacks);

        // 计算平均评分
        double avgRating = stepProgresses.stream()
                .filter(p -> p.getQualityRating() != null)
                .mapToInt(UserStepProgress::getQualityRating)
                .average()
                .orElse(0.0);
        analysis.setAverageRating(avgRating);

        // 计算整体满意度
        double avgDifficulty = stepProgresses.stream()
                .filter(p -> p.getDifficultyRating() != null)
                .mapToInt(UserStepProgress::getDifficultyRating)
                .average()
                .orElse(3.0);

        analysis.setOverallSatisfaction(calculateOverallSatisfaction(avgDifficulty, avgRating));

        // 反馈类型分布（简化版本）
        Map<FeedbackType, Integer> typeDistribution = new HashMap<>();
        typeDistribution.put(FeedbackType.DIFFICULTY_RATING, (int) stepProgresses.stream()
                .filter(p -> p.getDifficultyRating() != null).count());
        typeDistribution.put(FeedbackType.QUALITY_RATING, (int) stepProgresses.stream()
                .filter(p -> p.getQualityRating() != null).count());
        analysis.setFeedbackTypeDistribution(typeDistribution);

        // 情感分布（简化版本）
        Map<FeedbackSentiment, Integer> sentimentDistribution = new HashMap<>();
        sentimentDistribution.put(FeedbackSentiment.POSITIVE, 0);
        sentimentDistribution.put(FeedbackSentiment.NEUTRAL, 0);
        sentimentDistribution.put(FeedbackSentiment.NEGATIVE, 0);
        analysis.setSentimentDistribution(sentimentDistribution);
    }

    /**
     * 识别问题和改进点
     */
    private void identifyIssuesAndImprovements(FeedbackAnalysis analysis, List<UserStepProgress> stepProgresses) {
        List<String> issues = new ArrayList<>();
        List<Long> problematicSteps = new ArrayList<>();

        // 识别难度过高的步骤
        for (UserStepProgress progress : stepProgresses) {
            if (progress.getDifficultyRating() != null && progress.getDifficultyRating() >= 4) {
                problematicSteps.add(progress.getStepId());
            }
        }

        // 识别质量问题
        long lowQualityCount = stepProgresses.stream()
                .filter(p -> p.getQualityRating() != null && p.getQualityRating() <= 2)
                .count();

        if (lowQualityCount > 0) {
            issues.add("存在 " + lowQualityCount + " 个质量评分较低的步骤");
        }

        if (!problematicSteps.isEmpty()) {
            issues.add("存在 " + problematicSteps.size() + " 个难度过高的步骤");
        }

        if (analysis.getOverallSatisfaction() < 0.6) {
            issues.add("整体满意度较低，需要改进");
        }

        analysis.setIdentifiedIssues(issues);
        analysis.setProblematicSteps(problematicSteps);
    }

    /**
     * 生成改进建议
     */
    private void generateImprovementSuggestions(FeedbackAnalysis analysis, List<UserStepProgress> stepProgresses) {
        List<String> suggestions = new ArrayList<>();

        if (analysis.getOverallSatisfaction() < 0.6) {
            suggestions.add("考虑降低整体难度或提供更多学习支持");
        }

        if (!analysis.getProblematicSteps().isEmpty()) {
            suggestions.add("优化难度过高的步骤，提供替代学习路径");
        }

        if (analysis.getAverageRating() < 3.0) {
            suggestions.add("改进学习内容质量，增加互动性");
        }

        if (suggestions.isEmpty()) {
            suggestions.add("继续保持当前质量，收集更多反馈");
        }

        analysis.setImprovementSuggestions(suggestions);
        analysis.setUserSuggestions(new ArrayList<>()); // 暂时为空
        analysis.setPriorityAction(suggestions.isEmpty() ? "保持现状" : suggestions.get(0));
        analysis.setFeedbackTrend("STABLE");
        analysis.setSatisfactionTrend(0.0);
    }

    /**
     * 计算整体满意度
     */
    private double calculateOverallSatisfaction(double avgDifficulty, double avgQuality) {
        // 简化计算：质量评分权重70%，难度适中性权重30%
        double qualityScore = avgQuality / 5.0;
        double difficultyScore = 1.0 - Math.abs(avgDifficulty - 3.0) / 2.0; // 3.0为理想难度

        return qualityScore * 0.7 + difficultyScore * 0.3;
    }
}
