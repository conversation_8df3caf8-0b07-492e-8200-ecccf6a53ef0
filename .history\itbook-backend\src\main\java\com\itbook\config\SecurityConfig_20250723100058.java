package com.itbook.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security安全配置
 * 配置API访问权限和CORS跨域支持
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF，因为我们使用JWT
            .csrf().disable()
            
            // 配置CORS
            .cors().configurationSource(corsConfigurationSource())
            
            .and()
            
            // 配置会话管理为无状态
            .sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            
            .and()
            
            // 配置请求授权
            .authorizeRequests()
                // 允许健康检查端点
                .antMatchers("/health", "/actuator/health").permitAll()
                
                // 允许Swagger文档访问
                .antMatchers("/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**").permitAll()
                
                // 允许认证相关端点
                .antMatchers("/auth/**").permitAll()
                
                // 允许课程相关端点（开发阶段）
                .antMatchers("/courses/**").permitAll()
                
                // 允许文章相关端点（开发阶段）
                .antMatchers("/articles/**").permitAll()

                // 允许问答相关端点（开发阶段）
                .antMatchers("/questions/**").permitAll()
                .antMatchers("/answers/**").permitAll()

                // 允许资讯相关端点（开发阶段）
                .antMatchers("/news/**").permitAll()

                // 允许职位相关端点（开发阶段）
                .antMatchers("/jobs/**").permitAll()
                
                // 允许公司相关端点（开发阶段）
                .antMatchers("/companies/**").permitAll()

                // 允许学习路径相关端点（开发阶段）
                .antMatchers("/learning-paths/**").permitAll()

                // 允许岗位标准路径相关端点（开发阶段）
                .antMatchers("/job-standard-paths/**").permitAll()

                // 允许路径推荐相关端点（开发阶段）
                .antMatchers("/path-recommendations/**").permitAll()

                // 允许用户画像相关端点（开发阶段）
                .antMatchers("/user-profiles/**").permitAll()

                // 允许用户技能评估相关端点（开发阶段）
                .antMatchers("/user-skill-assessments/**").permitAll()

                // 允许原子技能相关端点（开发阶段）
                .antMatchers("/atomic-skills/**").permitAll()

                // 允许技能关系相关端点（开发阶段）
                .antMatchers("/skill-relationships/**").permitAll()

                // 允许技能图算法相关端点（开发阶段）
                .antMatchers("/skill-graph/**").permitAll()

                // 允许个性化学习相关端点（开发阶段）
                .antMatchers("/personalized-learning/**").permitAll()

                // 允许个性化推荐相关端点（开发阶段）
                .antMatchers("/personalized-recommendations/**").permitAll()

                // 允许个性化路径相关端点（开发阶段）
                .antMatchers("/personalized-paths/**").permitAll()

                // 允许前置技能分析相关端点（开发阶段）
                .antMatchers("/prerequisite-analysis/**").permitAll()

                // 允许统一进度相关端点（开发阶段）
                .antMatchers("/unified-progress/**").permitAll()
                .antMatchers("/users/*/progress/**").permitAll()

                // 允许市场分析相关端点（开发阶段）
                .antMatchers("/market-analysis/**").permitAll()

                // 允许职业目标相关端点（开发阶段）
                .antMatchers("/career-goals/**").permitAll()

                // 允许项目相关端点（开发阶段）
                .antMatchers("/projects/**").permitAll()

                // 允许用户相关端点（开发阶段）
                .antMatchers("/users/**").permitAll()

                // 允许简历相关端点（开发阶段）
                .antMatchers("/resumes/**").permitAll()

                // 允许文件上传相关端点（开发阶段）
                .antMatchers("/upload/**").permitAll()
                .antMatchers("/uploads/**").permitAll()

                // 允许数据迁移相关端点（开发阶段）
                .antMatchers("/data-migration/**").permitAll()

                // 允许用户行为分析相关端点（开发阶段）
                .antMatchers("/user-behavior-analysis/**").permitAll()

                // 允许动态路径生成相关端点（开发阶段）
                .antMatchers("/dynamic-path-generation/**").permitAll()

                // 允许学习进度跟踪相关端点（开发阶段）
                .antMatchers("/learning-progress-tracking/**").permitAll()

                // 允许路径调整相关端点（开发阶段）
                .antMatchers("/path-adjustment/**").permitAll()

                // 其他请求需要认证
                .anyRequest().authenticated()
            
            .and()
            
            // 禁用默认登录页面
            .formLogin().disable()
            
            // 禁用HTTP Basic认证
            .httpBasic().disable();
    }
    
    /**
     * CORS跨域配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许的源
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        
        // 允许的HTTP方法
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        
        // 允许的请求头
        configuration.setAllowedHeaders(Arrays.asList("*"));
        
        // 允许发送凭证
        configuration.setAllowCredentials(true);
        
        // 预检请求的缓存时间
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
    
    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
