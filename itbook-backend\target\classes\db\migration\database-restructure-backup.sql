-- ===================================================================
-- ITBook数据库表结构重构 - 数据备份脚本
-- 创建时间: 2025-07-14
-- 作者: ITBook Team
-- 
-- 备份career_path、career_level、project、career_project表的数据
-- 在重构前确保数据安全
-- ===================================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===================================================================
-- 第一步：创建备份表
-- ===================================================================

-- 备份career_path表
DROP TABLE IF EXISTS `career_path_backup`;
CREATE TABLE `career_path_backup` AS SELECT * FROM `career_path`;

-- 备份career_level表
DROP TABLE IF EXISTS `career_level_backup`;
CREATE TABLE `career_level_backup` AS SELECT * FROM `career_level`;

-- 备份project表
DROP TABLE IF EXISTS `project_backup`;
CREATE TABLE `project_backup` AS SELECT * FROM `project`;

-- 备份career_project表
DROP TABLE IF EXISTS `career_project_backup`;
CREATE TABLE `career_project_backup` AS SELECT * FROM `career_project`;

-- ===================================================================
-- 第二步：数据完整性检查
-- ===================================================================

-- 检查career_path表数据
SELECT 
    'career_path' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT level) as unique_levels
FROM career_path
UNION ALL
-- 检查career_level表数据
SELECT 
    'career_level' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT level_code) as unique_levels
FROM career_level
UNION ALL
-- 检查project表数据
SELECT 
    'project' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT difficulty) as unique_difficulties,
    COUNT(DISTINCT type) as unique_types
FROM project
UNION ALL
-- 检查career_project表数据
SELECT 
    'career_project' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT difficulty) as unique_difficulties
FROM career_project;

-- ===================================================================
-- 第三步：分析数据重叠情况
-- ===================================================================

-- 分析career_path和career_level的重叠情况
SELECT 
    cp.career_goal_id,
    cp.level as career_path_level,
    cl.level_code as career_level_code,
    cp.salary_min as cp_salary_min,
    cp.salary_max as cp_salary_max,
    cl.salary_range_min as cl_salary_min,
    cl.salary_range_max as cl_salary_max
FROM career_path cp
LEFT JOIN career_level cl ON cp.career_goal_id = cl.career_goal_id AND cp.level = cl.level_code
ORDER BY cp.career_goal_id, cp.level;

-- 分析project和career_project的字段对比
SELECT 
    'project表字段' as source,
    'title, description, difficulty, technologies, duration, participants, rating, status, type, is_featured, is_trending, view_count, like_count, bookmark_count, source_url, demo_url' as fields
UNION ALL
SELECT 
    'career_project表字段' as source,
    'career_goal_id, project_id, title, description, difficulty, tech_stack, estimated_time, business_scenario, learning_objectives, deliverables' as fields;

SET FOREIGN_KEY_CHECKS = 1;

-- ===================================================================
-- 备份完成提示
-- ===================================================================
SELECT 'Database backup completed successfully!' as status;
