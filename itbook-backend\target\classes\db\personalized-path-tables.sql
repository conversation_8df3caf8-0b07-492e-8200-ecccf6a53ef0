-- =====================================================
-- ITBook个性化路径系统数据库表结构
-- 基于"标准路径+个性化分支"混合模式设计
-- 创建时间: 2025-07-10
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户画像表 - 存储用户的学习偏好和技能水平信息
-- ----------------------------
DROP TABLE IF EXISTS `user_profile`;
CREATE TABLE `user_profile` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `current_skill_level` int DEFAULT 1 COMMENT '当前技能水平(1-5级)',
  `learning_style` enum('THEORETICAL','PRACTICAL','PROJECT_DRIVEN','MIXED') DEFAULT 'MIXED' COMMENT '学习风格偏好',
  `available_time_per_week` int DEFAULT 10 COMMENT '每周可用学习时间(小时)',
  `career_goal` varchar(200) DEFAULT NULL COMMENT '职业目标',
  `has_programming_experience` tinyint(1) DEFAULT 0 COMMENT '是否有编程经验',
  `preferred_learning_pace` enum('FAST','NORMAL','SLOW') DEFAULT 'NORMAL' COMMENT '偏好的学习节奏',
  `technical_background` json DEFAULT NULL COMMENT '技术背景信息',
  `learning_motivation` varchar(500) DEFAULT NULL COMMENT '学习动机',
  `target_salary_range` varchar(50) DEFAULT NULL COMMENT '期望薪资范围',
  `work_experience_years` int DEFAULT 0 COMMENT '工作经验年数',
  `education_level` varchar(50) DEFAULT NULL COMMENT '教育水平',
  `preferred_job_locations` json DEFAULT NULL COMMENT '偏好工作地点',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_profile_user_id` (`user_id`),
  CONSTRAINT `fk_user_profile_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户画像表';

-- ----------------------------
-- 岗位技能要求表 - 存储每个岗位的技能要求详情
-- ----------------------------
DROP TABLE IF EXISTS `job_skill_requirement`;
CREATE TABLE `job_skill_requirement` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `job_id` bigint NOT NULL COMMENT '岗位ID',
  `skill_name` varchar(100) NOT NULL COMMENT '技能名称',
  `skill_category` varchar(50) DEFAULT NULL COMMENT '技能分类',
  `required_level` int DEFAULT 1 COMMENT '要求熟练度(1-5级)',
  `is_core_skill` tinyint(1) DEFAULT 0 COMMENT '是否为核心技能',
  `weight` decimal(3,2) DEFAULT 1.00 COMMENT '技能权重',
  `description` text DEFAULT NULL COMMENT '技能描述',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_job_skill_job_id` (`job_id`),
  KEY `idx_job_skill_name` (`skill_name`),
  CONSTRAINT `fk_job_skill_job_id` FOREIGN KEY (`job_id`) REFERENCES `job` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位技能要求表';

-- ----------------------------
-- 个性化学习路径记录表 - 记录系统为用户生成的个性化路径
-- ----------------------------
DROP TABLE IF EXISTS `personalized_path`;
CREATE TABLE `personalized_path` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `job_id` bigint NOT NULL COMMENT '目标岗位ID',
  `generated_path_id` bigint NOT NULL COMMENT '生成的个性化学习路径ID',
  `personalization_score` decimal(5,3) DEFAULT 0.000 COMMENT '个性化匹配分数(0-1)',
  `generation_reason` text DEFAULT NULL COMMENT '生成理由',
  `algorithm_version` varchar(20) DEFAULT 'v1.0' COMMENT '算法版本',
  `personalization_factors` json DEFAULT NULL COMMENT '个性化因子详情',
  `is_accepted` tinyint(1) DEFAULT NULL COMMENT '用户是否接受个性化路径',
  `user_feedback` text DEFAULT NULL COMMENT '用户反馈',
  `feedback_rating` int DEFAULT NULL COMMENT '用户评分(1-5)',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_personalized_path_user_id` (`user_id`),
  KEY `idx_personalized_path_job_id` (`job_id`),
  KEY `idx_personalized_path_path_id` (`generated_path_id`),
  KEY `idx_personalized_path_score` (`personalization_score`),
  CONSTRAINT `fk_personalized_path_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_personalized_path_job_id` FOREIGN KEY (`job_id`) REFERENCES `job` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_personalized_path_path_id` FOREIGN KEY (`generated_path_id`) REFERENCES `learning_path` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个性化学习路径记录表';

-- ----------------------------
-- 用户技能评估表 - 记录用户的技能评估结果
-- ----------------------------
DROP TABLE IF EXISTS `user_skill_assessment`;
CREATE TABLE `user_skill_assessment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `skill_name` varchar(100) NOT NULL COMMENT '技能名称',
  `skill_category` varchar(50) DEFAULT NULL COMMENT '技能分类',
  `current_level` int DEFAULT 1 COMMENT '当前水平(1-5级)',
  `target_level` int DEFAULT NULL COMMENT '目标水平(1-5级)',
  `assessment_method` enum('SELF_EVALUATION','QUIZ','PROJECT','INTERVIEW') DEFAULT 'SELF_EVALUATION' COMMENT '评估方式',
  `assessment_score` decimal(5,2) DEFAULT NULL COMMENT '评估分数',
  `confidence_level` decimal(3,2) DEFAULT NULL COMMENT '置信度(0-1)',
  `evidence_data` json DEFAULT NULL COMMENT '评估证据数据',
  `last_assessed_at` datetime(6) DEFAULT NULL COMMENT '最后评估时间',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_skill_user_skill` (`user_id`, `skill_name`),
  KEY `idx_user_skill_skill_name` (`skill_name`),
  CONSTRAINT `fk_user_skill_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户技能评估表';

-- ----------------------------
-- 路径个性化规则表 - 定义路径个性化的规则
-- ----------------------------
DROP TABLE IF EXISTS `path_personalization_rule`;
CREATE TABLE `path_personalization_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_type` enum('SKILL_LEVEL','TIME_CONSTRAINT','LEARNING_STYLE','EXPERIENCE') DEFAULT 'SKILL_LEVEL' COMMENT '规则类型',
  `condition_expression` text NOT NULL COMMENT '条件表达式',
  `action_type` enum('GENERATE','EXCLUDE','MODIFY','PRIORITY') DEFAULT 'GENERATE' COMMENT '动作类型',
  `action_parameters` json DEFAULT NULL COMMENT '动作参数',
  `priority` int DEFAULT 1 COMMENT '规则优先级',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `description` text DEFAULT NULL COMMENT '规则描述',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_personalization_rule_type` (`rule_type`),
  KEY `idx_personalization_rule_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='路径个性化规则表';

SET FOREIGN_KEY_CHECKS = 1;
