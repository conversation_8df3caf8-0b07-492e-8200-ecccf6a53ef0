-- ===================================================================
-- 验证用户职业目标关联关系修复结果
-- ===================================================================

-- 1. 检查表结构是否正确修改
DESCRIBE user_career_goal;

-- 2. 检查外键约束是否正确设置
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    TABLE_NAME = 'user_career_goal' 
    AND CONSTRAINT_NAME LIKE 'fk_%';

-- 3. 检查索引是否正确创建
SHOW INDEX FROM user_career_goal WHERE Column_name = 'career_goal_id';

-- 4. 验证数据完整性
SELECT 
    COUNT(*) as total_records,
    COUNT(career_goal_id) as records_with_career_goal_id,
    COUNT(DISTINCT career_goal_id) as unique_career_goals
FROM user_career_goal;

-- 5. 检查是否有无效的外键引用
SELECT 
    ucg.id,
    ucg.user_id,
    ucg.career_goal_id,
    cg.name as career_goal_name
FROM 
    user_career_goal ucg
LEFT JOIN 
    career_goal cg ON ucg.career_goal_id = cg.id
WHERE 
    ucg.career_goal_id IS NOT NULL 
    AND cg.id IS NULL;

-- 6. 显示成功关联的记录示例
SELECT 
    ucg.id,
    ucg.user_id,
    ucg.career_goal_id,
    cg.name as career_goal_name,
    cg.category,
    ucg.target_level,
    ucg.description
FROM 
    user_career_goal ucg
INNER JOIN 
    career_goal cg ON ucg.career_goal_id = cg.id
WHERE 
    ucg.is_active = 1
LIMIT 10;
