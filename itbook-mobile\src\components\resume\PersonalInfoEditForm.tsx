/**
 * 个人信息编辑表单组件
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';

interface PersonalInfoFormData {
  fullName: string;
  email: string;
  phone: string;
  location: string;
  website: string;
  linkedin: string;
  github: string;
}

interface PersonalInfoEditFormProps {
  personalInfo?: any;
  onSave: (personalInfo: PersonalInfoFormData) => void;
  onCancel: () => void;
}

export const PersonalInfoEditForm: React.FC<PersonalInfoEditFormProps> = ({
  personalInfo,
  onSave,
  onCancel,
}) => {
  const colors = useThemeColors();

  // 表单数据状态
  const [formData, setFormData] = useState<PersonalInfoFormData>({
    fullName: personalInfo?.fullName || '',
    email: personalInfo?.email || '',
    phone: personalInfo?.phone || '',
    location: personalInfo?.location || '',
    website: personalInfo?.website || '',
    linkedin: personalInfo?.linkedin || '',
    github: personalInfo?.github || '',
  });

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = '请输入姓名';
    }

    if (!formData.email.trim()) {
      newErrors.email = '请输入邮箱';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '请输入电话号码';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入有效的手机号码';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理字段变更
  const handleFieldChange = (field: keyof PersonalInfoFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  // 处理保存
  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('提示', '请检查表单中的错误信息');
      return;
    }

    try {
      setLoading(true);
      onSave(formData);
    } catch (error) {
      console.error('保存个人信息失败:', error);
      Alert.alert('错误', '保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 渲染输入框
  const renderInput = (
    label: string,
    value: string,
    onChangeText: (text: string) => void,
    placeholder: string,
    error?: string,
    required = false,
    keyboardType: 'default' | 'email-address' | 'phone-pad' | 'url' = 'default'
  ) => (
    <View style={{ marginBottom: tokens.spacing('lg') }}>
      <Text style={{
        fontSize: tokens.fontSize('body'),
        fontWeight: tokens.fontWeight('medium') as any,
        color: colors.text,
        marginBottom: tokens.spacing('sm'),
      }}>
        {label}{required && <Text style={{ color: colors.error }}>*</Text>}
      </Text>
      <TextInput
        style={{
          borderWidth: 1,
          borderColor: error ? colors.error : colors.border,
          borderRadius: tokens.radius('sm'),
          padding: tokens.spacing('md'),
          fontSize: tokens.fontSize('body'),
          color: colors.text,
          backgroundColor: colors.surface,
          minHeight: 44,
        }}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={colors.textSecondary}
        keyboardType={keyboardType}
        autoCapitalize={keyboardType === 'email-address' ? 'none' : 'words'}
        autoCorrect={false}
      />
      {error && (
        <Text style={{
          fontSize: tokens.fontSize('caption'),
          color: colors.error,
          marginTop: tokens.spacing('xs'),
        }}>
          {error}
        </Text>
      )}
    </View>
  );

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background,
    }}>
      {/* 头部 */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: tokens.spacing('lg'),
        paddingVertical: tokens.spacing('md'),
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
        backgroundColor: colors.surface,
      }}>
        <TouchableOpacity
          onPress={onCancel}
          activeOpacity={0.7}
        >
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>

        <Text style={{
          fontSize: tokens.fontSize('title-sm'),
          fontWeight: tokens.fontWeight('bold') as any,
          color: colors.text,
        }}>
          编辑个人信息
        </Text>

        <TouchableOpacity
          onPress={handleSave}
          activeOpacity={0.7}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Text style={{
              fontSize: tokens.fontSize('body'),
              color: colors.primary,
              fontWeight: tokens.fontWeight('medium') as any,
            }}>
              保存
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* 表单内容 */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          padding: tokens.spacing('lg'),
        }}
        showsVerticalScrollIndicator={false}
      >
        {/* 基本信息 */}
        {renderInput(
          '姓名',
          formData.fullName,
          (text) => handleFieldChange('fullName', text),
          '请输入您的姓名',
          errors.fullName,
          true
        )}

        {renderInput(
          '邮箱',
          formData.email,
          (text) => handleFieldChange('email', text),
          '请输入您的邮箱地址',
          errors.email,
          true,
          'email-address'
        )}

        {renderInput(
          '电话',
          formData.phone,
          (text) => handleFieldChange('phone', text),
          '请输入您的手机号码',
          errors.phone,
          true,
          'phone-pad'
        )}

        {renderInput(
          '所在地',
          formData.location,
          (text) => handleFieldChange('location', text),
          '如：北京市朝阳区',
          errors.location
        )}

        {/* 社交链接 */}
        <Text style={{
          fontSize: tokens.fontSize('title-xs'),
          fontWeight: tokens.fontWeight('semibold') as any,
          color: colors.text,
          marginBottom: tokens.spacing('md'),
          marginTop: tokens.spacing('lg'),
        }}>
          社交链接（可选）
        </Text>

        {renderInput(
          '个人网站',
          formData.website,
          (text) => handleFieldChange('website', text),
          'https://yourwebsite.com',
          errors.website,
          false,
          'url'
        )}

        {renderInput(
          'LinkedIn',
          formData.linkedin,
          (text) => handleFieldChange('linkedin', text),
          'https://linkedin.com/in/yourprofile',
          errors.linkedin,
          false,
          'url'
        )}

        {renderInput(
          'GitHub',
          formData.github,
          (text) => handleFieldChange('github', text),
          'https://github.com/yourusername',
          errors.github,
          false,
          'url'
        )}
      </ScrollView>
    </View>
  );
};
