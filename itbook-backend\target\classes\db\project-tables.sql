-- ITBook项目展示系统数据库表结构
-- 创建时间: 2025-07-13
-- 作者: ITBook Team

-- ----------------------------
-- 项目表 - 存储项目展示信息
-- ----------------------------
DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `title` varchar(200) NOT NULL COMMENT '项目标题',
  `description` text COMMENT '项目描述',
  `image_url` varchar(500) COMMENT '项目封面图片URL',
  `difficulty` enum('BEGINNER','INTERMEDIATE','ADVANCED') NOT NULL COMMENT '项目难度级别',
  `technologies` json COMMENT '技术栈（JSON格式）',
  `duration` varchar(50) COMMENT '项目持续时间',
  `participants` int NOT NULL DEFAULT 0 COMMENT '参与人数',
  `rating` decimal(3,2) NOT NULL DEFAULT 0.00 COMMENT '项目评分',
  `status` enum('ACTIVE','COMPLETED','ARCHIVED') NOT NULL DEFAULT 'ACTIVE' COMMENT '项目状态',
  `type` enum('WEB_DEVELOPMENT','MOBILE_DEVELOPMENT','BACKEND_DEVELOPMENT','FULLSTACK_DEVELOPMENT','DATA_SCIENCE','MACHINE_LEARNING','DEVOPS','GAME_DEVELOPMENT','BLOCKCHAIN','IOT') NOT NULL DEFAULT 'WEB_DEVELOPMENT' COMMENT '项目类型',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为精选项目',
  `is_trending` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为热门项目',
  `view_count` int NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `like_count` int NOT NULL DEFAULT 0 COMMENT '点赞次数',
  `bookmark_count` int NOT NULL DEFAULT 0 COMMENT '收藏次数',
  `source_url` varchar(500) COMMENT '项目源码链接',
  `demo_url` varchar(500) COMMENT '项目演示链接',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_status` (`status`),
  KEY `idx_project_difficulty` (`difficulty`),
  KEY `idx_project_type` (`type`),
  KEY `idx_project_featured` (`is_featured`),
  KEY `idx_project_trending` (`is_trending`),
  KEY `idx_project_rating` (`rating`),
  KEY `idx_project_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目展示表';

-- ----------------------------
-- 插入测试项目数据
-- ----------------------------
INSERT INTO `project` (`title`, `description`, `image_url`, `difficulty`, `technologies`, `duration`, `participants`, `rating`, `status`, `type`, `is_featured`, `is_trending`, `view_count`, `like_count`, `bookmark_count`, `source_url`, `demo_url`) VALUES
('React Native电商应用开发', '从零开始构建一个完整的电商移动应用，包含商品展示、购物车、支付等核心功能。学习React Native开发、状态管理、API集成等技术。', 'https://via.placeholder.com/300x200/007AFF/FFFFFF?text=E-Commerce+App', 'INTERMEDIATE', '["React Native", "TypeScript", "Redux", "Expo", "Node.js"]', '8周', 156, 4.8, 'ACTIVE', 'MOBILE_DEVELOPMENT', 1, 1, 2340, 89, 67, 'https://github.com/itbook/react-native-ecommerce', 'https://expo.dev/@itbook/ecommerce-demo'),

('Spring Boot微服务架构实战', '使用Spring Boot构建企业级微服务系统，包含服务注册发现、配置中心、网关、监控等完整微服务生态。', 'https://via.placeholder.com/300x200/28A745/FFFFFF?text=Microservices', 'ADVANCED', '["Spring Boot", "Spring Cloud", "Docker", "Kubernetes", "MySQL", "Redis"]', '12周', 203, 4.9, 'ACTIVE', 'BACKEND_DEVELOPMENT', 1, 1, 3120, 145, 98, 'https://github.com/itbook/spring-boot-microservices', 'https://microservices-demo.itbook.com'),

('Vue.js全栈博客系统', '使用Vue.js + Node.js构建现代化博客系统，包含文章管理、用户系统、评论功能等。适合全栈开发学习。', 'https://via.placeholder.com/300x200/4FC08D/FFFFFF?text=Vue+Blog', 'INTERMEDIATE', '["Vue.js", "Nuxt.js", "Node.js", "Express", "MongoDB", "Element Plus"]', '6周', 134, 4.6, 'ACTIVE', 'FULLSTACK_DEVELOPMENT', 1, 0, 1890, 76, 54, 'https://github.com/itbook/vue-blog-system', 'https://vue-blog-demo.itbook.com'),

('Python数据分析项目', '使用Python进行数据分析和可视化，包含数据清洗、统计分析、机器学习模型构建等内容。', 'https://via.placeholder.com/300x200/3776AB/FFFFFF?text=Data+Analysis', 'INTERMEDIATE', '["Python", "Pandas", "NumPy", "Matplotlib", "Scikit-learn", "Jupyter"]', '4周', 89, 4.5, 'ACTIVE', 'DATA_SCIENCE', 0, 1, 1456, 62, 43, 'https://github.com/itbook/python-data-analysis', 'https://colab.research.google.com/itbook/data-analysis'),

('Flutter跨平台应用开发', '学习Flutter框架开发跨平台移动应用，包含UI设计、状态管理、原生功能调用等核心技术。', 'https://via.placeholder.com/300x200/02569B/FFFFFF?text=Flutter+App', 'BEGINNER', '["Flutter", "Dart", "Firebase", "Provider", "HTTP"]', '5周', 112, 4.4, 'ACTIVE', 'MOBILE_DEVELOPMENT', 0, 0, 1234, 45, 32, 'https://github.com/itbook/flutter-app-demo', 'https://flutter-demo.itbook.com'),

('Docker容器化部署实战', '学习Docker容器技术，包含镜像构建、容器编排、CI/CD流水线等DevOps核心技能。', 'https://via.placeholder.com/300x200/2496ED/FFFFFF?text=Docker+DevOps', 'INTERMEDIATE', '["Docker", "Docker Compose", "Kubernetes", "Jenkins", "GitLab CI", "Nginx"]', '3周', 78, 4.3, 'ACTIVE', 'DEVOPS', 0, 0, 987, 38, 25, 'https://github.com/itbook/docker-deployment', 'https://docker-demo.itbook.com'),

('机器学习入门项目', '从零开始学习机器学习，包含监督学习、无监督学习、深度学习等核心算法和实际应用。', 'https://via.placeholder.com/300x200/FF6F00/FFFFFF?text=ML+Project', 'ADVANCED', '["Python", "TensorFlow", "Keras", "Scikit-learn", "OpenCV", "Jupyter"]', '10周', 167, 4.7, 'ACTIVE', 'MACHINE_LEARNING', 1, 0, 2567, 123, 87, 'https://github.com/itbook/ml-beginner-project', 'https://ml-demo.itbook.com'),

('区块链DApp开发', '学习区块链技术和智能合约开发，构建去中心化应用（DApp），了解Web3技术栈。', 'https://via.placeholder.com/300x200/F7931E/FFFFFF?text=Blockchain+DApp', 'ADVANCED', '["Solidity", "Web3.js", "React", "Ethereum", "Truffle", "MetaMask"]', '8周', 95, 4.2, 'ACTIVE', 'BLOCKCHAIN', 0, 1, 1345, 56, 41, 'https://github.com/itbook/blockchain-dapp', 'https://dapp-demo.itbook.com'),

('Unity 3D游戏开发', '使用Unity引擎开发3D游戏，学习游戏设计、物理引擎、动画系统等游戏开发核心技术。', 'https://via.placeholder.com/300x200/000000/FFFFFF?text=Unity+Game', 'INTERMEDIATE', '["Unity", "C#", "Blender", "Photoshop", "Visual Studio"]', '7周', 143, 4.5, 'ACTIVE', 'GAME_DEVELOPMENT', 0, 0, 1678, 71, 49, 'https://github.com/itbook/unity-3d-game', 'https://unity-demo.itbook.com'),

('物联网智能家居系统', '构建基于物联网的智能家居控制系统，包含传感器数据采集、设备控制、移动端应用等。', 'https://via.placeholder.com/300x200/00D4AA/FFFFFF?text=IoT+Smart+Home', 'ADVANCED', '["Arduino", "Raspberry Pi", "MQTT", "Node.js", "React Native", "InfluxDB"]', '9周', 67, 4.1, 'ACTIVE', 'IOT', 0, 0, 892, 34, 23, 'https://github.com/itbook/iot-smart-home', 'https://iot-demo.itbook.com');
