package com.itbook.repository;

import com.itbook.entity.SkillAssessmentRecord;
import com.itbook.entity.UserAtomicSkillMastery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 技能评估记录数据访问接口
 * 提供技能评估记录的数据库操作方法
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Repository
public interface SkillAssessmentRecordRepository extends JpaRepository<SkillAssessmentRecord, Long> {

    /**
     * 根据用户ID查找评估记录
     */
    List<SkillAssessmentRecord> findByUserId(Long userId);

    /**
     * 根据用户ID分页查询评估记录
     */
    Page<SkillAssessmentRecord> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据原子技能ID查找评估记录
     */
    List<SkillAssessmentRecord> findByAtomicSkillId(Long atomicSkillId);

    /**
     * 根据用户ID和原子技能ID查找评估记录
     */
    List<SkillAssessmentRecord> findByUserIdAndAtomicSkillId(Long userId, Long atomicSkillId);

    /**
     * 根据用户ID和原子技能ID按时间倒序查找评估记录
     */
    List<SkillAssessmentRecord> findByUserIdAndAtomicSkillIdOrderByCreatedAtDesc(Long userId, Long atomicSkillId);

    /**
     * 根据评估类型查找评估记录
     */
    List<SkillAssessmentRecord> findByAssessmentType(SkillAssessmentRecord.AssessmentType assessmentType);

    /**
     * 根据通过状态查找评估记录
     */
    List<SkillAssessmentRecord> findByPassStatus(SkillAssessmentRecord.PassStatus passStatus);

    /**
     * 根据评估者类型查找评估记录
     */
    List<SkillAssessmentRecord> findByAssessorType(SkillAssessmentRecord.AssessorType assessorType);

    /**
     * 根据用户ID和评估类型查找评估记录
     */
    List<SkillAssessmentRecord> findByUserIdAndAssessmentType(Long userId, SkillAssessmentRecord.AssessmentType assessmentType);

    /**
     * 根据用户ID和通过状态查找评估记录
     */
    List<SkillAssessmentRecord> findByUserIdAndPassStatus(Long userId, SkillAssessmentRecord.PassStatus passStatus);

    /**
     * 根据用户ID查找最新的评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findLatestByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据技能ID查找最新的评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.atomicSkillId = :skillId " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findLatestBySkillId(@Param("skillId") Long skillId, Pageable pageable);

    /**
     * 根据用户ID和技能ID查找最新的评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId AND sar.atomicSkillId = :skillId " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findLatestByUserIdAndSkillId(@Param("userId") Long userId, 
                                                            @Param("skillId") Long skillId, 
                                                            Pageable pageable);

    /**
     * 根据分数范围查找评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.score BETWEEN :minScore AND :maxScore " +
           "ORDER BY sar.score DESC")
    List<SkillAssessmentRecord> findByScoreRange(@Param("minScore") BigDecimal minScore,
                                                 @Param("maxScore") BigDecimal maxScore);

    /**
     * 根据用户ID获取评估统计
     */
    @Query("SELECT sar.passStatus, COUNT(sar) FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId GROUP BY sar.passStatus ORDER BY sar.passStatus")
    List<Object[]> getAssessmentStatisticsByUserId(@Param("userId") Long userId);

    /**
     * 根据技能ID获取评估统计
     */
    @Query("SELECT sar.passStatus, COUNT(sar) FROM SkillAssessmentRecord sar WHERE " +
           "sar.atomicSkillId = :skillId GROUP BY sar.passStatus ORDER BY sar.passStatus")
    List<Object[]> getAssessmentStatisticsBySkillId(@Param("skillId") Long skillId);

    /**
     * 获取用户的平均分数
     */
    @Query("SELECT AVG(sar.score) FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId")
    BigDecimal getUserAverageScore(@Param("userId") Long userId);

    /**
     * 获取技能的平均分数
     */
    @Query("SELECT AVG(sar.score) FROM SkillAssessmentRecord sar WHERE " +
           "sar.atomicSkillId = :skillId")
    BigDecimal getSkillAverageScore(@Param("skillId") Long skillId);

    /**
     * 获取用户的通过率
     */
    @Query("SELECT COUNT(CASE WHEN sar.passStatus = 'PASS' THEN 1 END) * 100.0 / COUNT(sar) " +
           "FROM SkillAssessmentRecord sar WHERE sar.userId = :userId")
    BigDecimal getUserPassRate(@Param("userId") Long userId);

    /**
     * 获取技能的通过率
     */
    @Query("SELECT COUNT(CASE WHEN sar.passStatus = 'PASS' THEN 1 END) * 100.0 / COUNT(sar) " +
           "FROM SkillAssessmentRecord sar WHERE sar.atomicSkillId = :skillId")
    BigDecimal getSkillPassRate(@Param("skillId") Long skillId);

    /**
     * 根据用户ID获取评估类型统计
     */
    @Query("SELECT sar.assessmentType, COUNT(sar) FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId GROUP BY sar.assessmentType ORDER BY COUNT(sar) DESC")
    List<Object[]> getAssessmentTypeStatisticsByUserId(@Param("userId") Long userId);

    /**
     * 根据技能ID获取评估类型统计
     */
    @Query("SELECT sar.assessmentType, COUNT(sar) FROM SkillAssessmentRecord sar WHERE " +
           "sar.atomicSkillId = :skillId GROUP BY sar.assessmentType ORDER BY COUNT(sar) DESC")
    List<Object[]> getAssessmentTypeStatisticsBySkillId(@Param("skillId") Long skillId);

    /**
     * 根据用户ID查找高分评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId AND sar.score >= :minScore " +
           "ORDER BY sar.score DESC, sar.createdAt DESC")
    List<SkillAssessmentRecord> findHighScoreAssessmentsByUserId(@Param("userId") Long userId,
                                                                @Param("minScore") BigDecimal minScore);

    /**
     * 根据用户ID查找需要重新评估的技能
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId AND sar.passStatus = 'FAIL' AND " +
           "sar.atomicSkillId NOT IN (" +
           "  SELECT sar2.atomicSkillId FROM SkillAssessmentRecord sar2 WHERE " +
           "  sar2.userId = :userId AND sar2.passStatus = 'PASS' AND sar2.createdAt > sar.createdAt" +
           ") " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findSkillsNeedingReassessment(@Param("userId") Long userId);

    /**
     * 根据时间范围查找评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.createdAt BETWEEN :startTime AND :endTime " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findByTimeRange(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID和时间范围查找评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId AND " +
           "sar.createdAt BETWEEN :startTime AND :endTime " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findByUserIdAndTimeRange(@Param("userId") Long userId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 根据评估用时范围查找评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.timeSpentMinutes BETWEEN :minMinutes AND :maxMinutes " +
           "ORDER BY sar.timeSpentMinutes ASC")
    List<SkillAssessmentRecord> findByTimeSpentRange(@Param("minMinutes") Integer minMinutes,
                                                    @Param("maxMinutes") Integer maxMinutes);

    /**
     * 根据尝试次数查找评估记录
     */
    List<SkillAssessmentRecord> findByAttemptNumber(Integer attemptNumber);

    /**
     * 根据用户ID和技能ID查找最高分记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId AND sar.atomicSkillId = :skillId " +
           "ORDER BY sar.score DESC")
    List<SkillAssessmentRecord> findBestScoreByUserIdAndSkillId(@Param("userId") Long userId,
                                                               @Param("skillId") Long skillId,
                                                               Pageable pageable);

    /**
     * 根据用户ID查找各技能的最新评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId AND sar.id IN (" +
           "  SELECT MAX(sar2.id) FROM SkillAssessmentRecord sar2 WHERE " +
           "  sar2.userId = :userId GROUP BY sar2.atomicSkillId" +
           ") " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findLatestAssessmentsByUserId(@Param("userId") Long userId);

    /**
     * 根据达到的掌握水平查找评估记录
     */
    List<SkillAssessmentRecord> findByMasteryLevelAchieved(UserAtomicSkillMastery.MasteryLevel masteryLevelAchieved);

    /**
     * 根据用户ID和达到的掌握水平查找评估记录
     */
    List<SkillAssessmentRecord> findByUserIdAndMasteryLevelAchieved(Long userId, UserAtomicSkillMastery.MasteryLevel masteryLevelAchieved);

    /**
     * 获取评估类型统计
     */
    @Query("SELECT sar.assessmentType, COUNT(sar) FROM SkillAssessmentRecord sar " +
           "GROUP BY sar.assessmentType ORDER BY COUNT(sar) DESC")
    List<Object[]> getAssessmentTypeStatistics();

    /**
     * 获取通过状态统计
     */
    @Query("SELECT sar.passStatus, COUNT(sar) FROM SkillAssessmentRecord sar " +
           "GROUP BY sar.passStatus ORDER BY sar.passStatus")
    List<Object[]> getPassStatusStatistics();

    /**
     * 获取评估者类型统计
     */
    @Query("SELECT sar.assessorType, COUNT(sar) FROM SkillAssessmentRecord sar " +
           "GROUP BY sar.assessorType ORDER BY COUNT(sar) DESC")
    List<Object[]> getAssessorTypeStatistics();

    /**
     * 获取掌握水平统计
     */
    @Query("SELECT sar.masteryLevelAchieved, COUNT(sar) FROM SkillAssessmentRecord sar WHERE " +
           "sar.masteryLevelAchieved IS NOT NULL " +
           "GROUP BY sar.masteryLevelAchieved ORDER BY sar.masteryLevelAchieved")
    List<Object[]> getMasteryLevelStatistics();

    /**
     * 根据技能ID集合查找评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.atomicSkillId IN :skillIds " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findByAtomicSkillIdIn(@Param("skillIds") Set<Long> skillIds);

    /**
     * 根据用户ID集合查找评估记录
     */
    @Query("SELECT sar FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId IN :userIds " +
           "ORDER BY sar.createdAt DESC")
    List<SkillAssessmentRecord> findByUserIdIn(@Param("userIds") Set<Long> userIds);

    /**
     * 删除用户的所有评估记录
     */
    void deleteByUserId(Long userId);

    /**
     * 删除技能的所有评估记录
     */
    void deleteByAtomicSkillId(Long atomicSkillId);

    /**
     * 根据评估者ID查找评估记录
     */
    List<SkillAssessmentRecord> findByAssessorId(Long assessorId);

    /**
     * 获取用户的评估总数
     */
    @Query("SELECT COUNT(sar) FROM SkillAssessmentRecord sar WHERE sar.userId = :userId")
    Long getUserAssessmentCount(@Param("userId") Long userId);

    /**
     * 获取技能的评估总数
     */
    @Query("SELECT COUNT(sar) FROM SkillAssessmentRecord sar WHERE sar.atomicSkillId = :skillId")
    Long getSkillAssessmentCount(@Param("skillId") Long skillId);

    /**
     * 获取用户的平均评估用时
     */
    @Query("SELECT AVG(sar.timeSpentMinutes) FROM SkillAssessmentRecord sar WHERE " +
           "sar.userId = :userId AND sar.timeSpentMinutes IS NOT NULL")
    Double getUserAverageTimeSpent(@Param("userId") Long userId);

    /**
     * 获取技能的平均评估用时
     */
    @Query("SELECT AVG(sar.timeSpentMinutes) FROM SkillAssessmentRecord sar WHERE " +
           "sar.atomicSkillId = :skillId AND sar.timeSpentMinutes IS NOT NULL")
    Double getSkillAverageTimeSpent(@Param("skillId") Long skillId);
}
