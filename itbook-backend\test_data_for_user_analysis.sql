-- 为用户画像分析创建测试数据
-- 目标：为用户ID=2创建有意义的学习行为数据

USE itbook_dev;

-- 1. 首先创建一些学习路径
INSERT INTO learning_path (id, name, description, difficulty_level, estimated_hours, status, tags, skill_tags, created_at, updated_at, creator_id, completion_count, review_count) VALUES
(1, 'Java Backend Development', 'Java backend development learning path with Spring framework and database skills', 'BEGINNER', 120, 'PUBLISHED', '["Java", "Backend", "Spring"]', '["Java", "Spring Boot", "MySQL", "RESTful API"]', NOW(), NOW(), 1, 15, 8),
(2, 'React Frontend Advanced', 'Advanced React frontend development with state management and optimization', 'INTERMEDIATE', 80, 'PUBLISHED', '["React", "Frontend", "JavaScript"]', '["React", "Redux", "TypeScript", "Webpack"]', NOW(), NOW(), 1, 12, 6),
(3, 'Data Structures and Algorithms', 'Computer science fundamentals - data structures and algorithms', 'INTERMEDIATE', 100, 'PUBLISHED', '["Algorithm", "Data Structure", "Programming"]', '["Algorithm Design", "Data Structure", "Time Complexity", "Space Complexity"]', NOW(), NOW(), 1, 8, 4),
(4, 'DevOps Engineering Practice', 'Essential DevOps skills learning path', 'ADVANCED', 150, 'PUBLISHED', '["DevOps", "Operations", "Automation"]', '["Docker", "Kubernetes", "CI/CD", "Monitoring"]', NOW(), NOW(), 1, 5, 3),
(5, 'Python Data Analysis', 'Python data analysis and machine learning introduction', 'BEGINNER', 90, 'PUBLISHED', '["Python", "Data Analysis", "Machine Learning"]', '["Python", "Pandas", "NumPy", "Matplotlib"]', NOW(), NOW(), 1, 10, 5);

-- 2. 为每个学习路径创建学习步骤
INSERT INTO learning_path_step (id, learning_path_id, name, description, step_type, step_order, estimated_minutes, difficulty_level, is_required, status, content_type, created_at) VALUES
-- Java Backend Development Steps
(1, 1, 'Java Basic Syntax', 'Java language basic syntax learning', 'COURSE', 1, 480, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(2, 1, 'Java OOP Programming', 'Java OOP concepts and practice', 'COURSE', 2, 600, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(3, 1, 'Java Collections Framework', 'Java collections usage and principles', 'COURSE', 3, 360, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(4, 1, 'Spring Boot Introduction', 'Spring Boot framework basics', 'COURSE', 4, 720, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(5, 1, 'MySQL Database', 'MySQL database operations and design', 'COURSE', 5, 480, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(6, 1, 'RESTful API Development', 'REST API design and development', 'PROJECT', 6, 600, 'INTERMEDIATE', 1, 'ACTIVE', 'PROJECT', NOW()),

-- React Frontend Advanced Steps
(7, 2, 'React Hooks Deep Dive', 'Advanced React Hooks usage', 'COURSE', 1, 360, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(8, 2, 'Redux State Management', 'Redux state management best practices', 'COURSE', 2, 480, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(9, 2, 'TypeScript Integration', 'React + TypeScript development', 'COURSE', 3, 300, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(10, 2, 'Performance Optimization', 'React application performance optimization', 'COURSE', 4, 240, 'ADVANCED', 1, 'ACTIVE', 'VIDEO', NOW()),

-- Data Structures and Algorithms Steps
(11, 3, 'Arrays and Linked Lists', 'Basic data structures learning', 'COURSE', 1, 360, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(12, 3, 'Stacks and Queues', 'Stack and queue data structures', 'COURSE', 2, 300, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(13, 3, 'Trees and Graphs', 'Tree and graph data structures', 'COURSE', 3, 480, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(14, 3, 'Sorting Algorithms', 'Common sorting algorithm implementations', 'COURSE', 4, 420, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),

-- DevOps Engineering Practice Steps
(15, 4, 'Docker Containerization', 'Docker basics and practice', 'COURSE', 1, 600, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW()),
(16, 4, 'Kubernetes Orchestration', 'K8s cluster management', 'COURSE', 2, 900, 'ADVANCED', 1, 'ACTIVE', 'VIDEO', NOW()),
(17, 4, 'CI/CD Pipeline', 'Continuous integration and deployment', 'PROJECT', 3, 720, 'ADVANCED', 1, 'ACTIVE', 'PROJECT', NOW()),

-- Python Data Analysis Steps
(18, 5, 'Python Basics', 'Python language fundamentals', 'COURSE', 1, 360, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(19, 5, 'Pandas Data Processing', 'Pandas library usage', 'COURSE', 2, 480, 'BEGINNER', 1, 'ACTIVE', 'VIDEO', NOW()),
(20, 5, 'Data Visualization', 'Matplotlib and Seaborn', 'COURSE', 3, 300, 'INTERMEDIATE', 1, 'ACTIVE', 'VIDEO', NOW());

-- 3. 创建用户职业目标
INSERT INTO user_career_goal (id, user_id, target_job_id, target_level, set_at, target_completion_date, is_active, priority, description, motivation, created_at, updated_at) VALUES
(1, 2, 1, 'mid', '2024-01-15 10:00:00', '2025-06-01 00:00:00', 1, 1, '成为一名中级Java后端开发工程师', '希望在后端开发领域有更深入的发展，掌握Spring生态系统', NOW(), NOW());

-- 4. 创建用户学习路径进度（模拟用户2的学习情况）
INSERT INTO user_learning_path_progress (id, user_id, learning_path_id, status, completion_percentage, completed_steps, total_steps, studied_minutes, started_at, last_studied_at, created_at, updated_at, career_goal_id, current_step_id) VALUES
-- Java后端开发 - 正在学习中，完成度85%
(1, 2, 1, 'IN_PROGRESS', 85.00, 5, 6, 2640, '2024-01-20 09:00:00', '2024-07-20 14:30:00', NOW(), NOW(), 1, 6),
-- React前端开发 - 完成度60%
(2, 2, 2, 'IN_PROGRESS', 60.00, 2, 4, 840, '2024-03-01 10:00:00', '2024-07-18 16:45:00', NOW(), NOW(), NULL, 3),
-- 数据结构与算法 - 完成度30%
(3, 2, 3, 'IN_PROGRESS', 30.00, 1, 4, 360, '2024-05-15 11:00:00', '2024-07-15 20:15:00', NOW(), NOW(), NULL, 2),
-- Python数据分析 - 刚开始，完成度15%
(4, 2, 5, 'IN_PROGRESS', 15.00, 0, 3, 180, '2024-06-01 13:00:00', '2024-07-10 19:20:00', NOW(), NOW(), NULL, 1);

-- 5. 创建用户学习步骤进度（详细的步骤完成情况）
INSERT INTO user_step_progress (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, last_studied_at, completed_at, created_at, updated_at, difficulty_rating, quality_rating, notes) VALUES
-- Java后端开发路径的步骤进度
(1, 2, 1, 'COMPLETED', 100.00, 480, '2024-01-20 09:00:00', '2024-02-05 16:30:00', '2024-02-05 16:30:00', NOW(), NOW(), 3, 5, 'Java基础语法掌握得不错，语法清晰易懂'),
(2, 2, 2, 'COMPLETED', 100.00, 600, '2024-02-06 10:00:00', '2024-02-25 15:45:00', '2024-02-25 15:45:00', NOW(), NOW(), 4, 4, 'OOP概念理解深入，实践项目很有帮助'),
(3, 2, 3, 'COMPLETED', 100.00, 360, '2024-02-26 11:00:00', '2024-03-15 14:20:00', '2024-03-15 14:20:00', NOW(), NOW(), 4, 5, '集合框架内容丰富，源码分析很棒'),
(4, 2, 4, 'COMPLETED', 100.00, 720, '2024-03-16 09:30:00', '2024-04-20 17:00:00', '2024-04-20 17:00:00', NOW(), NOW(), 5, 5, 'Spring Boot学习曲线陡峭但很实用'),
(5, 2, 5, 'COMPLETED', 100.00, 480, '2024-04-21 10:15:00', '2024-05-10 16:45:00', '2024-05-10 16:45:00', NOW(), NOW(), 3, 4, 'MySQL基础扎实，SQL优化部分需要更多练习'),
(6, 2, 6, 'IN_PROGRESS', 75.00, 450, '2024-05-11 11:00:00', '2024-07-20 14:30:00', NULL, NOW(), NOW(), 4, 4, '正在开发个人博客项目，API设计很有挑战性'),

-- React前端开发路径的步骤进度
(7, 2, 7, 'COMPLETED', 100.00, 360, '2024-03-01 10:00:00', '2024-03-20 15:30:00', '2024-03-20 15:30:00', NOW(), NOW(), 4, 5, 'Hooks概念很新颖，大大简化了组件逻辑'),
(8, 2, 8, 'COMPLETED', 100.00, 480, '2024-03-21 11:00:00', '2024-04-15 16:45:00', '2024-04-15 16:45:00', NOW(), NOW(), 5, 4, 'Redux学习曲线较陡，但状态管理很强大'),
(9, 2, 9, 'IN_PROGRESS', 60.00, 180, '2024-04-16 09:00:00', '2024-07-18 16:45:00', NULL, NOW(), NOW(), 4, 4, 'TypeScript类型系统需要时间适应'),

-- 数据结构与算法路径的步骤进度
(10, 2, 11, 'COMPLETED', 100.00, 360, '2024-05-15 11:00:00', '2024-06-05 14:20:00', '2024-06-05 14:20:00', NOW(), NOW(), 3, 4, '数组和链表基础扎实，练习题很有帮助'),
(11, 2, 12, 'IN_PROGRESS', 50.00, 150, '2024-06-06 10:00:00', '2024-07-15 20:15:00', NULL, NOW(), NOW(), 3, 4, '栈和队列概念清晰，正在练习相关算法题'),

-- Python数据分析路径的步骤进度
(12, 2, 18, 'IN_PROGRESS', 50.00, 180, '2024-06-01 13:00:00', '2024-07-10 19:20:00', NULL, NOW(), NOW(), 2, 4, 'Python语法简洁，比Java容易上手');

-- 6. 创建一些课程数据（与学习步骤关联）
INSERT INTO course (id, title, description, category, difficulty_level, estimated_hours, price, currency, instructor_name, status, created_at, updated_at, enrollment_count, rating, review_count) VALUES
(1, 'Java编程基础', 'Java语言基础语法和编程概念', 'Programming', 'BEGINNER', 8, 199.00, 'CNY', '张老师', 'PUBLISHED', NOW(), NOW(), 156, 4.5, 23),
(2, 'Spring Boot实战', 'Spring Boot框架实战开发', 'Framework', 'INTERMEDIATE', 12, 299.00, 'CNY', '李老师', 'PUBLISHED', NOW(), NOW(), 89, 4.7, 15),
(3, 'React高级开发', 'React框架高级特性和最佳实践', 'Frontend', 'INTERMEDIATE', 6, 249.00, 'CNY', '王老师', 'PUBLISHED', NOW(), NOW(), 67, 4.6, 12),
(4, '数据结构精讲', '数据结构和算法详解', 'Computer Science', 'INTERMEDIATE', 10, 279.00, 'CNY', '陈老师', 'PUBLISHED', NOW(), NOW(), 134, 4.8, 28),
(5, 'Python数据分析入门', 'Python数据分析基础教程', 'Data Science', 'BEGINNER', 7, 219.00, 'CNY', '刘老师', 'PUBLISHED', NOW(), NOW(), 98, 4.4, 18);

-- 7. 创建社区参与数据（推荐反馈表模拟社区互动）
INSERT INTO recommendation_feedback (id, user_id, path_id, action, rating, feedback, feedback_time, is_helpful, tags, created_at, updated_at) VALUES
(1, 2, 1, 'LIKE', 5, 'Java后端开发路径很实用，项目导向的学习方式很棒！', '2024-02-10 14:30:00', 1, '["实用", "项目导向"]', NOW(), NOW()),
(2, 2, 2, 'LIKE', 4, 'React路径内容丰富，但希望能增加更多实战项目', '2024-04-01 16:45:00', 1, '["内容丰富", "需要更多实战"]', NOW(), NOW()),
(3, 2, 3, 'ACCEPT', 4, '数据结构路径逻辑清晰，适合系统学习', '2024-05-20 11:20:00', 1, '["逻辑清晰", "系统性强"]', NOW(), NOW()),
(4, 2, 1, 'LIKE', 5, '完成Java路径后找到了心仪的工作，感谢平台！', '2024-07-01 09:15:00', 1, '["求职成功", "感谢"]', NOW(), NOW()),
(5, 2, 5, 'ACCEPT', 4, 'Python数据分析路径很适合转行，内容由浅入深', '2024-06-15 20:30:00', 1, '["适合转行", "由浅入深"]', NOW(), NOW());

-- 8. 更新学习路径步骤与课程的关联
UPDATE learning_path_step SET course_id = 1 WHERE id = 1;
UPDATE learning_path_step SET course_id = 1 WHERE id = 2;
UPDATE learning_path_step SET course_id = 2 WHERE id = 4;
UPDATE learning_path_step SET course_id = 3 WHERE id = 7;
UPDATE learning_path_step SET course_id = 3 WHERE id = 8;
UPDATE learning_path_step SET course_id = 4 WHERE id = 11;
UPDATE learning_path_step SET course_id = 4 WHERE id = 12;
UPDATE learning_path_step SET course_id = 5 WHERE id = 18;
UPDATE learning_path_step SET course_id = 5 WHERE id = 19;

-- 9. 添加更多的学习活动记录（通过更新last_studied_at来模拟学习频率）
-- 模拟用户2在过去6个月的学习活动模式

-- 更新用户学习路径进度，添加更多学习时间记录
UPDATE user_learning_path_progress SET
    studied_minutes = 3200,  -- 增加总学习时间
    last_studied_at = '2024-07-22 20:30:00'  -- 最近学习时间
WHERE user_id = 2 AND learning_path_id = 1;

UPDATE user_learning_path_progress SET
    studied_minutes = 1200,
    last_studied_at = '2024-07-21 18:45:00'
WHERE user_id = 2 AND learning_path_id = 2;

UPDATE user_learning_path_progress SET
    studied_minutes = 600,
    last_studied_at = '2024-07-20 16:20:00'
WHERE user_id = 2 AND learning_path_id = 3;

UPDATE user_learning_path_progress SET
    studied_minutes = 420,
    last_studied_at = '2024-07-19 19:15:00'
WHERE user_id = 2 AND learning_path_id = 5;

-- 10. 添加更多社区互动记录
INSERT INTO recommendation_feedback (id, user_id, path_id, action, rating, feedback, feedback_time, is_helpful, tags, created_at, updated_at) VALUES
(6, 2, 1, 'LIKE', 5, '项目实战部分特别有价值，直接应用到工作中', '2024-03-15 10:20:00', 1, '["项目实战", "工作应用"]', NOW(), NOW()),
(7, 2, 2, 'LIKE', 4, 'TypeScript集成部分讲解很详细', '2024-04-20 15:30:00', 1, '["TypeScript", "详细讲解"]', NOW(), NOW()),
(8, 2, 3, 'LIKE', 5, '算法题目循序渐进，很适合刷题练习', '2024-06-10 21:45:00', 1, '["循序渐进", "刷题练习"]', NOW(), NOW()),
(9, 2, 1, 'LIKE', 5, 'Spring Boot部分内容很新，跟上了技术潮流', '2024-04-25 14:15:00', 1, '["技术新颖", "跟上潮流"]', NOW(), NOW()),
(10, 2, 2, 'LIKE', 4, 'React Hooks部分改变了我的编程思维', '2024-03-25 16:50:00', 1, '["编程思维", "Hooks"]', NOW(), NOW()),
(11, 2, 5, 'LIKE', 4, 'Pandas库的使用技巧很实用', '2024-07-05 19:30:00', 1, '["Pandas", "实用技巧"]', NOW(), NOW()),
(12, 2, 3, 'LIKE', 5, '数据结构可视化演示很棒，帮助理解', '2024-06-01 20:10:00', 1, '["可视化", "帮助理解"]', NOW(), NOW());

-- 11. 创建一些额外的学习记录来丰富数据
-- 添加更多学习步骤进度记录，模拟更频繁的学习活动
INSERT INTO user_step_progress (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, last_studied_at, completed_at, created_at, updated_at, difficulty_rating, quality_rating, notes) VALUES
-- 为DevOps路径添加一些进度（用户开始探索新领域）
(13, 2, 15, 'IN_PROGRESS', 25.00, 150, '2024-07-01 10:00:00', '2024-07-15 14:30:00', NULL, NOW(), NOW(), 5, 4, 'Docker概念很新颖，容器化技术很有前景'),
-- 为已完成的步骤添加更多学习时间记录
(14, 2, 3, 'COMPLETED', 100.00, 420, '2024-02-26 11:00:00', '2024-03-20 14:20:00', '2024-03-20 14:20:00', NOW(), NOW(), 4, 5, '重新学习了集合框架，加深了理解');

-- 12. 更新用户基本信息，添加技能标签等
UPDATE user SET
    bio = '热爱编程的后端开发工程师，正在学习全栈开发技能。擅长Java、Spring Boot，目前在学习React和数据分析。',
    profession = '软件开发工程师',
    company = '某科技公司',
    updated_at = NOW()
WHERE id = 2;
