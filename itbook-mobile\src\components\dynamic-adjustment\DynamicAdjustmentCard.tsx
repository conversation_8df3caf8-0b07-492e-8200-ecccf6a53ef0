/**
 * 动态调整卡片组件
 * 
 * 展示学习路径调整建议和执行调整操作
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */

import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { DynamicAdjustmentService } from '../../services/DynamicAdjustmentService';
import {
  DynamicAdjustmentCardProps,
  ComprehensiveAnalysis,
  PathAdjustmentResult,
  AdjustmentTrigger
} from '../../types/DynamicAdjustment';

/**
 * 动态调整卡片组件
 */
export const DynamicAdjustmentCard: React.FC<DynamicAdjustmentCardProps> = ({
  userId,
  pathId,
  onAdjustmentComplete
}) => {
  const colors = useThemeColors();
  const [analysis, setAnalysis] = useState<ComprehensiveAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isAdjusting, setIsAdjusting] = useState(false);

  // 加载综合分析数据
  useEffect(() => {
    loadAnalysis();
  }, [userId, pathId]);

  const loadAnalysis = async () => {
    try {
      setIsLoading(true);
      const analysisData = await DynamicAdjustmentService.comprehensiveAnalysis(userId, pathId);
      setAnalysis(analysisData);
    } catch (error) {
      console.error('加载分析数据失败:', error);
      Alert.alert('错误', '加载分析数据失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAutoAdjust = async () => {
    try {
      setIsAdjusting(true);
      
      Alert.alert(
        '自动调整确认',
        '系统将基于分析结果自动调整您的学习路径，是否继续？',
        [
          { text: '取消', style: 'cancel' },
          {
            text: '确认',
            onPress: async () => {
              try {
                const results = await DynamicAdjustmentService.autoAdjustPath(userId, pathId);
                
                if (results && results.length > 0) {
                  Alert.alert(
                    '调整完成',
                    `成功执行了 ${results.length} 项调整操作`,
                    [{ text: '确定', onPress: () => {
                      onAdjustmentComplete?.(results[0]);
                      loadAnalysis(); // 重新加载分析数据
                    }}]
                  );
                } else {
                  Alert.alert('提示', '当前路径无需调整');
                }
              } catch (error) {
                console.error('自动调整失败:', error);
                Alert.alert('错误', '自动调整失败，请稍后重试');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('自动调整失败:', error);
      Alert.alert('错误', '自动调整失败，请稍后重试');
    } finally {
      setIsAdjusting(false);
    }
  };

  const handleManualAdjust = () => {
    if (!analysis) return;

    const strategies = analysis.adjustmentEvaluation.recommendedStrategies;
    if (strategies.length === 0) {
      Alert.alert('提示', '当前路径无需调整');
      return;
    }

    const strategyOptions = strategies.map(strategy => ({
      text: DynamicAdjustmentService.formatAdjustmentStrategy(strategy),
      onPress: () => executeManualAdjustment(strategy)
    }));

    Alert.alert(
      '选择调整策略',
      '请选择您希望执行的调整策略：',
      [
        ...strategyOptions,
        { text: '取消', style: 'cancel' }
      ]
    );
  };

  const executeManualAdjustment = async (strategy: string) => {
    try {
      setIsAdjusting(true);
      
      const adjustmentRequest = {
        userId,
        pathId,
        trigger: AdjustmentTrigger.USER_FEEDBACK,
        preferredStrategy: strategy as any,
        reason: '用户手动选择调整策略'
      };

      const result = await DynamicAdjustmentService.adjustPath(adjustmentRequest);
      
      Alert.alert(
        '调整完成',
        result.adjustmentSummary,
        [{ text: '确定', onPress: () => {
          onAdjustmentComplete?.(result);
          loadAnalysis(); // 重新加载分析数据
        }}]
      );
    } catch (error) {
      console.error('手动调整失败:', error);
      Alert.alert('错误', '调整失败，请稍后重试');
    } finally {
      setIsAdjusting(false);
    }
  };

  const getRiskLevelIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'HIGH': return 'warning';
      case 'MEDIUM': return 'alert-circle';
      case 'LOW': return 'checkmark-circle';
      default: return 'help-circle';
    }
  };

  const getEffectivenessIcon = (level: string) => {
    switch (level) {
      case 'EXCELLENT': return 'star';
      case 'GOOD': return 'thumbs-up';
      case 'AVERAGE': return 'remove-circle';
      case 'POOR': return 'thumbs-down';
      case 'VERY_POOR': return 'close-circle';
      default: return 'help-circle';
    }
  };

  if (isLoading) {
    return (
      <View style={{
        backgroundColor: colors.surface,
        borderRadius: tokens.radius('md'),
        padding: tokens.spacing('lg'),
        margin: tokens.spacing('md'),
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 200
      }}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={{
          color: colors.textSecondary,
          fontSize: tokens.fontSize('sm'),
          marginTop: tokens.spacing('md')
        }}>
          正在分析学习状况...
        </Text>
      </View>
    );
  }

  if (!analysis) {
    return (
      <View style={{
        backgroundColor: colors.surface,
        borderRadius: tokens.radius('md'),
        padding: tokens.spacing('lg'),
        margin: tokens.spacing('md'),
        alignItems: 'center'
      }}>
        <Ionicons name="alert-circle-outline" size={48} color={colors.textSecondary} />
        <Text style={{
          color: colors.textSecondary,
          fontSize: tokens.fontSize('md'),
          marginTop: tokens.spacing('md'),
          textAlign: 'center'
        }}>
          暂无分析数据
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: colors.primary,
            paddingHorizontal: tokens.spacing('lg'),
            paddingVertical: tokens.spacing('sm'),
            borderRadius: tokens.radius('sm'),
            marginTop: tokens.spacing('md')
          }}
          onPress={loadAnalysis}
          activeOpacity={0.7}
        >
          <Text style={{
            color: colors.onPrimary,
            fontSize: tokens.fontSize('sm'),
            fontWeight: tokens.fontWeight('medium')
          }}>
            重新加载
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  const { behaviorAnalysis, effectivenessEvaluation, adjustmentEvaluation, recommendations } = analysis;

  return (
    <View style={{
      backgroundColor: colors.surface,
      borderRadius: tokens.radius('md'),
      padding: tokens.spacing('lg'),
      margin: tokens.spacing('md'),
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3
    }}>
      {/* 标题 */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: tokens.spacing('lg')
      }}>
        <Ionicons name="analytics-outline" size={24} color={colors.primary} />
        <Text style={{
          color: colors.text,
          fontSize: tokens.fontSize('title-sm'),
          fontWeight: tokens.fontWeight('bold'),
          marginLeft: tokens.spacing('sm')
        }}>
          学习路径分析
        </Text>
      </View>

      {/* 关键指标 */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: tokens.spacing('lg')
      }}>
        {/* 学习风险 */}
        <View style={{ flex: 1, alignItems: 'center' }}>
          <Ionicons 
            name={getRiskLevelIcon(behaviorAnalysis.riskLevel)} 
            size={32} 
            color={DynamicAdjustmentService.getRiskLevelColor(behaviorAnalysis.riskLevel)} 
          />
          <Text style={{
            color: colors.textSecondary,
            fontSize: tokens.fontSize('xs'),
            marginTop: tokens.spacing('xs')
          }}>
            学习风险
          </Text>
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('sm'),
            fontWeight: tokens.fontWeight('medium')
          }}>
            {behaviorAnalysis.riskLevel === 'UNKNOWN' ? '未知' : 
             behaviorAnalysis.riskLevel === 'LOW' ? '低' :
             behaviorAnalysis.riskLevel === 'MEDIUM' ? '中' : '高'}
          </Text>
        </View>

        {/* 学习效果 */}
        <View style={{ flex: 1, alignItems: 'center' }}>
          <Ionicons 
            name={getEffectivenessIcon(effectivenessEvaluation.effectivenessLevel)} 
            size={32} 
            color={DynamicAdjustmentService.getEffectivenessLevelColor(effectivenessEvaluation.effectivenessLevel)} 
          />
          <Text style={{
            color: colors.textSecondary,
            fontSize: tokens.fontSize('xs'),
            marginTop: tokens.spacing('xs')
          }}>
            学习效果
          </Text>
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('sm'),
            fontWeight: tokens.fontWeight('medium')
          }}>
            {DynamicAdjustmentService.formatEffectivenessLevel(effectivenessEvaluation.effectivenessLevel)}
          </Text>
        </View>

        {/* 调整建议 */}
        <View style={{ flex: 1, alignItems: 'center' }}>
          <Ionicons 
            name={adjustmentEvaluation.adjustmentLevel === 'HIGH' ? 'warning' : 
                  adjustmentEvaluation.adjustmentLevel === 'MEDIUM' ? 'information-circle' : 'checkmark-circle'} 
            size={32} 
            color={adjustmentEvaluation.adjustmentLevel === 'HIGH' ? '#F44336' : 
                   adjustmentEvaluation.adjustmentLevel === 'MEDIUM' ? '#FFC107' : '#4CAF50'} 
          />
          <Text style={{
            color: colors.textSecondary,
            fontSize: tokens.fontSize('xs'),
            marginTop: tokens.spacing('xs')
          }}>
            调整需求
          </Text>
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('sm'),
            fontWeight: tokens.fontWeight('medium')
          }}>
            {adjustmentEvaluation.adjustmentLevel === 'HIGH' ? '高' :
             adjustmentEvaluation.adjustmentLevel === 'MEDIUM' ? '中' : '低'}
          </Text>
        </View>
      </View>

      {/* 主要建议 */}
      <View style={{
        backgroundColor: colors.primaryContainer,
        borderRadius: tokens.radius('sm'),
        padding: tokens.spacing('md'),
        marginBottom: tokens.spacing('lg')
      }}>
        <Text style={{
          color: colors.onPrimaryContainer,
          fontSize: tokens.fontSize('sm'),
          fontWeight: tokens.fontWeight('medium'),
          marginBottom: tokens.spacing('xs')
        }}>
          主要建议
        </Text>
        <Text style={{
          color: colors.onPrimaryContainer,
          fontSize: tokens.fontSize('sm'),
          lineHeight: 20
        }}>
          {recommendations.primaryAction}
        </Text>
      </View>

      {/* 操作按钮 */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between'
      }}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: colors.primary,
            paddingVertical: tokens.spacing('md'),
            borderRadius: tokens.radius('sm'),
            marginRight: tokens.spacing('sm'),
            alignItems: 'center',
            opacity: isAdjusting ? 0.6 : 1
          }}
          onPress={handleAutoAdjust}
          disabled={isAdjusting}
          activeOpacity={0.7}
        >
          {isAdjusting ? (
            <ActivityIndicator size="small" color={colors.onPrimary} />
          ) : (
            <>
              <Ionicons name="flash" size={20} color={colors.onPrimary} />
              <Text style={{
                color: colors.onPrimary,
                fontSize: tokens.fontSize('sm'),
                fontWeight: tokens.fontWeight('medium'),
                marginTop: tokens.spacing('xs')
              }}>
                自动调整
              </Text>
            </>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: colors.outline,
            paddingVertical: tokens.spacing('md'),
            borderRadius: tokens.radius('sm'),
            marginLeft: tokens.spacing('sm'),
            alignItems: 'center',
            opacity: isAdjusting ? 0.6 : 1
          }}
          onPress={handleManualAdjust}
          disabled={isAdjusting}
          activeOpacity={0.7}
        >
          <Ionicons name="settings" size={20} color={colors.onSurface} />
          <Text style={{
            color: colors.onSurface,
            fontSize: tokens.fontSize('sm'),
            fontWeight: tokens.fontWeight('medium'),
            marginTop: tokens.spacing('xs')
          }}>
            手动调整
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
