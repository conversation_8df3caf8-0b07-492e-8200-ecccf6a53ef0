-- Test data initialization script
-- For market analysis functionality testing

-- Insert test job data
INSERT INTO `job` (
    `id`, `title`, `company_id`, `description`, `requirements`, `benefits`, 
    `location`, `job_type`, `experience_level`, `salary_min`, `salary_max`, 
    `currency`, `status`, `is_remote`, `is_urgent`, `view_count`, `application_count`,
    `created_at`, `updated_at`, `published_at`
) VALUES 
(1, 'Senior Frontend Developer', 1,
 'Responsible for frontend development using React and TypeScript.',
 '["3+ years frontend experience", "React/Vue/Angular", "TypeScript/ES6+", "Webpack/Vite", "Mobile development preferred"]',
 '["Insurance", "Flexible hours", "Bonus", "Stock options", "Training"]',
 'Hangzhou', 'full-time', 'senior', 25000.00, 40000.00, 'CNY', 'ACTIVE', 0, 0, 1300, 89,
 NOW(), NOW(), NOW()),

(2, 'Java后端开发工程师', 2, 
 '负责后端服务开发，设计和实现高性能、高可用的分布式系统。',
 '["3年以上Java开发经验", "熟悉Spring Boot、Spring Cloud", "掌握MySQL、Redis等数据库", "了解微服务架构", "有高并发系统经验优先"]',
 '["五险一金", "带薪年假", "年终奖", "技术培训", "健身房"]',
 '北京', 'full-time', 'mid', 20000.00, 35000.00, 'CNY', 'ACTIVE', 0, 0, 980, 156,
 NOW(), NOW(), NOW()),

(3, '产品经理', 3, 
 '负责产品规划和设计，与技术团队协作推进产品迭代。',
 '["2年以上产品经验", "熟悉产品设计流程", "具备数据分析能力", "良好的沟通协调能力", "有B端产品经验优先"]',
 '["五险一金", "弹性工作", "年终奖", "期权激励", "团建活动"]',
 '深圳', 'full-time', 'mid', 18000.00, 30000.00, 'CNY', 'ACTIVE', 0, 0, 750, 203,
 NOW(), NOW(), NOW()),

(4, 'UI/UX设计师', 1, 
 '负责产品界面设计和用户体验优化，制作高质量的设计稿和原型。',
 '["2年以上UI/UX设计经验", "熟练使用Figma、Sketch等设计工具", "具备用户体验思维", "有移动端设计经验", "了解前端开发流程优先"]',
 '["五险一金", "设计津贴", "年终奖", "培训机会", "创意奖励"]',
 '上海', 'full-time', 'mid', 15000.00, 25000.00, 'CNY', 'ACTIVE', 0, 0, 620, 134,
 NOW(), NOW(), NOW()),

(5, '数据分析师', 2, 
 '负责业务数据分析，为产品和运营决策提供数据支持。',
 '["2年以上数据分析经验", "熟练使用SQL、Python/R", "掌握统计学基础", "熟悉数据可视化工具", "有业务理解能力"]',
 '["五险一金", "弹性工作", "年终奖", "学习津贴", "数据工具"]',
 '广州', 'full-time', 'mid', 16000.00, 28000.00, 'CNY', 'ACTIVE', 0, 0, 540, 87,
 NOW(), NOW(), NOW());

-- 插入测试公司数据（如果不存在）
INSERT IGNORE INTO `company` (
    `id`, `name`, `description`, `industry`, `size`, `location`, 
    `website`, `logo_url`, `created_at`, `updated_at`
) VALUES 
(1, '阿里巴巴', '全球领先的数字经济体，致力于让天下没有难做的生意', '电子商务', 'LARGE', '杭州',
 'https://www.alibaba.com', 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop',
 NOW(), NOW()),

(2, '腾讯', '中国领先的互联网增值服务提供商', '互联网', 'LARGE', '深圳',
 'https://www.tencent.com', 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop',
 NOW(), NOW()),

(3, '字节跳动', '全球化的移动互联网公司，激发创造，丰富生活', '移动互联网', 'LARGE', '北京',
 'https://www.bytedance.com', 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop',
 NOW(), NOW());
