# ITBook现有数据库结构分析报告

## 📊 **数据库结构概览**

### **技能相关表统计**
- **career_skill**: 9条记录 - 职业技能定义
- **learning_path**: 7条记录 - 学习路径
- **course**: 3条记录 - 课程信息
- **atomic_skill**: 0条记录 - 原子技能（新建表）
- **skill_relationship**: 0条记录 - 技能关系（新建表）

## 🔍 **核心表结构分析**

### **1. career_skill表（职业技能）**

**表结构**：
```sql
CREATE TABLE career_skill (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    career_goal_id BIGINT NOT NULL,
    skill_id VARCHAR(100) NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    skill_category VARCHAR(50),
    importance ENUM('critical','important','nice-to-have') DEFAULT 'important',
    target_level ENUM('beginner','intermediate','advanced','expert') DEFAULT 'intermediate',
    skill_type ENUM('core','bonus') DEFAULT 'core',
    description TEXT,
    learning_resources JSON,
    assessment_criteria JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**数据样本**：
- Java基础 (编程语言, critical, advanced, core)
- Spring框架 (开发框架, critical, advanced, core)
- MySQL数据库 (数据库, critical, intermediate, core)
- Web开发 (Web技术, important, intermediate, core)
- 微服务架构 (架构设计, important, intermediate, bonus)

**特点分析**：
- ✅ 技能分类明确（编程语言、开发框架、数据库等）
- ✅ 重要程度分级（critical、important、nice-to-have）
- ✅ 目标水平定义（beginner到expert）
- ✅ 技能类型区分（core、bonus）
- ✅ 支持JSON格式的学习资源和评估标准

### **2. learning_path表（学习路径）**

**表结构**：
```sql
CREATE TABLE learning_path (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    difficulty_level VARCHAR(255) NOT NULL,
    path_type VARCHAR(255) NOT NULL,
    estimated_hours INT,
    skill_tags JSON,
    learning_objectives JSON,
    tags JSON,
    status VARCHAR(255) NOT NULL,
    is_template BIT(1),
    creator_id BIGINT,
    target_job_id BIGINT,
    rating DECIMAL(3,2),
    learner_count INT,
    completion_count INT,
    review_count INT,
    created_at DATETIME(6) NOT NULL,
    updated_at DATETIME(6) NOT NULL
);
```

**数据样本**：
- Java基础入门11 (BEGINNER, STANDARD, 30小时)
- Spring Boot实战22 (INTERMEDIATE, STANDARD, 45小时)
- 微服务架构设计33 (ADVANCED, STANDARD, 60小时)
- Java后端开发工程师标准学习路径1 (BEGINNER, PERSONALIZED, 200小时)

**特点分析**：
- ✅ 路径类型区分（STANDARD、PERSONALIZED）
- ✅ 难度级别分层（BEGINNER、INTERMEDIATE、ADVANCED）
- ✅ 技能标签JSON格式存储，包含权重和目标水平
- ✅ 支持模板路径和个性化路径
- ✅ 完整的统计信息（学习人数、完成人数、评分等）

### **3. course表（课程）**

**表结构**：
```sql
CREATE TABLE course (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    difficulty ENUM('beginner','intermediate','advanced') DEFAULT 'beginner',
    duration INT DEFAULT 0,
    instructor VARCHAR(100),
    price DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'CNY',
    rating DECIMAL(3,2) DEFAULT 0.00,
    review_count INT DEFAULT 0,
    enrollment_count INT DEFAULT 0,
    tags JSON,
    prerequisites JSON,
    learning_outcomes JSON,
    status ENUM('DRAFT','PUBLISHED','ARCHIVED') DEFAULT 'DRAFT',
    is_featured TINYINT(1) DEFAULT 0,
    is_popular TINYINT(1) DEFAULT 0,
    certificate_available TINYINT(1) DEFAULT 0,
    thumbnail_url VARCHAR(255),
    preview_video_url VARCHAR(255),
    created_at DATETIME(6) NOT NULL,
    updated_at DATETIME(6)
);
```

**数据样本**：
- Java基础入门-课程1 (编程语言, beginner, 1200分钟)
- Spring Boot实战-课程2 (框架技术, intermediate, 1800分钟)
- React前端开发-课程3 (前端开发, intermediate, 1500分钟)

**特点分析**：
- ✅ 完整的课程元数据（标题、描述、分类、难度）
- ✅ 商业化支持（价格、货币、证书）
- ✅ 社交化功能（评分、评论、注册人数）
- ✅ JSON格式存储标签、前置条件、学习成果
- ✅ 多媒体支持（缩略图、预览视频）

## 🔄 **数据关系分析**

### **现有关系模型**
```
career_goal (职业目标)
    ↓ (1:N)
career_skill (职业技能)
    ↓ (关联)
learning_path (学习路径)
    ↓ (包含)
course (课程)
```

### **技能标签关系**
- **learning_path.skill_tags**: JSON格式存储技能标签
- **course.tags**: JSON格式存储课程标签
- **career_skill.skill_name**: 技能名称字符串

## 📈 **数据质量评估**

### **优势**
1. **结构化程度高**: 表结构设计合理，字段定义清晰
2. **扩展性好**: 大量使用JSON字段，支持灵活的数据存储
3. **业务完整性**: 涵盖了技能、路径、课程的完整生命周期
4. **元数据丰富**: 包含评分、统计、状态等完整信息

### **挑战**
1. **技能粒度粗糙**: career_skill是粗粒度的技能定义
2. **关系模糊**: 技能间缺乏明确的依赖和关联关系
3. **标准化不足**: 技能名称和分类缺乏统一标准
4. **原子化程度低**: 无法支持细粒度的技能管理

## 🎯 **迁移策略建议**

### **1. 技能映射策略**
- **career_skill → atomic_skill**: 将粗粒度技能拆分为原子技能
- **skill_tags → atomic_skill**: 从JSON标签中提取原子技能
- **course.tags → atomic_skill**: 从课程标签中识别技能

### **2. 关系建立策略**
- **前置关系**: 基于learning_path中的技能顺序建立前置关系
- **包含关系**: 基于course和skill的关联建立包含关系
- **相似关系**: 基于技能名称和描述的相似度建立相关关系

### **3. 数据清洗策略**
- **去重**: 识别和合并重复的技能定义
- **标准化**: 统一技能名称和分类体系
- **补全**: 为缺失的技能信息补充默认值

### **4. 验证策略**
- **完整性验证**: 确保所有原始数据都有对应的原子技能
- **一致性验证**: 确保映射关系的逻辑一致性
- **业务验证**: 确保迁移后的数据符合业务逻辑

## 📋 **迁移清单**

### **阶段一：数据分析和准备**
- [x] 分析现有表结构
- [x] 评估数据质量
- [x] 设计映射策略
- [ ] 创建迁移脚本

### **阶段二：数据转换**
- [ ] 实现自动映射算法
- [ ] 执行技能拆分和标准化
- [ ] 建立技能关系图谱
- [ ] 创建用户掌握度数据

### **阶段三：验证和优化**
- [ ] 数据完整性验证
- [ ] 业务逻辑测试
- [ ] 性能优化
- [ ] 文档更新

## 🔧 **技术实现要点**

### **映射算法核心**
1. **文本相似度计算**: 使用余弦相似度、编辑距离等算法
2. **关键词提取**: 基于TF-IDF、词频分析等技术
3. **语义分析**: 利用词向量、语义网络等方法
4. **规则引擎**: 基于专家知识的映射规则

### **数据迁移流程**
1. **备份原始数据**: 确保数据安全
2. **分批处理**: 避免大事务影响性能
3. **事务控制**: 保证数据一致性
4. **错误处理**: 完善的异常处理和回滚机制

这份分析为后续的数据迁移工作提供了详细的基础信息和策略指导。
