package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import com.itbook.service.LearningProgressTracker.LearningBehaviorAnalysis;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 路径调整算法引擎
 * 
 * 核心功能：
 * 1. 基于学习进度和用户反馈实时调整学习路径
 * 2. 智能难度调节和内容优化
 * 3. 路径质量评估和重新生成决策
 * 4. 个性化学习建议和路径优化
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PathAdjustmentEngine {

    private final DynamicLearningPathRepository dynamicPathRepository;
    private final DynamicPathStepRepository dynamicStepRepository;
    private final UserStepProgressRepository stepProgressRepository;
    private final LearningProgressTracker learningProgressTracker;
    private final DynamicPathGenerationService pathGenerationService;

    /**
     * 调整策略枚举
     */
    public enum AdjustmentStrategy {
        DIFFICULTY_ADJUSTMENT,    // 难度调整
        CONTENT_REPLACEMENT,      // 内容替换
        STEP_REORDERING,         // 步骤重排序
        PATH_EXTENSION,          // 路径扩展
        PATH_SIMPLIFICATION,     // 路径简化
        COMPLETE_REGENERATION    // 完全重新生成
    }

    /**
     * 调整触发条件
     */
    public enum AdjustmentTrigger {
        POOR_PERFORMANCE,        // 学习表现不佳
        HIGH_DIFFICULTY_RATING,  // 难度评分过高
        LOW_QUALITY_RATING,      // 质量评分过低
        SLOW_PROGRESS,           // 进度缓慢
        USER_FEEDBACK,           // 用户反馈
        SCHEDULED_OPTIMIZATION   // 定期优化
    }

    /**
     * 路径调整请求
     */
    public static class PathAdjustmentRequest {
        private Long userId;
        private Long pathId;
        private AdjustmentTrigger trigger;
        private AdjustmentStrategy preferredStrategy;
        private Map<String, Object> adjustmentData;
        private String reason;

        // Constructors
        public PathAdjustmentRequest() {}

        public PathAdjustmentRequest(Long userId, Long pathId, AdjustmentTrigger trigger) {
            this.userId = userId;
            this.pathId = pathId;
            this.trigger = trigger;
            this.adjustmentData = new HashMap<>();
        }

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public AdjustmentTrigger getTrigger() { return trigger; }
        public void setTrigger(AdjustmentTrigger trigger) { this.trigger = trigger; }
        
        public AdjustmentStrategy getPreferredStrategy() { return preferredStrategy; }
        public void setPreferredStrategy(AdjustmentStrategy preferredStrategy) { this.preferredStrategy = preferredStrategy; }
        
        public Map<String, Object> getAdjustmentData() { return adjustmentData; }
        public void setAdjustmentData(Map<String, Object> adjustmentData) { this.adjustmentData = adjustmentData; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    /**
     * 路径调整结果
     */
    public static class PathAdjustmentResult {
        private Long pathId;
        private AdjustmentStrategy appliedStrategy;
        private List<String> adjustmentActions;
        private Map<String, Object> adjustmentDetails;
        private BigDecimal qualityImprovement;
        private String adjustmentSummary;
        private LocalDateTime adjustmentTime;
        private boolean requiresUserConfirmation;

        // Getters and Setters
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public AdjustmentStrategy getAppliedStrategy() { return appliedStrategy; }
        public void setAppliedStrategy(AdjustmentStrategy appliedStrategy) { this.appliedStrategy = appliedStrategy; }
        
        public List<String> getAdjustmentActions() { return adjustmentActions; }
        public void setAdjustmentActions(List<String> adjustmentActions) { this.adjustmentActions = adjustmentActions; }
        
        public Map<String, Object> getAdjustmentDetails() { return adjustmentDetails; }
        public void setAdjustmentDetails(Map<String, Object> adjustmentDetails) { this.adjustmentDetails = adjustmentDetails; }
        
        public BigDecimal getQualityImprovement() { return qualityImprovement; }
        public void setQualityImprovement(BigDecimal qualityImprovement) { this.qualityImprovement = qualityImprovement; }
        
        public String getAdjustmentSummary() { return adjustmentSummary; }
        public void setAdjustmentSummary(String adjustmentSummary) { this.adjustmentSummary = adjustmentSummary; }
        
        public LocalDateTime getAdjustmentTime() { return adjustmentTime; }
        public void setAdjustmentTime(LocalDateTime adjustmentTime) { this.adjustmentTime = adjustmentTime; }
        
        public boolean isRequiresUserConfirmation() { return requiresUserConfirmation; }
        public void setRequiresUserConfirmation(boolean requiresUserConfirmation) { this.requiresUserConfirmation = requiresUserConfirmation; }
    }

    /**
     * 执行路径调整
     * 
     * @param request 调整请求
     * @return 调整结果
     */
    @Transactional
    public PathAdjustmentResult adjustPath(PathAdjustmentRequest request) {
        log.info("🔧 开始执行路径调整: userId={}, pathId={}, trigger={}", 
                request.getUserId(), request.getPathId(), request.getTrigger());

        try {
            // 1. 验证请求参数
            validateAdjustmentRequest(request);

            // 2. 获取当前路径和用户学习数据
            DynamicLearningPath currentPath = getCurrentPath(request.getPathId());
            LearningBehaviorAnalysis behaviorAnalysis = learningProgressTracker
                    .analyzeLearningBehavior(request.getUserId(), request.getPathId());

            // 3. 分析调整需求
            AdjustmentStrategy strategy = determineAdjustmentStrategy(request, behaviorAnalysis);

            // 4. 执行调整策略
            PathAdjustmentResult result = executeAdjustmentStrategy(strategy, request, currentPath, behaviorAnalysis);

            // 5. 记录调整历史
            recordAdjustmentHistory(request, result);

            log.info("✅ 路径调整完成: pathId={}, strategy={}, qualityImprovement={}", 
                    request.getPathId(), strategy, result.getQualityImprovement());

            return result;

        } catch (Exception e) {
            log.error("❌ 路径调整失败: userId={}, pathId={}", 
                    request.getUserId(), request.getPathId(), e);
            throw new RuntimeException("路径调整失败: " + e.getMessage(), e);
        }
    }

    /**
     * 自动检测并触发路径调整
     * 
     * @param userId 用户ID
     * @param pathId 路径ID
     * @return 调整结果列表
     */
    public List<PathAdjustmentResult> autoAdjustPath(Long userId, Long pathId) {
        log.info("🤖 自动检测路径调整需求: userId={}, pathId={}", userId, pathId);

        try {
            List<PathAdjustmentResult> results = new ArrayList<>();

            // 分析学习行为
            LearningBehaviorAnalysis analysis = learningProgressTracker.analyzeLearningBehavior(userId, pathId);

            // 检测各种调整触发条件
            List<PathAdjustmentRequest> adjustmentRequests = detectAdjustmentTriggers(userId, pathId, analysis);

            // 执行调整
            for (PathAdjustmentRequest request : adjustmentRequests) {
                PathAdjustmentResult result = adjustPath(request);
                results.add(result);
            }

            log.info("✅ 自动路径调整完成: userId={}, pathId={}, adjustmentCount={}", 
                    userId, pathId, results.size());

            return results;

        } catch (Exception e) {
            log.error("❌ 自动路径调整失败: userId={}, pathId={}", userId, pathId, e);
            throw new RuntimeException("自动路径调整失败", e);
        }
    }

    /**
     * 评估路径调整的必要性
     * 
     * @param userId 用户ID
     * @param pathId 路径ID
     * @return 调整建议
     */
    public Map<String, Object> evaluateAdjustmentNeed(Long userId, Long pathId) {
        log.info("📊 评估路径调整必要性: userId={}, pathId={}", userId, pathId);

        try {
            Map<String, Object> evaluation = new HashMap<>();

            // 获取学习行为分析
            LearningBehaviorAnalysis analysis = learningProgressTracker.analyzeLearningBehavior(userId, pathId);

            // 计算调整必要性分数
            double adjustmentScore = calculateAdjustmentScore(analysis);
            evaluation.put("adjustmentScore", adjustmentScore);
            evaluation.put("adjustmentLevel", getAdjustmentLevel(adjustmentScore));

            // 识别具体问题
            List<String> identifiedIssues = identifyPathIssues(analysis);
            evaluation.put("identifiedIssues", identifiedIssues);

            // 推荐调整策略
            List<AdjustmentStrategy> recommendedStrategies = recommendAdjustmentStrategies(analysis);
            evaluation.put("recommendedStrategies", recommendedStrategies);

            // 预估调整效果
            Map<String, Object> expectedImpact = estimateAdjustmentImpact(analysis, recommendedStrategies);
            evaluation.put("expectedImpact", expectedImpact);

            evaluation.put("evaluationTime", LocalDateTime.now());
            evaluation.put("userId", userId);
            evaluation.put("pathId", pathId);

            log.info("✅ 路径调整必要性评估完成: userId={}, pathId={}, adjustmentScore={}", 
                    userId, pathId, adjustmentScore);

            return evaluation;

        } catch (Exception e) {
            log.error("❌ 路径调整必要性评估失败: userId={}, pathId={}", userId, pathId, e);
            throw new RuntimeException("路径调整必要性评估失败", e);
        }
    }

    /**
     * 验证调整请求
     */
    private void validateAdjustmentRequest(PathAdjustmentRequest request) {
        if (request.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (request.getPathId() == null) {
            throw new IllegalArgumentException("路径ID不能为空");
        }
        if (request.getTrigger() == null) {
            throw new IllegalArgumentException("调整触发条件不能为空");
        }
    }

    /**
     * 获取当前路径
     */
    private DynamicLearningPath getCurrentPath(Long pathId) {
        return dynamicPathRepository.findById(pathId)
                .orElseThrow(() -> new IllegalArgumentException("路径不存在: " + pathId));
    }

    /**
     * 确定调整策略
     */
    private AdjustmentStrategy determineAdjustmentStrategy(PathAdjustmentRequest request,
                                                          LearningBehaviorAnalysis analysis) {
        // 如果用户指定了偏好策略，优先使用
        if (request.getPreferredStrategy() != null) {
            return request.getPreferredStrategy();
        }

        // 根据触发条件和分析结果确定策略
        switch (request.getTrigger()) {
            case HIGH_DIFFICULTY_RATING:
                return analysis.getOverallDifficulty() > 4.0 ?
                        AdjustmentStrategy.DIFFICULTY_ADJUSTMENT : AdjustmentStrategy.CONTENT_REPLACEMENT;

            case POOR_PERFORMANCE:
                return analysis.getCompletionProbability() < 0.3 ?
                        AdjustmentStrategy.COMPLETE_REGENERATION : AdjustmentStrategy.PATH_SIMPLIFICATION;

            case SLOW_PROGRESS:
                return AdjustmentStrategy.STEP_REORDERING;

            case LOW_QUALITY_RATING:
                return AdjustmentStrategy.CONTENT_REPLACEMENT;

            case USER_FEEDBACK:
                return AdjustmentStrategy.CONTENT_REPLACEMENT;

            case SCHEDULED_OPTIMIZATION:
            default:
                return AdjustmentStrategy.DIFFICULTY_ADJUSTMENT;
        }
    }

    /**
     * 执行调整策略
     */
    private PathAdjustmentResult executeAdjustmentStrategy(AdjustmentStrategy strategy,
                                                          PathAdjustmentRequest request,
                                                          DynamicLearningPath currentPath,
                                                          LearningBehaviorAnalysis analysis) {
        PathAdjustmentResult result = new PathAdjustmentResult();
        result.setPathId(request.getPathId());
        result.setAppliedStrategy(strategy);
        result.setAdjustmentTime(LocalDateTime.now());
        result.setAdjustmentActions(new ArrayList<>());
        result.setAdjustmentDetails(new HashMap<>());

        switch (strategy) {
            case DIFFICULTY_ADJUSTMENT:
                result = executeDifficultyAdjustment(request, currentPath, analysis, result);
                break;
            case CONTENT_REPLACEMENT:
                result = executeContentReplacement(request, currentPath, analysis, result);
                break;
            case STEP_REORDERING:
                result = executeStepReordering(request, currentPath, analysis, result);
                break;
            case PATH_SIMPLIFICATION:
                result = executePathSimplification(request, currentPath, analysis, result);
                break;
            case PATH_EXTENSION:
                result = executePathExtension(request, currentPath, analysis, result);
                break;
            case COMPLETE_REGENERATION:
                result = executeCompleteRegeneration(request, currentPath, analysis, result);
                break;
        }

        return result;
    }

    /**
     * 执行难度调整
     */
    private PathAdjustmentResult executeDifficultyAdjustment(PathAdjustmentRequest request,
                                                            DynamicLearningPath currentPath,
                                                            LearningBehaviorAnalysis analysis,
                                                            PathAdjustmentResult result) {
        log.debug("🎯 执行难度调整: pathId={}", request.getPathId());

        List<String> actions = new ArrayList<>();
        Map<String, Object> details = new HashMap<>();

        // 获取路径步骤
        List<DynamicPathStep> steps = dynamicStepRepository.findByPathIdOrderByStepOrder(request.getPathId());

        // 根据用户表现调整难度
        double targetDifficulty = calculateTargetDifficulty(analysis);
        int adjustedSteps = 0;

        for (DynamicPathStep step : steps) {
            BigDecimal currentAdjustment = step.getDifficultyAdjustment();
            BigDecimal newAdjustment = calculateNewDifficultyAdjustment(currentAdjustment, targetDifficulty);

            if (!currentAdjustment.equals(newAdjustment)) {
                step.setDifficultyAdjustment(newAdjustment);
                step.setPersonalizationReason("基于学习表现调整难度");
                dynamicStepRepository.save(step);
                adjustedSteps++;
            }
        }

        actions.add("调整了 " + adjustedSteps + " 个步骤的难度级别");
        details.put("adjustedStepsCount", adjustedSteps);
        details.put("targetDifficulty", targetDifficulty);

        result.setAdjustmentActions(actions);
        result.setAdjustmentDetails(details);
        result.setQualityImprovement(BigDecimal.valueOf(0.15)); // 预估15%的质量提升
        result.setAdjustmentSummary("根据学习表现调整了路径难度，优化学习体验");
        result.setRequiresUserConfirmation(false);

        return result;
    }

    /**
     * 执行内容替换
     */
    private PathAdjustmentResult executeContentReplacement(PathAdjustmentRequest request,
                                                          DynamicLearningPath currentPath,
                                                          LearningBehaviorAnalysis analysis,
                                                          PathAdjustmentResult result) {
        log.debug("🔄 执行内容替换: pathId={}", request.getPathId());

        List<String> actions = new ArrayList<>();
        Map<String, Object> details = new HashMap<>();

        // 识别需要替换的问题步骤
        List<Long> problematicSteps = analysis.getProblematicSteps();
        int replacedSteps = 0;

        for (Long stepId : problematicSteps) {
            // 这里可以实现具体的内容替换逻辑
            // 暂时记录替换动作
            actions.add("替换问题步骤: " + stepId);
            replacedSteps++;
        }

        details.put("replacedStepsCount", replacedSteps);
        details.put("problematicSteps", problematicSteps);

        result.setAdjustmentActions(actions);
        result.setAdjustmentDetails(details);
        result.setQualityImprovement(BigDecimal.valueOf(0.20)); // 预估20%的质量提升
        result.setAdjustmentSummary("替换了 " + replacedSteps + " 个质量较低的学习步骤");
        result.setRequiresUserConfirmation(true); // 内容替换需要用户确认

        return result;
    }

    /**
     * 执行步骤重排序
     */
    private PathAdjustmentResult executeStepReordering(PathAdjustmentRequest request,
                                                      DynamicLearningPath currentPath,
                                                      LearningBehaviorAnalysis analysis,
                                                      PathAdjustmentResult result) {
        log.debug("🔀 执行步骤重排序: pathId={}", request.getPathId());

        List<String> actions = new ArrayList<>();
        Map<String, Object> details = new HashMap<>();

        // 获取当前步骤
        List<DynamicPathStep> steps = dynamicStepRepository.findByPathIdOrderByStepOrder(request.getPathId());

        // 基于学习效率重新排序（简化版本）
        // 将困难步骤后移，简单步骤前移
        List<DynamicPathStep> reorderedSteps = optimizeStepOrder(steps, analysis);

        // 更新步骤顺序
        for (int i = 0; i < reorderedSteps.size(); i++) {
            DynamicPathStep step = reorderedSteps.get(i);
            step.setStepOrder(i + 1);
            step.setPersonalizationReason("基于学习效率优化步骤顺序");
            dynamicStepRepository.save(step);
        }

        actions.add("重新排序了 " + steps.size() + " 个学习步骤");
        details.put("totalSteps", steps.size());
        details.put("reorderingStrategy", "基于学习效率优化");

        result.setAdjustmentActions(actions);
        result.setAdjustmentDetails(details);
        result.setQualityImprovement(BigDecimal.valueOf(0.10)); // 预估10%的质量提升
        result.setAdjustmentSummary("优化了学习步骤的顺序，提高学习效率");
        result.setRequiresUserConfirmation(false);

        return result;
    }
}
