-- =====================================================
-- ITBook项目 - 数据库迁移日志表
-- 
-- 用于记录数据库迁移的执行状态和历史
-- 作者：ITBook Team
-- 日期：2025-07-13
-- =====================================================

-- 创建迁移日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `migration_log` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `migration_name` VARCHAR(255) NOT NULL COMMENT '迁移脚本名称',
  `description` TEXT NULL COMMENT '迁移描述',
  `start_time` DATETIME(6) NOT NULL COMMENT '开始时间',
  `end_time` DATETIME(6) NULL COMMENT '结束时间',
  `status` ENUM('STARTED', 'COMPLETED', 'FAILED', 'ROLLBACK') NOT NULL DEFAULT 'STARTED' COMMENT '迁移状态',
  `error_message` TEXT NULL COMMENT '错误信息',
  `executed_by` VARCHAR(100) NULL COMMENT '执行者',
  `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_migration_name` (`migration_name`) USING BTREE,
  INDEX `idx_status` (`status`) USING BTREE,
  INDEX `idx_start_time` (`start_time`) USING BTREE
) ENGINE = InnoDB 
CHARACTER SET = utf8mb4 
COLLATE = utf8mb4_unicode_ci 
COMMENT = '数据库迁移日志表' 
ROW_FORMAT = Dynamic;

-- 插入初始记录（如果表是新创建的）
INSERT IGNORE INTO `migration_log` 
(`migration_name`, `description`, `start_time`, `end_time`, `status`, `executed_by`) 
VALUES 
('migration-log-table', '创建迁移日志表', NOW(), NOW(), 'COMPLETED', 'system');
