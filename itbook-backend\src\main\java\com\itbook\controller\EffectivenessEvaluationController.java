package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.EffectivenessEvaluator;
import com.itbook.service.EffectivenessEvaluator.EffectivenessEvaluation;
import com.itbook.service.EffectivenessEvaluator.EvaluationDimension;
import com.itbook.service.EffectivenessEvaluator.EffectivenessLevel;
import com.itbook.service.EffectivenessEvaluator.RegenerationRecommendation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学习效果评估控制器
 * 
 * 提供学习效果评估的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/effectiveness-evaluation")
@RequiredArgsConstructor
@Tag(name = "学习效果评估", description = "学习效果评估相关接口")
public class EffectivenessEvaluationController {

    private final EffectivenessEvaluator effectivenessEvaluator;

    /**
     * 评估学习效果
     */
    @GetMapping("/users/{userId}/paths/{pathId}/evaluate")
    @Operation(summary = "评估学习效果", description = "评估用户在特定学习路径中的学习效果")
    public ResponseEntity<ApiResponse<EffectivenessEvaluation>> evaluateEffectiveness(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 评估学习效果: userId={}, pathId={}", userId, pathId);
        
        try {
            // 执行学习效果评估
            EffectivenessEvaluation evaluation = effectivenessEvaluator.evaluateEffectiveness(userId, pathId);

            log.info("✅ 学习效果评估完成: userId={}, pathId={}, effectiveness={}, level={}", 
                    userId, pathId, evaluation.getOverallEffectiveness(), evaluation.getEffectivenessLevel());

            return ResponseEntity.ok(ApiResponse.success("学习效果评估完成", evaluation));

        } catch (Exception e) {
            log.error("❌ 学习效果评估失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习效果评估失败: " + e.getMessage()));
        }
    }

    /**
     * 批量评估学习效果
     */
    @PostMapping("/users/{userId}/batch-evaluate")
    @Operation(summary = "批量评估学习效果", description = "批量评估用户多个学习路径的效果")
    public ResponseEntity<ApiResponse<List<EffectivenessEvaluation>>> batchEvaluateEffectiveness(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID列表", required = true) @Valid @RequestBody BatchEvaluationRequest request) {
        
        log.info("📊 批量评估学习效果: userId={}, pathCount={}", userId, request.getPathIds().size());
        
        try {
            // 执行批量评估
            List<EffectivenessEvaluation> evaluations = effectivenessEvaluator
                    .batchEvaluateEffectiveness(userId, request.getPathIds());

            log.info("✅ 批量学习效果评估完成: userId={}, successCount={}/{}", 
                    userId, evaluations.size(), request.getPathIds().size());

            return ResponseEntity.ok(ApiResponse.success("批量学习效果评估完成", evaluations));

        } catch (Exception e) {
            log.error("❌ 批量学习效果评估失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("批量学习效果评估失败: " + e.getMessage()));
        }
    }

    /**
     * 获取评估摘要
     */
    @GetMapping("/users/{userId}/summary")
    @Operation(summary = "获取评估摘要", description = "获取用户的学习效果评估摘要统计")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEvaluationSummary(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        log.info("📈 获取评估摘要: userId={}", userId);
        
        try {
            // 获取评估摘要
            Map<String, Object> summary = effectivenessEvaluator.getEvaluationSummary(userId);

            log.info("✅ 评估摘要获取完成: userId={}, avgEffectiveness={}", 
                    userId, summary.get("averageEffectiveness"));

            return ResponseEntity.ok(ApiResponse.success("评估摘要获取成功", summary));

        } catch (Exception e) {
            log.error("❌ 评估摘要获取失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("评估摘要获取失败: " + e.getMessage()));
        }
    }

    /**
     * 获取评估维度列表
     */
    @GetMapping("/dimensions")
    @Operation(summary = "获取评估维度", description = "获取所有评估维度的说明")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEvaluationDimensions() {
        
        log.info("📋 获取评估维度列表");
        
        try {
            Map<String, Object> dimensions = new HashMap<>();
            
            for (EvaluationDimension dimension : EvaluationDimension.values()) {
                Map<String, String> dimensionInfo = new HashMap<>();
                switch (dimension) {
                    case LEARNING_OUTCOME:
                        dimensionInfo.put("name", "学习成果");
                        dimensionInfo.put("description", "评估用户的学习成果和技能掌握情况");
                        dimensionInfo.put("weight", "35%");
                        break;
                    case PATH_QUALITY:
                        dimensionInfo.put("name", "路径质量");
                        dimensionInfo.put("description", "评估学习路径的内容质量和结构合理性");
                        dimensionInfo.put("weight", "25%");
                        break;
                    case LEARNING_EFFICIENCY:
                        dimensionInfo.put("name", "学习效率");
                        dimensionInfo.put("description", "评估学习时间利用率和进度效率");
                        dimensionInfo.put("weight", "25%");
                        break;
                    case USER_SATISFACTION:
                        dimensionInfo.put("name", "用户满意度");
                        dimensionInfo.put("description", "评估用户对学习体验的满意程度");
                        dimensionInfo.put("weight", "15%");
                        break;
                    case SKILL_MASTERY:
                        dimensionInfo.put("name", "技能掌握");
                        dimensionInfo.put("description", "评估具体技能的掌握程度");
                        dimensionInfo.put("weight", "辅助指标");
                        break;
                    case TIME_EFFECTIVENESS:
                        dimensionInfo.put("name", "时间有效性");
                        dimensionInfo.put("description", "评估学习时间投入的有效性");
                        dimensionInfo.put("weight", "辅助指标");
                        break;
                }
                dimensions.put(dimension.name(), dimensionInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("dimensions", dimensions);
            responseData.put("totalCount", dimensions.size());

            return ResponseEntity.ok(ApiResponse.success("获取评估维度成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取评估维度失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取评估维度失败: " + e.getMessage()));
        }
    }

    /**
     * 获取效果等级列表
     */
    @GetMapping("/levels")
    @Operation(summary = "获取效果等级", description = "获取所有学习效果等级的说明")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getEffectivenessLevels() {
        
        log.info("📋 获取效果等级列表");
        
        try {
            Map<String, Object> levels = new HashMap<>();
            
            for (EffectivenessLevel level : EffectivenessLevel.values()) {
                Map<String, String> levelInfo = new HashMap<>();
                switch (level) {
                    case EXCELLENT:
                        levelInfo.put("name", "优秀");
                        levelInfo.put("description", "学习效果非常好，各项指标表现优异");
                        levelInfo.put("range", "90-100%");
                        levelInfo.put("color", "#4CAF50");
                        break;
                    case GOOD:
                        levelInfo.put("name", "良好");
                        levelInfo.put("description", "学习效果良好，大部分指标表现不错");
                        levelInfo.put("range", "75-89%");
                        levelInfo.put("color", "#8BC34A");
                        break;
                    case AVERAGE:
                        levelInfo.put("name", "一般");
                        levelInfo.put("description", "学习效果一般，有改进空间");
                        levelInfo.put("range", "60-74%");
                        levelInfo.put("color", "#FFC107");
                        break;
                    case POOR:
                        levelInfo.put("name", "较差");
                        levelInfo.put("description", "学习效果较差，需要调整学习方法");
                        levelInfo.put("range", "40-59%");
                        levelInfo.put("color", "#FF9800");
                        break;
                    case VERY_POOR:
                        levelInfo.put("name", "很差");
                        levelInfo.put("description", "学习效果很差，建议重新规划学习路径");
                        levelInfo.put("range", "<40%");
                        levelInfo.put("color", "#F44336");
                        break;
                }
                levels.put(level.name(), levelInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("levels", levels);
            responseData.put("totalCount", levels.size());

            return ResponseEntity.ok(ApiResponse.success("获取效果等级成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取效果等级失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取效果等级失败: " + e.getMessage()));
        }
    }

    /**
     * 获取重新生成建议类型
     */
    @GetMapping("/regeneration-recommendations")
    @Operation(summary = "获取重新生成建议", description = "获取所有路径重新生成建议类型")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRegenerationRecommendations() {
        
        log.info("📋 获取重新生成建议类型列表");
        
        try {
            Map<String, Object> recommendations = new HashMap<>();
            
            for (RegenerationRecommendation recommendation : RegenerationRecommendation.values()) {
                Map<String, String> recommendationInfo = new HashMap<>();
                switch (recommendation) {
                    case NOT_NEEDED:
                        recommendationInfo.put("name", "不需要");
                        recommendationInfo.put("description", "学习效果良好，无需重新生成");
                        recommendationInfo.put("action", "继续当前路径");
                        break;
                    case MINOR_ADJUSTMENT:
                        recommendationInfo.put("name", "小幅调整");
                        recommendationInfo.put("description", "学习效果一般，建议进行小幅调整");
                        recommendationInfo.put("action", "调整部分内容");
                        break;
                    case MAJOR_ADJUSTMENT:
                        recommendationInfo.put("name", "大幅调整");
                        recommendationInfo.put("description", "学习效果较差，建议进行大幅调整");
                        recommendationInfo.put("action", "重新设计路径");
                        break;
                    case COMPLETE_REGENERATION:
                        recommendationInfo.put("name", "完全重新生成");
                        recommendationInfo.put("description", "学习效果很差，建议完全重新生成");
                        recommendationInfo.put("action", "重新生成路径");
                        break;
                }
                recommendations.put(recommendation.name(), recommendationInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("recommendations", recommendations);
            responseData.put("totalCount", recommendations.size());

            return ResponseEntity.ok(ApiResponse.success("获取重新生成建议成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取重新生成建议失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取重新生成建议失败: " + e.getMessage()));
        }
    }

    /**
     * 批量评估请求参数
     */
    public static class BatchEvaluationRequest {
        private List<Long> pathIds;

        // Getters and Setters
        public List<Long> getPathIds() { return pathIds; }
        public void setPathIds(List<Long> pathIds) { this.pathIds = pathIds; }
    }
}
