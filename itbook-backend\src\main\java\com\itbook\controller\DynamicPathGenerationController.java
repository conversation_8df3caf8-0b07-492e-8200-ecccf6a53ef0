package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.DynamicPathGenerationService;
import com.itbook.service.DynamicPathGenerationService.PathGenerationParams;
import com.itbook.service.DynamicPathGenerationService.PathGenerationResult;
import com.itbook.service.DynamicPathGenerationService.GenerationStrategy;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态路径生成控制器
 * 
 * 提供动态学习路径生成的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/dynamic-path-generation")
@RequiredArgsConstructor
@Tag(name = "动态路径生成", description = "动态学习路径生成相关接口")
public class DynamicPathGenerationController {

    private final DynamicPathGenerationService dynamicPathGenerationService;

    /**
     * 生成动态学习路径
     */
    @PostMapping("/generate")
    @Operation(summary = "生成动态学习路径", description = "基于用户画像和职业目标生成个性化学习路径")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateDynamicPath(
            @Parameter(description = "路径生成参数", required = true)
            @Valid @RequestBody GeneratePathRequest request) {
        
        log.info("🚀 开始生成动态学习路径: userId={}, strategy={}", 
                request.getUserId(), request.getStrategy());
        
        try {
            // 构建生成参数
            PathGenerationParams params = new PathGenerationParams();
            params.setUserId(request.getUserId());
            params.setCareerGoalId(request.getCareerGoalId());
            params.setStrategy(request.getStrategy() != null ? request.getStrategy() : GenerationStrategy.HYBRID);
            params.setTimeConstraintWeeks(request.getTimeConstraintWeeks());
            params.setDifficultyPreference(request.getDifficultyPreference() != null ? request.getDifficultyPreference() : "GRADUAL");
            params.setLearningStyle(request.getLearningStyle() != null ? request.getLearningStyle() : "MIXED");
            params.setCustomParams(request.getCustomParams() != null ? request.getCustomParams() : new HashMap<>());

            // 生成路径
            PathGenerationResult result = dynamicPathGenerationService.generateDynamicPath(params);

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("generatedPath", result.getGeneratedPath());
            responseData.put("pathSteps", result.getPathSteps());
            responseData.put("qualityScore", result.getQualityScore());
            responseData.put("generationReport", result.getGenerationReport());
            responseData.put("metadata", result.getMetadata());
            responseData.put("stepCount", result.getPathSteps().size());

            log.info("✅ 动态学习路径生成成功: pathId={}, qualityScore={}", 
                    result.getGeneratedPath().getId(), result.getQualityScore());

            return ResponseEntity.ok(ApiResponse.success("动态学习路径生成成功", responseData));

        } catch (Exception e) {
            log.error("❌ 动态学习路径生成失败: userId={}", request.getUserId(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("动态学习路径生成失败: " + e.getMessage()));
        }
    }

    /**
     * 获取路径生成策略列表
     */
    @GetMapping("/strategies")
    @Operation(summary = "获取路径生成策略", description = "获取所有可用的路径生成策略")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getGenerationStrategies() {
        
        log.info("📋 获取路径生成策略列表");
        
        try {
            Map<String, Object> strategies = new HashMap<>();
            
            for (GenerationStrategy strategy : GenerationStrategy.values()) {
                Map<String, String> strategyInfo = new HashMap<>();
                switch (strategy) {
                    case SKILL_BASED:
                        strategyInfo.put("name", "基于技能");
                        strategyInfo.put("description", "根据用户当前技能水平和目标技能生成路径");
                        break;
                    case CAREER_ORIENTED:
                        strategyInfo.put("name", "面向职业");
                        strategyInfo.put("description", "根据职业目标和技能要求生成路径");
                        break;
                    case ADAPTIVE:
                        strategyInfo.put("name", "自适应");
                        strategyInfo.put("description", "根据学习习惯和进度动态调整的智能路径");
                        break;
                    case HYBRID:
                        strategyInfo.put("name", "混合策略");
                        strategyInfo.put("description", "融合多种策略优势的综合路径");
                        break;
                }
                strategies.put(strategy.name(), strategyInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("strategies", strategies);
            responseData.put("defaultStrategy", GenerationStrategy.HYBRID.name());
            responseData.put("totalCount", strategies.size());

            return ResponseEntity.ok(ApiResponse.success("获取路径生成策略成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取路径生成策略失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取路径生成策略失败: " + e.getMessage()));
        }
    }

    /**
     * 路径生成请求参数
     */
    public static class GeneratePathRequest {
        private Long userId;
        private Long careerGoalId;
        private GenerationStrategy strategy;
        private Integer timeConstraintWeeks;
        private String difficultyPreference;
        private String learningStyle;
        private Map<String, Object> customParams;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getCareerGoalId() { return careerGoalId; }
        public void setCareerGoalId(Long careerGoalId) { this.careerGoalId = careerGoalId; }
        
        public GenerationStrategy getStrategy() { return strategy; }
        public void setStrategy(GenerationStrategy strategy) { this.strategy = strategy; }
        
        public Integer getTimeConstraintWeeks() { return timeConstraintWeeks; }
        public void setTimeConstraintWeeks(Integer timeConstraintWeeks) { this.timeConstraintWeeks = timeConstraintWeeks; }
        
        public String getDifficultyPreference() { return difficultyPreference; }
        public void setDifficultyPreference(String difficultyPreference) { this.difficultyPreference = difficultyPreference; }
        
        public String getLearningStyle() { return learningStyle; }
        public void setLearningStyle(String learningStyle) { this.learningStyle = learningStyle; }
        
        public Map<String, Object> getCustomParams() { return customParams; }
        public void setCustomParams(Map<String, Object> customParams) { this.customParams = customParams; }
    }
}
