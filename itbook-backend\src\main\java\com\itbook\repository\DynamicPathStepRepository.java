package com.itbook.repository;

import com.itbook.entity.DynamicPathStep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 动态路径步骤数据访问接口
 * 提供动态学习路径步骤的数据库操作方法
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Repository
public interface DynamicPathStepRepository extends JpaRepository<DynamicPathStep, Long> {

    /**
     * 根据路径ID查找所有步骤
     */
    List<DynamicPathStep> findByPathId(Long pathId);

    /**
     * 根据路径ID按步骤顺序查找步骤
     */
    List<DynamicPathStep> findByPathIdOrderByStepOrder(Long pathId);

    /**
     * 根据路径ID和状态查找步骤
     */
    List<DynamicPathStep> findByPathIdAndStatus(Long pathId, DynamicPathStep.Status status);

    /**
     * 根据原子技能ID查找步骤
     */
    List<DynamicPathStep> findByAtomicSkillId(Long atomicSkillId);

    /**
     * 根据步骤类型查找步骤
     */
    List<DynamicPathStep> findByStepType(DynamicPathStep.StepType stepType);

    /**
     * 根据路径ID和步骤类型查找步骤
     */
    List<DynamicPathStep> findByPathIdAndStepType(Long pathId, DynamicPathStep.StepType stepType);

    /**
     * 根据路径ID查找当前步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId AND dps.status = 'IN_PROGRESS' " +
           "ORDER BY dps.stepOrder ASC")
    List<DynamicPathStep> findCurrentStepsByPathId(@Param("pathId") Long pathId);

    /**
     * 根据路径ID查找下一个步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId AND dps.status = 'NOT_STARTED' " +
           "ORDER BY dps.stepOrder ASC")
    List<DynamicPathStep> findNextStepsByPathId(@Param("pathId") Long pathId);

    /**
     * 根据路径ID查找已完成的步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId AND dps.status = 'COMPLETED' " +
           "ORDER BY dps.stepOrder ASC")
    List<DynamicPathStep> findCompletedStepsByPathId(@Param("pathId") Long pathId);

    /**
     * 根据路径ID获取步骤统计
     */
    @Query("SELECT dps.status, COUNT(dps) FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId GROUP BY dps.status ORDER BY dps.status")
    List<Object[]> getStepStatisticsByPathId(@Param("pathId") Long pathId);

    /**
     * 根据路径ID计算完成率
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN dps.status = 'COMPLETED' THEN 1 END) * 100.0 / COUNT(dps) " +
           "FROM DynamicPathStep dps WHERE dps.pathId = :pathId")
    BigDecimal calculateCompletionRateByPathId(@Param("pathId") Long pathId);

    /**
     * 根据路径ID获取平均进度
     */
    @Query("SELECT AVG(dps.progressPercentage) FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId")
    BigDecimal getAverageProgressByPathId(@Param("pathId") Long pathId);

    /**
     * 根据路径ID获取总预计时长
     */
    @Query("SELECT SUM(dps.estimatedHours) FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId")
    Integer getTotalEstimatedHoursByPathId(@Param("pathId") Long pathId);

    /**
     * 根据路径ID获取实际用时
     */
    @Query("SELECT SUM(dps.actualHours) FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId AND dps.actualHours IS NOT NULL")
    BigDecimal getTotalActualHoursByPathId(@Param("pathId") Long pathId);

    /**
     * 根据优先级权重范围查找步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.priorityWeight BETWEEN :minWeight AND :maxWeight " +
           "ORDER BY dps.priorityWeight DESC")
    List<DynamicPathStep> findByPriorityWeightRange(@Param("minWeight") BigDecimal minWeight,
                                                   @Param("maxWeight") BigDecimal maxWeight);

    /**
     * 根据难度调整范围查找步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.difficultyAdjustment BETWEEN :minAdjustment AND :maxAdjustment " +
           "ORDER BY dps.difficultyAdjustment DESC")
    List<DynamicPathStep> findByDifficultyAdjustmentRange(@Param("minAdjustment") BigDecimal minAdjustment,
                                                         @Param("maxAdjustment") BigDecimal maxAdjustment);

    /**
     * 根据进度范围查找步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.progressPercentage BETWEEN :minProgress AND :maxProgress " +
           "ORDER BY dps.progressPercentage DESC")
    List<DynamicPathStep> findByProgressRange(@Param("minProgress") BigDecimal minProgress,
                                             @Param("maxProgress") BigDecimal maxProgress);

    /**
     * 查找超时的步骤（开始时间超过预计时长）
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.status = 'IN_PROGRESS' AND " +
           "dps.startedAt IS NOT NULL AND " +
           "dps.startedAt < :cutoffTime " +
           "ORDER BY dps.startedAt ASC")
    List<DynamicPathStep> findOverdueSteps(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找长时间未开始的步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.status = 'NOT_STARTED' AND " +
           "dps.createdAt < :cutoffTime " +
           "ORDER BY dps.createdAt ASC")
    List<DynamicPathStep> findStaleSteps(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 根据路径ID查找高优先级步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId AND " +
           "dps.priorityWeight >= :minPriority " +
           "ORDER BY dps.priorityWeight DESC, dps.stepOrder ASC")
    List<DynamicPathStep> findHighPriorityStepsByPathId(@Param("pathId") Long pathId,
                                                       @Param("minPriority") BigDecimal minPriority);

    /**
     * 根据路径ID查找需要调整的步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId AND " +
           "(dps.difficultyAdjustment < 0.5 OR dps.difficultyAdjustment > 2.0) " +
           "ORDER BY dps.stepOrder ASC")
    List<DynamicPathStep> findStepsNeedingAdjustmentByPathId(@Param("pathId") Long pathId);

    /**
     * 获取步骤类型统计
     */
    @Query("SELECT dps.stepType, COUNT(dps) FROM DynamicPathStep dps " +
           "GROUP BY dps.stepType ORDER BY COUNT(dps) DESC")
    List<Object[]> getStepTypeStatistics();

    /**
     * 获取步骤状态统计
     */
    @Query("SELECT dps.status, COUNT(dps) FROM DynamicPathStep dps " +
           "GROUP BY dps.status ORDER BY dps.status")
    List<Object[]> getStepStatusStatistics();

    /**
     * 根据原子技能ID获取步骤统计
     */
    @Query("SELECT dps.status, COUNT(dps) FROM DynamicPathStep dps WHERE " +
           "dps.atomicSkillId = :skillId GROUP BY dps.status ORDER BY dps.status")
    List<Object[]> getStepStatisticsBySkillId(@Param("skillId") Long skillId);

    /**
     * 批量更新步骤状态
     */
    @Query("UPDATE DynamicPathStep dps SET " +
           "dps.status = :status, " +
           "dps.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dps.id IN :stepIds")
    int updateStatusBatch(@Param("stepIds") Set<Long> stepIds,
                         @Param("status") DynamicPathStep.Status status);

    /**
     * 批量更新步骤进度
     */
    @Query("UPDATE DynamicPathStep dps SET " +
           "dps.progressPercentage = :progress, " +
           "dps.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dps.id IN :stepIds")
    int updateProgressBatch(@Param("stepIds") Set<Long> stepIds,
                           @Param("progress") BigDecimal progress);

    /**
     * 更新步骤开始时间
     */
    @Query("UPDATE DynamicPathStep dps SET " +
           "dps.startedAt = :startTime, " +
           "dps.status = 'IN_PROGRESS', " +
           "dps.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dps.id = :stepId")
    int updateStartTime(@Param("stepId") Long stepId,
                       @Param("startTime") LocalDateTime startTime);

    /**
     * 更新步骤完成时间
     */
    @Query("UPDATE DynamicPathStep dps SET " +
           "dps.completedAt = :completionTime, " +
           "dps.status = 'COMPLETED', " +
           "dps.progressPercentage = 100.00, " +
           "dps.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dps.id = :stepId")
    int updateCompletionTime(@Param("stepId") Long stepId,
                            @Param("completionTime") LocalDateTime completionTime);

    /**
     * 更新步骤实际用时
     */
    @Query("UPDATE DynamicPathStep dps SET " +
           "dps.actualHours = :actualHours, " +
           "dps.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dps.id = :stepId")
    int updateActualHours(@Param("stepId") Long stepId,
                         @Param("actualHours") BigDecimal actualHours);

    /**
     * 更新步骤完成分数
     */
    @Query("UPDATE DynamicPathStep dps SET " +
           "dps.completionScore = :score, " +
           "dps.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE dps.id = :stepId")
    int updateCompletionScore(@Param("stepId") Long stepId,
                             @Param("score") BigDecimal score);

    /**
     * 删除路径的所有步骤
     */
    void deleteByPathId(Long pathId);

    /**
     * 删除技能相关的所有步骤
     */
    void deleteByAtomicSkillId(Long atomicSkillId);

    /**
     * 根据路径ID查找最后一个步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId " +
           "ORDER BY dps.stepOrder DESC")
    List<DynamicPathStep> findLastStepByPathId(@Param("pathId") Long pathId);

    /**
     * 根据路径ID和步骤顺序查找步骤
     */
    DynamicPathStep findByPathIdAndStepOrder(Long pathId, Integer stepOrder);

    /**
     * 根据路径ID查找指定顺序范围的步骤
     */
    @Query("SELECT dps FROM DynamicPathStep dps WHERE " +
           "dps.pathId = :pathId AND " +
           "dps.stepOrder BETWEEN :startOrder AND :endOrder " +
           "ORDER BY dps.stepOrder ASC")
    List<DynamicPathStep> findStepsByOrderRange(@Param("pathId") Long pathId,
                                               @Param("startOrder") Integer startOrder,
                                               @Param("endOrder") Integer endOrder);
}
