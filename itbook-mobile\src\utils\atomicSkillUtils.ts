import { AtomicSkill, AtomicSkillApiResponse } from '../types/atomicSkill';

export class AtomicSkillUtils {
  static fromApiResponse(apiResponse: AtomicSkillApiResponse): AtomicSkill {
    return {
      id: apiResponse.id,
      name: apiResponse.name,
      description: apiResponse.description,
      category: apiResponse.category,
      difficultyLevel: apiResponse.difficulty_level,
      estimatedHours: apiResponse.estimated_hours,
      prerequisites: apiResponse.prerequisites || [],
      learningObjectives: apiResponse.learning_objectives || [],
      resources: apiResponse.resources || [],
      tags: apiResponse.tags || [],
      status: apiResponse.status,
      rating: apiResponse.rating,
      completionRate: apiResponse.completion_rate,
      createdAt: apiResponse.created_at,
      updatedAt: apiResponse.updated_at,
      isPublished: apiResponse.is_published || false,
      authorId: apiResponse.author_id,
      version: apiResponse.version || '1.0.0'
    };
  }

  static toApiRequest(skill: Partial<AtomicSkill>): any {
    return {
      id: skill.id,
      name: skill.name,
      description: skill.description,
      category: skill.category,
      difficulty_level: skill.difficultyLevel,
      estimated_hours: skill.estimatedHours,
      prerequisites: skill.prerequisites || [],
      learning_objectives: skill.learningObjectives || [],
      resources: skill.resources || [],
      tags: skill.tags || [],
      status: skill.status,
      rating: skill.rating,
      completion_rate: skill.completionRate,
      is_published: skill.isPublished || false,
      author_id: skill.authorId,
      version: skill.version || '1.0.0'
    };
  }
}
