/**
 * 语言能力编辑表单组件（简化版）
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';

interface LanguageFormData {
  name: string;
  level: string;
  description: string;
}

interface LanguageEditFormProps {
  resumeId: number;
  language?: any;
  onSave: (language: any) => void;
  onCancel: () => void;
  onDelete?: () => void;
}

export const LanguageEditForm: React.FC<LanguageEditFormProps> = ({
  resumeId,
  language,
  onSave,
  onCancel,
  onDelete,
}) => {
  const colors = useThemeColors();
  const isEditing = !!language;

  // 表单数据状态
  const [formData, setFormData] = useState<LanguageFormData>({
    name: language?.name || '',
    level: language?.level || 'CONVERSATIONAL',
    description: language?.description || '',
  });

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 语言水平选项
  const languageLevelOptions = [
    { label: '基础', value: 'BASIC', description: '能够进行简单的日常交流' },
    { label: '会话', value: 'CONVERSATIONAL', description: '能够进行基本的工作交流' },
    { label: '流利', value: 'FLUENT', description: '能够流利进行专业交流' },
    { label: '母语', value: 'NATIVE', description: '母语水平或接近母语' },
  ];

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入语言名称';
    }

    if (!formData.level) {
      newErrors.level = '请选择语言水平';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理字段变更
  const handleFieldChange = (field: keyof LanguageFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  // 处理保存
  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('提示', '请检查表单中的错误信息');
      return;
    }

    try {
      setLoading(true);

      const saveData = {
        id: language?.id || 0,
        name: formData.name.trim(),
        level: formData.level,
        description: formData.description.trim() || undefined,
        sortOrder: language?.sortOrder,
      };

      onSave(saveData);
    } catch (error) {
      console.error('保存语言能力失败:', error);
      Alert.alert('错误', '保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理删除
  const handleDelete = () => {
    Alert.alert(
      '确认删除',
      '确定要删除这个语言能力吗？此操作无法撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => onDelete?.(),
        },
      ]
    );
  };

  // 渲染输入框
  const renderInput = (
    label: string,
    value: string,
    onChangeText: (text: string) => void,
    placeholder: string,
    error?: string,
    multiline = false,
    required = false
  ) => (
    <View style={{ marginBottom: tokens.spacing('lg') }}>
      <Text style={{
        fontSize: tokens.fontSize('body'),
        fontWeight: tokens.fontWeight('medium') as any,
        color: colors.text,
        marginBottom: tokens.spacing('sm'),
      }}>
        {label}{required && <Text style={{ color: colors.error }}>*</Text>}
      </Text>
      <TextInput
        style={{
          borderWidth: 1,
          borderColor: error ? colors.error : colors.border,
          borderRadius: tokens.radius('sm'),
          padding: tokens.spacing('md'),
          fontSize: tokens.fontSize('body'),
          color: colors.text,
          backgroundColor: colors.surface,
          minHeight: multiline ? 80 : 44,
          textAlignVertical: multiline ? 'top' : 'center',
        }}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={colors.textSecondary}
        multiline={multiline}
        numberOfLines={multiline ? 4 : 1}
      />
      {error && (
        <Text style={{
          fontSize: tokens.fontSize('caption'),
          color: colors.error,
          marginTop: tokens.spacing('xs'),
        }}>
          {error}
        </Text>
      )}
    </View>
  );

  // 渲染语言水平选择器
  const renderLevelPicker = () => (
    <View style={{ marginBottom: tokens.spacing('lg') }}>
      <Text style={{
        fontSize: tokens.fontSize('body'),
        fontWeight: tokens.fontWeight('medium') as any,
        color: colors.text,
        marginBottom: tokens.spacing('sm'),
      }}>
        语言水平<Text style={{ color: colors.error }}>*</Text>
      </Text>
      
      <View style={{
        borderWidth: 1,
        borderColor: errors.level ? colors.error : colors.border,
        borderRadius: tokens.radius('sm'),
        backgroundColor: colors.surface,
        overflow: 'hidden',
      }}>
        {languageLevelOptions.map((option, index) => (
          <TouchableOpacity
            key={option.value}
            onPress={() => handleFieldChange('level', option.value)}
            activeOpacity={0.7}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: tokens.spacing('md'),
              borderBottomWidth: index < languageLevelOptions.length - 1 ? 1 : 0,
              borderBottomColor: colors.border,
              backgroundColor: formData.level === option.value ? colors.primary + '20' : 'transparent',
            }}
          >
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: tokens.fontSize('body'),
                fontWeight: tokens.fontWeight('medium') as any,
                color: formData.level === option.value ? colors.primary : colors.text,
                marginBottom: tokens.spacing('xs'),
              }}>
                {option.label}
              </Text>
              <Text style={{
                fontSize: tokens.fontSize('caption'),
                color: formData.level === option.value ? colors.primary : colors.textSecondary,
              }}>
                {option.description}
              </Text>
            </View>
            
            {formData.level === option.value && (
              <Ionicons name="checkmark-circle" size={20} color={colors.primary} />
            )}
          </TouchableOpacity>
        ))}
      </View>
      
      {errors.level && (
        <Text style={{
          fontSize: tokens.fontSize('caption'),
          color: colors.error,
          marginTop: tokens.spacing('xs'),
        }}>
          {errors.level}
        </Text>
      )}
    </View>
  );

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background,
    }}>
      {/* 头部 */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: tokens.spacing('lg'),
        paddingVertical: tokens.spacing('md'),
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
        backgroundColor: colors.surface,
      }}>
        <TouchableOpacity
          onPress={onCancel}
          activeOpacity={0.7}
        >
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>

        <Text style={{
          fontSize: tokens.fontSize('title-sm'),
          fontWeight: tokens.fontWeight('bold') as any,
          color: colors.text,
        }}>
          {isEditing ? '编辑语言能力' : '添加语言能力'}
        </Text>

        <TouchableOpacity
          onPress={handleSave}
          activeOpacity={0.7}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Text style={{
              fontSize: tokens.fontSize('body'),
              color: colors.primary,
              fontWeight: tokens.fontWeight('medium') as any,
            }}>
              保存
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* 表单内容 */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          padding: tokens.spacing('lg'),
        }}
        showsVerticalScrollIndicator={false}
      >
        {/* 语言名称 */}
        {renderInput(
          '语言名称',
          formData.name,
          (text) => handleFieldChange('name', text),
          '如：中文、英语、日语、法语',
          errors.name,
          false,
          true
        )}

        {/* 语言水平 */}
        {renderLevelPicker()}

        {/* 语言描述 */}
        {renderInput(
          '语言描述',
          formData.description,
          (text) => handleFieldChange('description', text),
          '如：CET-6、TOEFL 100分、能够进行技术交流（可选）',
          errors.description,
          true
        )}

        {/* 删除按钮（编辑模式下显示） */}
        {isEditing && onDelete && (
          <TouchableOpacity
            onPress={handleDelete}
            activeOpacity={0.7}
            style={{
              backgroundColor: colors.error + '20',
              borderRadius: tokens.radius('sm'),
              padding: tokens.spacing('md'),
              alignItems: 'center',
              marginTop: tokens.spacing('lg'),
              marginBottom: tokens.spacing('xl'),
            }}
          >
            <Text style={{
              fontSize: tokens.fontSize('body'),
              color: colors.error,
              fontWeight: tokens.fontWeight('medium') as any,
            }}>
              删除这个语言能力
            </Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );
};
