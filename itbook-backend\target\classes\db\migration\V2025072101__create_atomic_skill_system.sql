-- ===================================================================
-- 原子技能+知识图谱+动态学习路径 数据库设计
-- 版本: V2025072101
-- 描述: 创建原子技能库、知识图谱关系、动态路径等核心表
-- ===================================================================

-- ----------------------------
-- 原子技能库 - 存储最小可验证技能单元
-- ----------------------------
DROP TABLE IF EXISTS `atomic_skill`;
CREATE TABLE `atomic_skill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '原子技能ID',
  `skill_code` varchar(100) NOT NULL COMMENT '技能编码，如"java-oop-inheritance"',
  `name` varchar(200) NOT NULL COMMENT '技能名称，如"Java面向对象-继承"',
  `description` text COMMENT '技能详细描述',
  `category` varchar(100) NOT NULL COMMENT '技能分类，如"编程语言"',
  `subcategory` varchar(100) COMMENT '技能子分类，如"Java"',
  
  -- 技能属性
  `difficulty_level` enum('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT') NOT NULL DEFAULT 'BEGINNER' COMMENT '难度级别',
  `estimated_hours` int DEFAULT 0 COMMENT '预计学习时长（小时）',
  `skill_type` enum('CORE','SUPPORTING','BONUS') NOT NULL DEFAULT 'CORE' COMMENT '技能类型：核心/支撑/加分',
  
  -- 验证标准
  `verification_criteria` json COMMENT '验证标准，JSON格式存储评估方法',
  `assessment_method` enum('QUIZ','PROJECT','PRACTICE','PEER_REVIEW') DEFAULT 'QUIZ' COMMENT '评估方法',
  `pass_threshold` decimal(3,2) DEFAULT 0.70 COMMENT '通过阈值(0-1)',
  
  -- 学习资源
  `learning_resources` json COMMENT '学习资源列表，包含课程、文档、视频等',
  `practice_exercises` json COMMENT '练习题目列表',
  
  -- 标签和元数据
  `tags` json COMMENT '技能标签',
  `keywords` text COMMENT '关键词，用于搜索',
  `industry_relevance` json COMMENT '行业相关性，存储相关行业和权重',
  
  -- 统计信息
  `learner_count` int DEFAULT 0 COMMENT '学习人数',
  `completion_rate` decimal(5,2) DEFAULT 0.00 COMMENT '完成率',
  `average_rating` decimal(3,2) DEFAULT 0.00 COMMENT '平均评分',
  
  -- 状态管理
  `status` enum('DRAFT','PUBLISHED','DEPRECATED') NOT NULL DEFAULT 'DRAFT' COMMENT '状态',
  `version` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  
  -- 时间戳
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint COMMENT '创建者ID',
  `updated_by` bigint COMMENT '更新者ID',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_atomic_skill_code` (`skill_code`),
  KEY `idx_atomic_skill_category` (`category`, `subcategory`),
  KEY `idx_atomic_skill_difficulty` (`difficulty_level`),
  KEY `idx_atomic_skill_type` (`skill_type`),
  KEY `idx_atomic_skill_status` (`status`, `is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原子技能库';

-- ----------------------------
-- 技能关系图谱 - 存储技能间的依赖和关联关系
-- ----------------------------
DROP TABLE IF EXISTS `skill_relationship`;
CREATE TABLE `skill_relationship` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `source_skill_id` bigint NOT NULL COMMENT '源技能ID',
  `target_skill_id` bigint NOT NULL COMMENT '目标技能ID',
  
  -- 关系类型
  `relationship_type` enum('PREREQUISITE','COREQUISITE','SUCCESSOR','RELATED','ALTERNATIVE') NOT NULL COMMENT '关系类型',
  `relationship_strength` decimal(3,2) DEFAULT 1.00 COMMENT '关系强度(0-1)',
  `is_mandatory` tinyint(1) DEFAULT 0 COMMENT '是否必需关系',
  
  -- 关系描述
  `description` text COMMENT '关系描述',
  `learning_sequence` int COMMENT '学习顺序权重',
  
  -- 条件和规则
  `condition_rules` json COMMENT '条件规则，JSON格式存储复杂条件',
  `validation_rules` json COMMENT '验证规则',
  
  -- 元数据
  `confidence_score` decimal(3,2) DEFAULT 1.00 COMMENT '置信度分数',
  `source` enum('MANUAL','AUTO_GENERATED','ML_INFERRED') DEFAULT 'MANUAL' COMMENT '关系来源',
  
  -- 状态管理
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint COMMENT '创建者ID',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_skill_relationship` (`source_skill_id`, `target_skill_id`, `relationship_type`),
  KEY `idx_skill_rel_source` (`source_skill_id`),
  KEY `idx_skill_rel_target` (`target_skill_id`),
  KEY `idx_skill_rel_type` (`relationship_type`),
  CONSTRAINT `fk_skill_rel_source` FOREIGN KEY (`source_skill_id`) REFERENCES `atomic_skill` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_skill_rel_target` FOREIGN KEY (`target_skill_id`) REFERENCES `atomic_skill` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能关系图谱';

-- ----------------------------
-- 动态学习路径 - 存储个性化生成的学习路径
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_learning_path`;
CREATE TABLE `dynamic_learning_path` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '动态路径ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `career_goal_id` bigint COMMENT '关联的职业目标ID',
  `base_path_id` bigint COMMENT '基础学习路径ID（可选）',
  
  -- 路径基本信息
  `name` varchar(200) NOT NULL COMMENT '路径名称',
  `description` text COMMENT '路径描述',
  `path_type` enum('CAREER_ORIENTED','SKILL_FOCUSED','PROJECT_BASED','CUSTOM') NOT NULL DEFAULT 'CAREER_ORIENTED' COMMENT '路径类型',
  
  -- 个性化参数
  `personalization_factors` json COMMENT '个性化因子，包含用户偏好、能力等',
  `learning_style` enum('THEORETICAL','PRACTICAL','PROJECT_DRIVEN','MIXED') DEFAULT 'MIXED' COMMENT '学习风格',
  `difficulty_preference` enum('GRADUAL','CHALLENGING','MIXED') DEFAULT 'GRADUAL' COMMENT '难度偏好',
  `time_constraint` int COMMENT '时间约束（周）',
  
  -- 路径统计
  `total_skills` int DEFAULT 0 COMMENT '总技能数',
  `estimated_hours` int DEFAULT 0 COMMENT '预计总时长',
  `completion_rate` decimal(5,2) DEFAULT 0.00 COMMENT '完成率',
  `current_step` int DEFAULT 1 COMMENT '当前步骤',
  
  -- 算法信息
  `generation_algorithm` varchar(50) DEFAULT 'v1.0' COMMENT '生成算法版本',
  `generation_params` json COMMENT '生成参数',
  `quality_score` decimal(3,2) DEFAULT 0.00 COMMENT '路径质量分数',
  
  -- 状态管理
  `status` enum('DRAFT','ACTIVE','PAUSED','COMPLETED','ABANDONED') NOT NULL DEFAULT 'DRAFT' COMMENT '路径状态',
  `is_template` tinyint(1) DEFAULT 0 COMMENT '是否为模板',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `started_at` datetime COMMENT '开始时间',
  `completed_at` datetime COMMENT '完成时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_dynamic_path_user` (`user_id`),
  KEY `idx_dynamic_path_career` (`career_goal_id`),
  KEY `idx_dynamic_path_status` (`status`),
  KEY `idx_dynamic_path_type` (`path_type`),
  CONSTRAINT `fk_dynamic_path_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_dynamic_path_career` FOREIGN KEY (`career_goal_id`) REFERENCES `career_goal` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_dynamic_path_base` FOREIGN KEY (`base_path_id`) REFERENCES `learning_path` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='动态学习路径';

-- ----------------------------
-- 动态路径步骤 - 存储动态路径的具体学习步骤
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_path_step`;
CREATE TABLE `dynamic_path_step` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '步骤ID',
  `path_id` bigint NOT NULL COMMENT '动态路径ID',
  `atomic_skill_id` bigint NOT NULL COMMENT '原子技能ID',
  
  -- 步骤信息
  `step_order` int NOT NULL COMMENT '步骤顺序',
  `step_type` enum('LEARN','PRACTICE','ASSESS','PROJECT','REVIEW') NOT NULL DEFAULT 'LEARN' COMMENT '步骤类型',
  `estimated_hours` int DEFAULT 0 COMMENT '预计时长',
  
  -- 个性化调整
  `difficulty_adjustment` decimal(3,2) DEFAULT 1.00 COMMENT '难度调整系数',
  `priority_weight` decimal(3,2) DEFAULT 1.00 COMMENT '优先级权重',
  `personalization_reason` text COMMENT '个性化原因',
  
  -- 学习资源
  `recommended_resources` json COMMENT '推荐学习资源',
  `alternative_resources` json COMMENT '备选资源',
  
  -- 进度跟踪
  `status` enum('NOT_STARTED','IN_PROGRESS','COMPLETED','SKIPPED') NOT NULL DEFAULT 'NOT_STARTED' COMMENT '步骤状态',
  `progress_percentage` decimal(5,2) DEFAULT 0.00 COMMENT '进度百分比',
  `completion_score` decimal(3,2) COMMENT '完成分数',
  
  -- 时间记录
  `started_at` datetime COMMENT '开始时间',
  `completed_at` datetime COMMENT '完成时间',
  `actual_hours` decimal(5,2) COMMENT '实际用时',
  
  -- 反馈和调整
  `user_feedback` json COMMENT '用户反馈',
  `system_feedback` json COMMENT '系统反馈',
  `adjustment_history` json COMMENT '调整历史',
  
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_path_step_order` (`path_id`, `step_order`),
  KEY `idx_path_step_skill` (`atomic_skill_id`),
  KEY `idx_path_step_status` (`status`),
  KEY `idx_path_step_type` (`step_type`),
  CONSTRAINT `fk_path_step_path` FOREIGN KEY (`path_id`) REFERENCES `dynamic_learning_path` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_path_step_skill` FOREIGN KEY (`atomic_skill_id`) REFERENCES `atomic_skill` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='动态路径步骤';

-- ----------------------------
-- 用户技能掌握度 - 存储用户对原子技能的掌握情况
-- ----------------------------
DROP TABLE IF EXISTS `user_atomic_skill_mastery`;
CREATE TABLE `user_atomic_skill_mastery` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '掌握度记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `atomic_skill_id` bigint NOT NULL COMMENT '原子技能ID',

  -- 掌握度信息
  `mastery_level` enum('NONE','BASIC','INTERMEDIATE','ADVANCED','EXPERT') NOT NULL DEFAULT 'NONE' COMMENT '掌握水平',
  `mastery_score` decimal(5,2) DEFAULT 0.00 COMMENT '掌握分数(0-100)',
  `confidence_level` decimal(3,2) DEFAULT 0.00 COMMENT '置信度(0-1)',

  -- 学习历史
  `learning_hours` decimal(6,2) DEFAULT 0.00 COMMENT '学习时长',
  `practice_count` int DEFAULT 0 COMMENT '练习次数',
  `assessment_count` int DEFAULT 0 COMMENT '评估次数',
  `last_assessment_score` decimal(5,2) COMMENT '最后评估分数',

  -- 时间记录
  `first_learned_at` datetime COMMENT '首次学习时间',
  `last_practiced_at` datetime COMMENT '最后练习时间',
  `last_assessed_at` datetime COMMENT '最后评估时间',
  `mastery_achieved_at` datetime COMMENT '掌握达成时间',

  -- 学习路径关联
  `learned_via_path_id` bigint COMMENT '通过哪个路径学习',
  `learning_context` json COMMENT '学习上下文信息',

  -- 状态和元数据
  `is_certified` tinyint(1) DEFAULT 0 COMMENT '是否已认证',
  `certification_date` datetime COMMENT '认证日期',
  `needs_refresh` tinyint(1) DEFAULT 0 COMMENT '是否需要复习',
  `decay_factor` decimal(3,2) DEFAULT 1.00 COMMENT '遗忘衰减因子',

  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_skill_mastery` (`user_id`, `atomic_skill_id`),
  KEY `idx_mastery_level` (`mastery_level`),
  KEY `idx_mastery_score` (`mastery_score`),
  KEY `idx_user_mastery_user` (`user_id`),
  KEY `idx_user_mastery_skill` (`atomic_skill_id`),
  CONSTRAINT `fk_user_mastery_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_mastery_skill` FOREIGN KEY (`atomic_skill_id`) REFERENCES `atomic_skill` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_mastery_path` FOREIGN KEY (`learned_via_path_id`) REFERENCES `dynamic_learning_path` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户原子技能掌握度';

-- ----------------------------
-- 职业技能映射 - 将现有career_skill与原子技能关联
-- ----------------------------
DROP TABLE IF EXISTS `career_skill_mapping`;
CREATE TABLE `career_skill_mapping` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '映射ID',
  `career_skill_id` bigint NOT NULL COMMENT '职业技能ID（关联career_skill表）',
  `atomic_skill_id` bigint NOT NULL COMMENT '原子技能ID',

  -- 映射关系
  `weight` decimal(3,2) DEFAULT 1.00 COMMENT '权重(0-1)',
  `importance` enum('CRITICAL','IMPORTANT','NICE_TO_HAVE') NOT NULL DEFAULT 'IMPORTANT' COMMENT '重要程度',
  `required_mastery_level` enum('BASIC','INTERMEDIATE','ADVANCED','EXPERT') NOT NULL DEFAULT 'INTERMEDIATE' COMMENT '要求掌握水平',

  -- 映射元数据
  `mapping_reason` text COMMENT '映射原因',
  `mapping_source` enum('MANUAL','AUTO_GENERATED','ML_INFERRED') DEFAULT 'MANUAL' COMMENT '映射来源',
  `confidence_score` decimal(3,2) DEFAULT 1.00 COMMENT '置信度',

  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint COMMENT '创建者ID',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_career_atomic_mapping` (`career_skill_id`, `atomic_skill_id`),
  KEY `idx_mapping_career_skill` (`career_skill_id`),
  KEY `idx_mapping_atomic_skill` (`atomic_skill_id`),
  CONSTRAINT `fk_mapping_career_skill` FOREIGN KEY (`career_skill_id`) REFERENCES `career_skill` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_mapping_atomic_skill` FOREIGN KEY (`atomic_skill_id`) REFERENCES `atomic_skill` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职业技能与原子技能映射';

-- ----------------------------
-- 技能评估记录 - 存储技能评估的详细记录
-- ----------------------------
DROP TABLE IF EXISTS `skill_assessment_record`;
CREATE TABLE `skill_assessment_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评估记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `atomic_skill_id` bigint NOT NULL COMMENT '原子技能ID',
  `assessment_type` enum('QUIZ','PROJECT','PRACTICE','PEER_REVIEW','SELF_ASSESSMENT') NOT NULL COMMENT '评估类型',

  -- 评估结果
  `score` decimal(5,2) NOT NULL COMMENT '评估分数',
  `max_score` decimal(5,2) NOT NULL DEFAULT 100.00 COMMENT '最高分数',
  `pass_status` enum('PASS','FAIL','PARTIAL') NOT NULL COMMENT '通过状态',
  `mastery_level_achieved` enum('NONE','BASIC','INTERMEDIATE','ADVANCED','EXPERT') COMMENT '达到的掌握水平',

  -- 评估详情
  `assessment_data` json COMMENT '评估详细数据',
  `feedback` text COMMENT '评估反馈',
  `improvement_suggestions` json COMMENT '改进建议',

  -- 评估上下文
  `assessment_context` json COMMENT '评估上下文',
  `time_spent_minutes` int COMMENT '评估用时（分钟）',
  `attempt_number` int DEFAULT 1 COMMENT '尝试次数',

  -- 评估者信息
  `assessor_type` enum('SYSTEM','PEER','INSTRUCTOR','SELF') DEFAULT 'SYSTEM' COMMENT '评估者类型',
  `assessor_id` bigint COMMENT '评估者ID',

  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评估时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  KEY `idx_assessment_user` (`user_id`),
  KEY `idx_assessment_skill` (`atomic_skill_id`),
  KEY `idx_assessment_type` (`assessment_type`),
  KEY `idx_assessment_score` (`score`),
  KEY `idx_assessment_date` (`created_at`),
  CONSTRAINT `fk_assessment_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_assessment_skill` FOREIGN KEY (`atomic_skill_id`) REFERENCES `atomic_skill` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能评估记录';

-- ----------------------------
-- 创建索引以优化查询性能
-- ----------------------------

-- 原子技能相关索引
CREATE INDEX `idx_atomic_skill_search` ON `atomic_skill` (`name`, `category`, `subcategory`);
CREATE INDEX `idx_atomic_skill_stats` ON `atomic_skill` (`learner_count`, `completion_rate`, `average_rating`);

-- 技能关系图谱索引
CREATE INDEX `idx_skill_rel_graph` ON `skill_relationship` (`source_skill_id`, `relationship_type`, `relationship_strength`);
CREATE INDEX `idx_skill_rel_reverse` ON `skill_relationship` (`target_skill_id`, `relationship_type`);

-- 动态路径相关索引
CREATE INDEX `idx_dynamic_path_user_status` ON `dynamic_learning_path` (`user_id`, `status`);
CREATE INDEX `idx_dynamic_path_career_type` ON `dynamic_learning_path` (`career_goal_id`, `path_type`);

-- 用户掌握度索引
CREATE INDEX `idx_user_mastery_level_score` ON `user_atomic_skill_mastery` (`user_id`, `mastery_level`, `mastery_score`);
CREATE INDEX `idx_skill_mastery_stats` ON `user_atomic_skill_mastery` (`atomic_skill_id`, `mastery_level`);

-- 评估记录索引
CREATE INDEX `idx_assessment_user_skill_date` ON `skill_assessment_record` (`user_id`, `atomic_skill_id`, `created_at`);
CREATE INDEX `idx_assessment_skill_stats` ON `skill_assessment_record` (`atomic_skill_id`, `pass_status`, `score`);
