import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>View, Alert, Dimensions } from 'react-native';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Card, LoaderScreen } from 'react-native-ui-lib';
import { Ionicons } from '@expo/vector-icons';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';
import { useThemeColors } from '../../contexts/ThemeContext';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { tokens } from '../../design-tokens';
import { Header } from '../../components/ui';
import { DynamicLearningPathService } from '../../services/DynamicLearningPathService';
import { CareerGoalService } from '../../services/CareerGoalService';

const { width: screenWidth } = Dimensions.get('window');

type RootStackParamList = {
  PathGenerationWizard: undefined;
  LearningPathDetail: { pathId: string; userId: number };
};

type Props = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'PathGenerationWizard'>;
  route: RouteProp<RootStackParamList, 'PathGenerationWizard'>;
};

interface CareerGoal {
  id: number;
  title: string;
  description: string;
  category: string;
  difficultyLevel: string;
  estimatedMonths: number;
  requiredSkills: string[];
  salaryRange?: string;
  jobProspects?: string;
}

interface GenerationStep {
  id: number;
  title: string;
  description: string;
  completed: boolean;
  current: boolean;
}

/**
 * 路径生成向导页面
 * 引导用户一步步创建个性化学习路径
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
const PathGenerationWizardScreen: React.FC<Props> = ({ navigation }) => {
  const colors = useThemeColors();
  const { user } = useSelector((state: RootState) => state.auth);
  const userId = user?.id || 2;

  // 状态管理
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [careerGoals, setCareerGoals] = useState<CareerGoal[]>([]);
  const [selectedCareerGoal, setSelectedCareerGoal] = useState<CareerGoal | null>(null);
  const [skillLevel, setSkillLevel] = useState<'beginner' | 'intermediate' | 'advanced'>('beginner');
  const [learningPreferences, setLearningPreferences] = useState({
    timeCommitment: 'moderate', // light, moderate, intensive
    learningStyle: 'balanced', // theoretical, practical, balanced
    projectFocus: true,
    certificationGoal: false
  });

  // 步骤配置
  const steps: GenerationStep[] = [
    {
      id: 1,
      title: '选择职业目标',
      description: '选择你想要达成的职业目标',
      completed: currentStep > 1,
      current: currentStep === 1
    },
    {
      id: 2,
      title: '评估技能水平',
      description: '告诉我们你当前的技能水平',
      completed: currentStep > 2,
      current: currentStep === 2
    },
    {
      id: 3,
      title: '学习偏好设置',
      description: '设置你的学习偏好和时间安排',
      completed: currentStep > 3,
      current: currentStep === 3
    },
    {
      id: 4,
      title: '生成学习路径',
      description: '基于你的选择生成个性化路径',
      completed: currentStep > 4,
      current: currentStep === 4
    }
  ];

  useEffect(() => {
    loadCareerGoals();
  }, []);

  // 加载职业目标列表
  const loadCareerGoals = async () => {
    try {
      setLoading(true);
      const goals = await CareerGoalService.getAllCareerGoals();
      setCareerGoals(goals);
    } catch (error) {
      console.error('加载职业目标失败:', error);
      Alert.alert('加载失败', '无法加载职业目标列表，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 生成学习路径
  const generateLearningPath = async () => {
    if (!selectedCareerGoal) {
      Alert.alert('提示', '请先选择职业目标');
      return;
    }

    try {
      setLoading(true);
      
      const dynamicService = new DynamicLearningPathService();
      const result = await dynamicService.generatePersonalizedPath(userId, selectedCareerGoal.id);
      
      if (result.success && result.data) {
        Alert.alert(
          '生成成功',
          '个性化学习路径已生成！',
          [
            {
              text: '查看路径',
              onPress: () => navigation.navigate('LearningPathDetail', {
                pathId: result.data.id.toString(),
                userId
              })
            }
          ]
        );
      } else {
        Alert.alert('生成失败', result.message || '生成学习路径失败');
      }
    } catch (error) {
      console.error('生成学习路径失败:', error);
      Alert.alert('生成失败', '生成学习路径时发生错误，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 渲染步骤指示器
  const renderStepIndicator = () => (
    <View style={{
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginHorizontal: tokens.spacing('lg'),
      marginVertical: tokens.spacing('md')
    }}>
      {steps.map((step, index) => (
        <View key={step.id} style={{ flex: 1, alignItems: 'center' }}>
          <View style={{
            width: 32,
            height: 32,
            borderRadius: 16,
            backgroundColor: step.completed ? colors.primary : 
                           step.current ? colors.primary : colors.surfaceVariant,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: tokens.spacing('xs')
          }}>
            {step.completed ? (
              <Ionicons name="checkmark" size={18} color={colors.onPrimary} />
            ) : (
              <Text style={{
                color: step.current ? colors.onPrimary : colors.onSurfaceVariant,
                fontSize: tokens.fontSize('sm'),
                fontWeight: tokens.fontWeight('bold')
              }}>
                {step.id}
              </Text>
            )}
          </View>
          
          <Text style={{
            color: step.current ? colors.primary : colors.textSecondary,
            fontSize: tokens.fontSize('xs'),
            fontWeight: step.current ? tokens.fontWeight('semibold') : tokens.fontWeight('normal'),
            textAlign: 'center',
            maxWidth: 80
          }}>
            {step.title}
          </Text>
          
          {index < steps.length - 1 && (
            <View style={{
              position: 'absolute',
              top: 16,
              left: '50%',
              width: screenWidth / steps.length - 40,
              height: 2,
              backgroundColor: step.completed ? colors.primary : colors.border,
              zIndex: -1
            }} />
          )}
        </View>
      ))}
    </View>
  );

  // 渲染职业目标选择
  const renderCareerGoalSelection = () => (
    <View style={{ padding: tokens.spacing('lg') }}>
      <Text style={{
        color: colors.text,
        fontSize: tokens.fontSize('title-sm'),
        fontWeight: tokens.fontWeight('bold'),
        marginBottom: tokens.spacing('md')
      }}>
        选择你的职业目标
      </Text>
      
      <Text style={{
        color: colors.textSecondary,
        fontSize: tokens.fontSize('sm'),
        marginBottom: tokens.spacing('lg'),
        lineHeight: 20
      }}>
        选择一个你想要达成的职业目标，我们将为你生成相应的学习路径
      </Text>

      {careerGoals.map((goal) => (
        <Card
          key={goal.id}
          style={{
            backgroundColor: selectedCareerGoal?.id === goal.id ? colors.primaryContainer : colors.surface,
            borderColor: selectedCareerGoal?.id === goal.id ? colors.primary : colors.border,
            borderWidth: selectedCareerGoal?.id === goal.id ? 2 : 1,
            borderRadius: tokens.radius('md'),
            padding: tokens.spacing('md'),
            marginBottom: tokens.spacing('md')
          }}
          onPress={() => setSelectedCareerGoal(goal)}
        >
          <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
            <View style={{ flex: 1 }}>
              <Text style={{
                color: selectedCareerGoal?.id === goal.id ? colors.onPrimaryContainer : colors.text,
                fontSize: tokens.fontSize('md'),
                fontWeight: tokens.fontWeight('semibold'),
                marginBottom: tokens.spacing('xs')
              }}>
                {goal.title}
              </Text>
              
              <Text style={{
                color: selectedCareerGoal?.id === goal.id ? colors.onPrimaryContainer : colors.textSecondary,
                fontSize: tokens.fontSize('sm'),
                marginBottom: tokens.spacing('sm'),
                lineHeight: 18
              }}>
                {goal.description}
              </Text>
              
              <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: tokens.spacing('xs') }}>
                <View style={{
                  backgroundColor: colors.secondaryContainer,
                  paddingHorizontal: tokens.spacing('sm'),
                  paddingVertical: tokens.spacing('xs'),
                  borderRadius: tokens.radius('sm')
                }}>
                  <Text style={{
                    color: colors.onSecondaryContainer,
                    fontSize: tokens.fontSize('xs'),
                    fontWeight: tokens.fontWeight('medium')
                  }}>
                    {goal.difficultyLevel}
                  </Text>
                </View>
                
                <View style={{
                  backgroundColor: colors.tertiaryContainer,
                  paddingHorizontal: tokens.spacing('sm'),
                  paddingVertical: tokens.spacing('xs'),
                  borderRadius: tokens.radius('sm')
                }}>
                  <Text style={{
                    color: colors.onTertiaryContainer,
                    fontSize: tokens.fontSize('xs'),
                    fontWeight: tokens.fontWeight('medium')
                  }}>
                    {goal.estimatedMonths}个月
                  </Text>
                </View>
              </View>
            </View>
            
            {selectedCareerGoal?.id === goal.id && (
              <Ionicons 
                name="checkmark-circle" 
                size={24} 
                color={colors.primary} 
                style={{ marginLeft: tokens.spacing('sm') }}
              />
            )}
          </View>
        </Card>
      ))}
    </View>
  );

  // 渲染技能水平评估
  const renderSkillLevelAssessment = () => (
    <View style={{ padding: tokens.spacing('lg') }}>
      <Text style={{
        color: colors.text,
        fontSize: tokens.fontSize('title-sm'),
        fontWeight: tokens.fontWeight('bold'),
        marginBottom: tokens.spacing('md')
      }}>
        评估你的技能水平
      </Text>
      
      <Text style={{
        color: colors.textSecondary,
        fontSize: tokens.fontSize('sm'),
        marginBottom: tokens.spacing('lg'),
        lineHeight: 20
      }}>
        请选择最符合你当前技能水平的选项，这将帮助我们为你推荐合适的学习内容
      </Text>

      {[
        {
          level: 'beginner' as const,
          title: '初学者',
          description: '刚开始学习相关技术，需要从基础开始',
          icon: 'leaf-outline'
        },
        {
          level: 'intermediate' as const,
          title: '中级',
          description: '有一定基础，能够独立完成简单项目',
          icon: 'trending-up-outline'
        },
        {
          level: 'advanced' as const,
          title: '高级',
          description: '经验丰富，能够处理复杂项目和技术难题',
          icon: 'rocket-outline'
        }
      ].map((option) => (
        <Card
          key={option.level}
          style={{
            backgroundColor: skillLevel === option.level ? colors.primaryContainer : colors.surface,
            borderColor: skillLevel === option.level ? colors.primary : colors.border,
            borderWidth: skillLevel === option.level ? 2 : 1,
            borderRadius: tokens.radius('md'),
            padding: tokens.spacing('md'),
            marginBottom: tokens.spacing('md')
          }}
          onPress={() => setSkillLevel(option.level)}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{
              width: 48,
              height: 48,
              borderRadius: tokens.radius('md'),
              backgroundColor: skillLevel === option.level ? colors.primary : colors.surfaceVariant,
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: tokens.spacing('md')
            }}>
              <Ionicons 
                name={option.icon as any} 
                size={24} 
                color={skillLevel === option.level ? colors.onPrimary : colors.onSurfaceVariant} 
              />
            </View>
            
            <View style={{ flex: 1 }}>
              <Text style={{
                color: skillLevel === option.level ? colors.onPrimaryContainer : colors.text,
                fontSize: tokens.fontSize('md'),
                fontWeight: tokens.fontWeight('semibold'),
                marginBottom: tokens.spacing('xs')
              }}>
                {option.title}
              </Text>
              
              <Text style={{
                color: skillLevel === option.level ? colors.onPrimaryContainer : colors.textSecondary,
                fontSize: tokens.fontSize('sm'),
                lineHeight: 18
              }}>
                {option.description}
              </Text>
            </View>
            
            {skillLevel === option.level && (
              <Ionicons 
                name="checkmark-circle" 
                size={24} 
                color={colors.primary} 
                style={{ marginLeft: tokens.spacing('sm') }}
              />
            )}
          </View>
        </Card>
      ))}
    </View>
  );

  if (loading) {
    return (
      <LoaderScreen 
        color={colors.primary}
        message="正在处理中..."
        overlay
      />
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Header
        title="创建学习路径"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />
      
      {renderStepIndicator()}
      
      <ScrollView 
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
      >
        {currentStep === 1 && renderCareerGoalSelection()}
        {currentStep === 2 && renderSkillLevelAssessment()}
        {currentStep === 3 && renderLearningPreferences()}
        {currentStep === 4 && renderPathGeneration()}
      </ScrollView>
      
      {/* 底部操作按钮 */}
      <View style={{
        padding: tokens.spacing('lg'),
        borderTopWidth: 1,
        borderTopColor: colors.border,
        backgroundColor: colors.surface
      }}>
        <View style={{ flexDirection: 'row', gap: tokens.spacing('md') }}>
          {currentStep > 1 && (
            <Button
              label="上一步"
              size="large"
              backgroundColor={colors.surfaceVariant}
              color={colors.onSurfaceVariant}
              onPress={() => setCurrentStep(currentStep - 1)}
              style={{ flex: 1 }}
            />
          )}
          
          <Button
            label={currentStep === 4 ? '生成路径' : '下一步'}
            size="large"
            backgroundColor={colors.primary}
            color={colors.onPrimary}
            onPress={() => {
              if (currentStep === 4) {
                generateLearningPath();
              } else if (currentStep === 1 && !selectedCareerGoal) {
                Alert.alert('提示', '请先选择职业目标');
              } else {
                setCurrentStep(currentStep + 1);
              }
            }}
            style={{ flex: currentStep > 1 ? 1 : undefined }}
          />
        </View>
      </View>
    </View>
  );
};

export default PathGenerationWizardScreen;
