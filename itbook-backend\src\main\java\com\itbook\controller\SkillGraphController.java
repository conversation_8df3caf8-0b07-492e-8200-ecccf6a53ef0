package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.SkillGraphService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 技能图算法控制器
 * 提供技能关系图的算法分析API，包括路径推荐、依赖检测、关系强度计算等
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@RestController
@RequestMapping("/skill-graph")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "技能图算法", description = "技能关系图的算法分析功能")
public class SkillGraphController {

    private final SkillGraphService skillGraphService;

    /**
     * 构建技能学习路径
     */
    @GetMapping("/learning-path/{skillId}")
    @Operation(summary = "构建学习路径", description = "基于技能关系图构建从基础技能到目标技能的最优学习路径")
    public ResponseEntity<ApiResponse<SkillGraphService.LearningPath>> buildLearningPath(
            @Parameter(description = "目标技能ID") @PathVariable Long skillId) {
        
        log.debug("构建学习路径: skillId={}", skillId);
        
        try {
            SkillGraphService.LearningPath learningPath = skillGraphService.buildOptimalLearningPath(skillId);
            return ResponseEntity.ok(ApiResponse.success(learningPath));
        } catch (IllegalArgumentException e) {
            log.warn("构建学习路径失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.badRequest(e.getMessage()));
        } catch (Exception e) {
            log.error("构建学习路径时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("构建学习路径失败"));
        }
    }

    /**
     * 推荐相关技能
     */
    @GetMapping("/recommendations/{skillId}")
    @Operation(summary = "推荐相关技能", description = "基于技能关系强度和类型推荐相关技能")
    public ResponseEntity<ApiResponse<List<SkillGraphService.SkillRecommendation>>> recommendSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId,
            @Parameter(description = "最大推荐数量") @RequestParam(defaultValue = "10") int maxRecommendations) {
        
        log.debug("推荐相关技能: skillId={}, maxRecommendations={}", skillId, maxRecommendations);
        
        try {
            List<SkillGraphService.SkillRecommendation> recommendations = 
                    skillGraphService.recommendRelatedSkills(skillId, maxRecommendations);
            return ResponseEntity.ok(ApiResponse.success(recommendations));
        } catch (Exception e) {
            log.error("推荐相关技能时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("推荐相关技能失败"));
        }
    }

    /**
     * 检测循环依赖
     */
    @GetMapping("/cycles")
    @Operation(summary = "检测循环依赖", description = "检测技能关系图中的循环依赖")
    public ResponseEntity<ApiResponse<List<List<Long>>>> detectCycles() {
        
        log.debug("检测循环依赖");
        
        try {
            SkillGraphService.SkillGraph graph = skillGraphService.buildSkillGraph();
            List<List<Long>> cycles = skillGraphService.detectCycles(graph);
            return ResponseEntity.ok(ApiResponse.success(cycles));
        } catch (Exception e) {
            log.error("检测循环依赖时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("检测循环依赖失败"));
        }
    }

    /**
     * 计算技能关系强度
     */
    @GetMapping("/relationship-strength")
    @Operation(summary = "计算关系强度", description = "计算两个技能之间的关系强度")
    public ResponseEntity<ApiResponse<BigDecimal>> calculateRelationshipStrength(
            @Parameter(description = "源技能ID") @RequestParam Long sourceSkillId,
            @Parameter(description = "目标技能ID") @RequestParam Long targetSkillId) {
        
        log.debug("计算关系强度: sourceSkillId={}, targetSkillId={}", sourceSkillId, targetSkillId);
        
        try {
            BigDecimal strength = skillGraphService.calculateRelationshipStrength(sourceSkillId, targetSkillId);
            return ResponseEntity.ok(ApiResponse.success(strength));
        } catch (Exception e) {
            log.error("计算关系强度时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("计算关系强度失败"));
        }
    }

    /**
     * 获取技能图统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取图统计", description = "获取技能关系图的统计信息")
    public ResponseEntity<ApiResponse<SkillGraphService.SkillGraphStatistics>> getGraphStatistics() {

        log.debug("获取技能图统计信息");

        try {
            SkillGraphService.SkillGraphStatistics statistics = skillGraphService.getGraphStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            log.error("获取技能图统计时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取图统计失败"));
        }
    }

    /**
     * 分析技能重要性
     */
    @GetMapping("/importance/{skillId}")
    @Operation(summary = "分析技能重要性", description = "基于图算法分析技能在整个技能图中的重要性")
    public ResponseEntity<ApiResponse<SkillGraphService.SkillImportanceAnalysis>> analyzeSkillImportance(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {

        log.debug("分析技能重要性: skillId={}", skillId);

        try {
            SkillGraphService.SkillImportanceAnalysis analysis = skillGraphService.analyzeSkillImportance(skillId);
            return ResponseEntity.ok(ApiResponse.success(analysis));
        } catch (Exception e) {
            log.error("分析技能重要性时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("分析技能重要性失败"));
        }
    }

    /**
     * 查找技能路径
     */
    @GetMapping("/path")
    @Operation(summary = "查找技能路径", description = "查找从源技能到目标技能的最短路径")
    public ResponseEntity<ApiResponse<List<Long>>> findSkillPath(
            @Parameter(description = "源技能ID") @RequestParam Long sourceSkillId,
            @Parameter(description = "目标技能ID") @RequestParam Long targetSkillId) {
        
        log.debug("查找技能路径: sourceSkillId={}, targetSkillId={}", sourceSkillId, targetSkillId);
        
        try {
            List<Long> path = skillGraphService.findShortestPath(sourceSkillId, targetSkillId);
            return ResponseEntity.ok(ApiResponse.success(path));
        } catch (Exception e) {
            log.error("查找技能路径时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("查找技能路径失败"));
        }
    }

    /**
     * 获取技能聚类
     */
    @GetMapping("/clusters")
    @Operation(summary = "获取技能聚类", description = "基于技能关系进行聚类分析")
    public ResponseEntity<ApiResponse<List<SkillGraphService.SkillCluster>>> getSkillClusters() {

        log.debug("获取技能聚类");

        try {
            List<SkillGraphService.SkillCluster> clusters = skillGraphService.performSkillClustering();
            return ResponseEntity.ok(ApiResponse.success(clusters));
        } catch (Exception e) {
            log.error("获取技能聚类时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取技能聚类失败"));
        }
    }

}
