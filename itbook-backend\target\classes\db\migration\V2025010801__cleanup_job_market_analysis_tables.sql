-- 清理Job相关的市场分析表
-- 这些表在架构简化后不再使用，统一使用CareerGoal + CareerMarketData架构
-- 
-- <AUTHOR> Team  
-- @since 2025-01-08
-- @description 删除Job相关的市场分析表，保持数据库架构与代码架构一致

-- 禁用外键检查，避免删除顺序问题
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 删除技能需求统计数据表
DROP TABLE IF EXISTS `skill_demand_stats`;

-- 2. 删除薪资统计数据表  
DROP TABLE IF EXISTS `salary_statistics`;

-- 3. 删除岗位市场趋势历史数据表
DROP TABLE IF EXISTS `job_market_trends`;

-- 4. 删除热门城市数据表
DROP TABLE IF EXISTS `market_locations`;

-- 5. 删除热门公司数据表
DROP TABLE IF EXISTS `market_companies`;

-- 6. 删除市场分析核心数据表
DROP TABLE IF EXISTS `market_analysis`;

-- 7. 删除市场分析数据版本控制表
DROP TABLE IF EXISTS `market_data_versions`;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 记录迁移日志
INSERT INTO migration_log (
    migration_name, 
    description, 
    executed_at, 
    executed_by
) VALUES (
    'V2025010801__cleanup_job_market_analysis_tables',
    '清理Job相关的市场分析表，统一使用CareerGoal架构',
    NOW(),
    'system'
);
