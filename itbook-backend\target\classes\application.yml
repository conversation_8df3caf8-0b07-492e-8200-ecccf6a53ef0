# ITBook后端应用 - 基础配置
# 此配置文件包含所有环境的通用配置

server:
  port: ${SERVER_PORT:8888}
  servlet:
    context-path: /api
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: itbook-backend

  # 激活的配置文件（通过环境变量或启动参数指定）
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  # 数据库连接池配置
  datasource:
    hikari:
      maximum-pool-size: ${DB_POOL_SIZE:10}
      minimum-idle: ${DB_POOL_MIN_IDLE:5}
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
      leak-detection-threshold: 60000

  # JPA通用配置
  jpa:
    open-in-view: false
    properties:
      hibernate:
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
        connection:
          provider_disables_autocommit: true

  # 缓存配置（使用本地缓存替代Redis）
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=300s

# JWT配置
jwt:
  secret: ${JWT_SECRET:itbook-secret-key-2025-very-long-and-secure}
  expiration: ${JWT_EXPIRATION:86400000} # 24小时
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7天

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
    enabled: ${SWAGGER_ENABLED:true}
  swagger-ui:
    path: /swagger-ui.html
    enabled: ${SWAGGER_UI_ENABLED:true}
    tags-sorter: alpha
    operations-sorter: alpha

# 文件上传配置
spring.servlet.multipart:
  max-file-size: ${MAX_FILE_SIZE:10MB}
  max-request-size: ${MAX_REQUEST_SIZE:10MB}
  # 临时文件处理配置
  location: ${java.io.tmpdir}/itbook-uploads
  file-size-threshold: 1MB
  # 启用延迟解析以优化内存使用
  resolve-lazily: true

# 应用自定义配置
app:
  name: ITBook学习平台
  version: ${APP_VERSION:1.0.0}
  environment: ${APP_ENV:dev}

  # 服务器配置
  server:
    # 服务器基础URL，用于生成文件访问链接等
    # 格式: http://域名:端口 或 https://域名
    base-url: ${APP_SERVER_BASE_URL:http://localhost:8888}

  # 文件上传配置
  upload:
    # 使用项目根目录下的uploads文件夹
    path: uploads
    max-size: ${MAX_FILE_SIZE:10485760} # 10MB

  # 功能开关
  features:
    enable-analytics: ${ENABLE_ANALYTICS:false}
    enable-monitoring: ${ENABLE_MONITORING:false}
    enable-cache: ${ENABLE_CACHE:true}
    enable-rate-limiting: ${ENABLE_RATE_LIMITING:false}

  # 安全配置
  security:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8083,http://localhost:19006}
      allowed-methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
      allowed-headers: ${CORS_ALLOWED_HEADERS:*}
      allow-credentials: ${CORS_ALLOW_CREDENTIALS:true}

    # 密码策略
    password:
      min-length: ${PASSWORD_MIN_LENGTH:6}
      require-uppercase: ${PASSWORD_REQUIRE_UPPERCASE:false}
      require-lowercase: ${PASSWORD_REQUIRE_LOWERCASE:false}
      require-numbers: ${PASSWORD_REQUIRE_NUMBERS:false}
      require-special-chars: ${PASSWORD_REQUIRE_SPECIAL_CHARS:false}




