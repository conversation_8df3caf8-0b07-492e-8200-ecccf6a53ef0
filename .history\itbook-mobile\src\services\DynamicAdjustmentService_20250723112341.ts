/**
 * 动态调整服务类
 * 
 * 封装所有动态学习路径调整相关的API调用
 * 提供统一的数据访问接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */

import { apiService } from './ApiService';
import {
  LearningEventRequest,
  FeedbackRequest,
  PathAdjustmentRequest,
  SmartAdjustmentRequest,
  LearningBehaviorAnalysis,
  PathAdjustmentResult,
  FeedbackAnalysis,
  EffectivenessEvaluation,
  ComprehensiveAnalysis,
  SmartAdjustmentResult,
  SystemStatus
} from '../types/DynamicAdjustment';

/**
 * 动态调整服务类
 */
export class DynamicAdjustmentService {
  private static readonly BASE_PATH = '/dynamic-adjustment';

  // ==================== 学习进度跟踪相关API ====================

  /**
   * 记录学习行为事件
   */
  static async recordLearningEvent(event: LearningEventRequest): Promise<any> {
    try {
      console.log('📝 记录学习行为事件:', event);
      
      const response = await apiService.post(`${this.BASE_PATH}/learning-events`, event);

      console.log('✅ 学习行为事件记录成功:', response);
      return response.data;
    } catch (error) {
      console.error('❌ 学习行为事件记录失败:', error);
      throw error;
    }
  }

  /**
   * 分析用户学习行为
   */
  static async analyzeLearningBehavior(userId: number, pathId: number): Promise<LearningBehaviorAnalysis> {
    try {
      console.log('🔍 分析用户学习行为:', { userId, pathId });
      
      const response = await apiService.get(`${this.BASE_PATH}/users/${userId}/paths/${pathId}/behavior-analysis`);
      
      console.log('✅ 学习行为分析完成:', response);
      return response;
    } catch (error) {
      console.error('❌ 学习行为分析失败:', error);
      throw error;
    }
  }

  // ==================== 路径调整相关API ====================

  /**
   * 执行路径调整
   */
  static async adjustPath(request: PathAdjustmentRequest): Promise<PathAdjustmentResult> {
    try {
      console.log('🔧 执行路径调整:', request);
      
      const response = await apiService.post(`${this.BASE_PATH}/path-adjustment`, request);

      console.log('✅ 路径调整完成:', response);
      return response;
    } catch (error) {
      console.error('❌ 路径调整失败:', error);
      throw error;
    }
  }

  /**
   * 自动路径调整
   */
  static async autoAdjustPath(userId: number, pathId: number): Promise<PathAdjustmentResult[]> {
    try {
      console.log('🤖 自动路径调整:', { userId, pathId });

      const response = await apiService.post(`${this.BASE_PATH}/users/${userId}/paths/${pathId}/auto-adjust`);

      console.log('✅ 自动路径调整完成:', response);
      return response;
    } catch (error) {
      console.error('❌ 自动路径调整失败:', error);
      throw error;
    }
  }

  /**
   * 评估调整必要性
   */
  static async evaluateAdjustmentNeed(userId: number, pathId: number): Promise<any> {
    try {
      console.log('📊 评估路径调整必要性:', { userId, pathId });

      const response = await apiService.get(`${this.BASE_PATH}/users/${userId}/paths/${pathId}/adjustment-evaluation`);
      
      console.log('✅ 路径调整必要性评估完成:', response);
      return response;
    } catch (error) {
      console.error('❌ 路径调整必要性评估失败:', error);
      throw error;
    }
  }

  // ==================== 用户反馈相关API ====================

  /**
   * 提交用户反馈
   */
  static async submitFeedback(feedback: FeedbackRequest): Promise<any> {
    try {
      console.log('📝 提交用户反馈:', feedback);
      
      const response = await apiService.post(`${this.BASE_PATH}/feedback`, feedback);

      console.log('✅ 用户反馈提交成功:', response);
      return response;
    } catch (error) {
      console.error('❌ 用户反馈提交失败:', error);
      throw error;
    }
  }

  /**
   * 分析路径反馈
   */
  static async analyzeFeedback(pathId: number): Promise<FeedbackAnalysis> {
    try {
      console.log('📊 分析路径反馈:', { pathId });

      const response = await apiService.get(`${this.BASE_PATH}/paths/${pathId}/feedback-analysis`);
      
      console.log('✅ 路径反馈分析完成:', response);
      return response;
    } catch (error) {
      console.error('❌ 路径反馈分析失败:', error);
      throw error;
    }
  }

  // ==================== 学习效果评估相关API ====================

  /**
   * 评估学习效果
   */
  static async evaluateEffectiveness(userId: number, pathId: number): Promise<EffectivenessEvaluation> {
    try {
      console.log('📊 评估学习效果:', { userId, pathId });
      
      const response = await apiService.get(`${this.BASE_PATH}/users/${userId}/paths/${pathId}/effectiveness-evaluation`);

      console.log('✅ 学习效果评估完成:', response);
      return response;
    } catch (error) {
      console.error('❌ 学习效果评估失败:', error);
      throw error;
    }
  }

  /**
   * 获取评估摘要
   */
  static async getEvaluationSummary(userId: number): Promise<any> {
    try {
      console.log('📈 获取评估摘要:', { userId });

      const response = await apiService.get(`${this.BASE_PATH}/users/${userId}/evaluation-summary`);
      
      console.log('✅ 评估摘要获取成功:', response);
      return response;
    } catch (error) {
      console.error('❌ 评估摘要获取失败:', error);
      throw error;
    }
  }

  // ==================== 综合分析API ====================

  /**
   * 综合分析用户学习状况
   */
  static async comprehensiveAnalysis(userId: number, pathId: number): Promise<ComprehensiveAnalysis> {
    try {
      console.log('🔍 综合分析用户学习状况:', { userId, pathId });
      
      const response = await apiService.get(`${this.BASE_PATH}/users/${userId}/paths/${pathId}/comprehensive-analysis`);

      console.log('✅ 综合分析完成:', response);
      return response.data;
    } catch (error) {
      console.error('❌ 综合分析失败:', error);
      throw error;
    }
  }

  /**
   * 智能调整建议
   */
  static async smartAdjustment(
    userId: number,
    pathId: number,
    request?: SmartAdjustmentRequest
  ): Promise<SmartAdjustmentResult> {
    try {
      console.log('🧠 智能调整建议:', { userId, pathId, request });

      const response = await apiService.post(
        `${this.BASE_PATH}/users/${userId}/paths/${pathId}/smart-adjustment`,
        request || {}
      );

      console.log('✅ 智能调整建议完成:', response);
      return response.data;
    } catch (error) {
      console.error('❌ 智能调整建议失败:', error);
      throw error;
    }
  }

  /**
   * 获取系统状态
   */
  static async getSystemStatus(): Promise<SystemStatus> {
    try {
      console.log('📊 获取系统状态');

      const response = await apiService.get(`${this.BASE_PATH}/system-status`);
      
      console.log('✅ 系统状态获取成功:', response);
      return response;
    } catch (error) {
      console.error('❌ 系统状态获取失败:', error);
      throw error;
    }
  }

  // ==================== 辅助方法 ====================

  /**
   * 格式化效果等级显示文本
   */
  static formatEffectivenessLevel(level: string): string {
    const levelMap: Record<string, string> = {
      'EXCELLENT': '优秀',
      'GOOD': '良好',
      'AVERAGE': '一般',
      'POOR': '较差',
      'VERY_POOR': '很差'
    };
    return levelMap[level] || level;
  }

  /**
   * 格式化调整策略显示文本
   */
  static formatAdjustmentStrategy(strategy: string): string {
    const strategyMap: Record<string, string> = {
      'DIFFICULTY_ADJUSTMENT': '难度调整',
      'CONTENT_REPLACEMENT': '内容替换',
      'STEP_REORDERING': '步骤重排序',
      'PATH_EXTENSION': '路径扩展',
      'PATH_SIMPLIFICATION': '路径简化',
      'COMPLETE_REGENERATION': '完全重新生成'
    };
    return strategyMap[strategy] || strategy;
  }

  /**
   * 格式化反馈类型显示文本
   */
  static formatFeedbackType(type: string): string {
    const typeMap: Record<string, string> = {
      'DIFFICULTY_RATING': '难度评分',
      'QUALITY_RATING': '质量评分',
      'CONTENT_FEEDBACK': '内容反馈',
      'LEARNING_EXPERIENCE': '学习体验',
      'SUGGESTION': '改进建议',
      'BUG_REPORT': '问题报告',
      'GENERAL_COMMENT': '一般评论'
    };
    return typeMap[type] || type;
  }

  /**
   * 获取风险等级颜色
   */
  static getRiskLevelColor(riskLevel: string): string {
    const colorMap: Record<string, string> = {
      'LOW': '#4CAF50',
      'MEDIUM': '#FFC107',
      'HIGH': '#F44336',
      'UNKNOWN': '#9E9E9E'
    };
    return colorMap[riskLevel] || '#9E9E9E';
  }

  /**
   * 获取效果等级颜色
   */
  static getEffectivenessLevelColor(level: string): string {
    const colorMap: Record<string, string> = {
      'EXCELLENT': '#4CAF50',
      'GOOD': '#8BC34A',
      'AVERAGE': '#FFC107',
      'POOR': '#FF9800',
      'VERY_POOR': '#F44336'
    };
    return colorMap[level] || '#9E9E9E';
  }
}
