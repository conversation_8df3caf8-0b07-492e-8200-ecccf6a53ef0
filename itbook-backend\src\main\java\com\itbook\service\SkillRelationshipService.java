package com.itbook.service;

import com.itbook.entity.AtomicSkill;
import com.itbook.entity.SkillRelationship;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.repository.SkillRelationshipRepository;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 技能关系服务层
 * 提供技能关系图谱的业务逻辑处理，实现知识图谱构建的核心功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SkillRelationshipService {

    private final SkillRelationshipRepository skillRelationshipRepository;
    private final AtomicSkillRepository atomicSkillRepository;

    /**
     * 获取所有活跃的技能关系
     */
    @Transactional(readOnly = true)
    public List<SkillRelationship> getAllActiveRelationships() {
        log.debug("获取所有活跃的技能关系");
        return skillRelationshipRepository.findAll().stream()
                .filter(relationship -> relationship.getIsActive() != null && relationship.getIsActive())
                .collect(Collectors.toList());
    }

    /**
     * 获取指定技能的前置技能
     */
    @Transactional(readOnly = true)
    public List<AtomicSkill> getPrerequisiteSkills(Long skillId) {
        log.debug("获取前置技能: skillId={}", skillId);

        List<SkillRelationship> relationships = skillRelationshipRepository.findPrerequisiteRelationships(skillId);

        return relationships.stream()
                .map(relationship -> {
                    AtomicSkill skill = atomicSkillRepository.findById(relationship.getSourceSkillId()).orElse(null);
                    return createCleanSkill(skill);
                })
                .filter(skill -> skill != null)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定技能的后续技能
     */
    @Transactional(readOnly = true)
    public List<AtomicSkill> getSuccessorSkills(Long skillId) {
        log.debug("获取后续技能: skillId={}", skillId);

        List<SkillRelationship> relationships = skillRelationshipRepository.findSuccessorRelationships(skillId);

        return relationships.stream()
                .map(relationship -> {
                    AtomicSkill skill = atomicSkillRepository.findById(relationship.getTargetSkillId()).orElse(null);
                    return createCleanSkill(skill);
                })
                .filter(skill -> skill != null)
                .collect(Collectors.toList());
    }

    /**
     * 创建一个干净的AtomicSkill对象，避免懒加载问题
     */
    public AtomicSkill createCleanSkill(AtomicSkill skill) {
        if (skill == null) {
            return null;
        }

        AtomicSkill cleanSkill = new AtomicSkill();
        cleanSkill.setId(skill.getId());
        cleanSkill.setSkillCode(skill.getSkillCode());
        cleanSkill.setName(skill.getName());
        cleanSkill.setDescription(skill.getDescription());
        cleanSkill.setCategory(skill.getCategory());
        cleanSkill.setSubcategory(skill.getSubcategory());
        cleanSkill.setDifficultyLevel(skill.getDifficultyLevel());
        cleanSkill.setEstimatedHours(skill.getEstimatedHours());
        cleanSkill.setSkillType(skill.getSkillType());
        cleanSkill.setAssessmentMethod(skill.getAssessmentMethod());
        cleanSkill.setPassThreshold(skill.getPassThreshold());
        cleanSkill.setKeywords(skill.getKeywords());
        cleanSkill.setLearnerCount(skill.getLearnerCount());
        cleanSkill.setCompletionRate(skill.getCompletionRate());
        cleanSkill.setAverageRating(skill.getAverageRating());
        cleanSkill.setStatus(skill.getStatus());
        cleanSkill.setVersion(skill.getVersion());
        cleanSkill.setIsActive(skill.getIsActive());
        cleanSkill.setCreatedAt(skill.getCreatedAt());
        cleanSkill.setUpdatedAt(skill.getUpdatedAt());
        return cleanSkill;
    }

    /**
     * 获取指定技能的相关技能
     */
    @Transactional(readOnly = true)
    public List<AtomicSkill> getRelatedSkills(Long skillId) {
        log.debug("获取相关技能: skillId={}", skillId);

        List<SkillRelationship> relationships = skillRelationshipRepository.findRelatedSkills(skillId);

        return relationships.stream()
                .map(relationship -> {
                    // 获取关系中的另一个技能ID
                    Long otherSkillId = relationship.getSourceSkillId().equals(skillId)
                            ? relationship.getTargetSkillId()
                            : relationship.getSourceSkillId();
                    AtomicSkill skill = atomicSkillRepository.findById(otherSkillId).orElse(null);
                    return createCleanSkill(skill);
                })
                .filter(skill -> skill != null)
                .collect(Collectors.toList());
    }

    /**
     * 获取指定技能的并行技能
     */
    @Transactional(readOnly = true)
    public List<AtomicSkill> getCorequisiteSkills(Long skillId) {
        log.debug("获取并行技能: skillId={}", skillId);
        
        List<SkillRelationship> relationships = skillRelationshipRepository.findCorequisiteRelationships(skillId);
        
        return relationships.stream()
                .map(relationship -> {
                    // 获取关系中的另一个技能ID
                    Long otherSkillId = relationship.getSourceSkillId().equals(skillId) 
                            ? relationship.getTargetSkillId() 
                            : relationship.getSourceSkillId();
                    return atomicSkillRepository.findById(otherSkillId);
                })
                .filter(optional -> optional.isPresent())
                .map(optional -> optional.get())
                .collect(Collectors.toList());
    }

    /**
     * 获取指定技能的替代技能
     */
    @Transactional(readOnly = true)
    public List<AtomicSkill> getAlternativeSkills(Long skillId) {
        log.debug("获取替代技能: skillId={}", skillId);
        
        List<SkillRelationship> relationships = skillRelationshipRepository.findAlternativeRelationships(skillId);
        
        return relationships.stream()
                .map(relationship -> {
                    // 获取关系中的另一个技能ID
                    Long otherSkillId = relationship.getSourceSkillId().equals(skillId) 
                            ? relationship.getTargetSkillId() 
                            : relationship.getSourceSkillId();
                    return atomicSkillRepository.findById(otherSkillId);
                })
                .filter(optional -> optional.isPresent())
                .map(optional -> optional.get())
                .collect(Collectors.toList());
    }

    /**
     * 创建技能关系
     */
    @Transactional
    public SkillRelationship createRelationship(SkillRelationship relationship) {
        log.info("创建技能关系: sourceSkillId={}, targetSkillId={}, type={}", 
                relationship.getSourceSkillId(), relationship.getTargetSkillId(), relationship.getRelationshipType());
        
        // 验证源技能和目标技能是否存在
        if (!atomicSkillRepository.existsById(relationship.getSourceSkillId())) {
            throw new IllegalArgumentException("源技能不存在: " + relationship.getSourceSkillId());
        }
        
        if (!atomicSkillRepository.existsById(relationship.getTargetSkillId())) {
            throw new IllegalArgumentException("目标技能不存在: " + relationship.getTargetSkillId());
        }
        
        // 设置默认值
        if (relationship.getIsActive() == null) {
            relationship.setIsActive(true);
        }
        
        if (relationship.getRelationshipStrength() == null) {
            relationship.setRelationshipStrength(java.math.BigDecimal.ONE);
        }
        
        if (relationship.getIsMandatory() == null) {
            relationship.setIsMandatory(false);
        }
        
        return skillRelationshipRepository.save(relationship);
    }

    /**
     * 根据关系类型获取技能关系
     */
    @Transactional(readOnly = true)
    public List<SkillRelationship> getRelationshipsByType(SkillRelationship.RelationshipType relationshipType) {
        log.debug("根据类型获取技能关系: relationshipType={}", relationshipType);
        return skillRelationshipRepository.findByRelationshipType(relationshipType);
    }

    /**
     * 构建技能学习路径
     * 基于前置技能关系，为指定技能构建完整的学习路径
     */
    @Transactional(readOnly = true)
    public SkillLearningPath buildLearningPath(Long targetSkillId) {
        log.debug("构建技能学习路径: targetSkillId={}", targetSkillId);
        
        AtomicSkill targetSkill = atomicSkillRepository.findById(targetSkillId)
                .orElseThrow(() -> new IllegalArgumentException("目标技能不存在: " + targetSkillId));
        
        SkillLearningPath path = new SkillLearningPath();
        path.setTargetSkill(targetSkill);
        
        // 递归构建前置技能路径
        buildPrerequisitePath(targetSkillId, path, 0);
        
        return path;
    }

    /**
     * 递归构建前置技能路径
     */
    private void buildPrerequisitePath(Long skillId, SkillLearningPath path, int depth) {
        if (depth > 10) { // 防止循环依赖
            log.warn("技能依赖深度过深，可能存在循环依赖: skillId={}", skillId);
            return;
        }
        
        List<SkillRelationship> prerequisites = skillRelationshipRepository.findPrerequisitesByLearningSequence(skillId);
        
        for (SkillRelationship relationship : prerequisites) {
            AtomicSkill prerequisiteSkill = atomicSkillRepository.findById(relationship.getSourceSkillId())
                    .orElse(null);
            
            if (prerequisiteSkill != null) {
                SkillLearningPath.LearningStep step = new SkillLearningPath.LearningStep();
                step.setSkill(prerequisiteSkill);
                step.setSequence(relationship.getLearningSequence());
                step.setIsMandatory(relationship.getIsMandatory());
                step.setRelationshipStrength(relationship.getRelationshipStrength());
                
                path.addStep(step);
                
                // 递归构建更深层的前置技能
                buildPrerequisitePath(prerequisiteSkill.getId(), path, depth + 1);
            }
        }
    }

    /**
     * 获取技能关系统计信息
     */
    @Transactional(readOnly = true)
    public RelationshipStatistics getRelationshipStatistics() {
        log.debug("获取技能关系统计信息");
        
        RelationshipStatistics statistics = new RelationshipStatistics();
        
        // 总关系数
        statistics.setTotalRelationships(skillRelationshipRepository.count());
        
        // 活跃关系数
        statistics.setActiveRelationships((long) getAllActiveRelationships().size());
        
        // 各类型关系数量
        statistics.setPrerequisiteCount(skillRelationshipRepository.findByRelationshipType(SkillRelationship.RelationshipType.PREREQUISITE).size());
        statistics.setSuccessorCount(skillRelationshipRepository.findByRelationshipType(SkillRelationship.RelationshipType.SUCCESSOR).size());
        statistics.setRelatedCount(skillRelationshipRepository.findByRelationshipType(SkillRelationship.RelationshipType.RELATED).size());
        statistics.setCorequisiteCount(skillRelationshipRepository.findByRelationshipType(SkillRelationship.RelationshipType.COREQUISITE).size());
        statistics.setAlternativeCount(skillRelationshipRepository.findByRelationshipType(SkillRelationship.RelationshipType.ALTERNATIVE).size());
        
        // 必需关系数
        statistics.setMandatoryRelationships(skillRelationshipRepository.findMandatoryRelationships().size());
        
        return statistics;
    }

    /**
     * 技能关系统计信息
     */
    @Data
    public static class RelationshipStatistics {
        private Long totalRelationships;
        private Long activeRelationships;
        private Integer prerequisiteCount;
        private Integer successorCount;
        private Integer relatedCount;
        private Integer corequisiteCount;
        private Integer alternativeCount;
        private Integer mandatoryRelationships;
    }

    /**
     * 技能学习路径
     */
    @Data
    public static class SkillLearningPath {
        private AtomicSkill targetSkill;
        private List<LearningStep> steps = new java.util.ArrayList<>();
        
        public void addStep(LearningStep step) {
            this.steps.add(step);
        }
        
        @Data
        public static class LearningStep {
            private AtomicSkill skill;
            private Integer sequence;
            private Boolean isMandatory;
            private java.math.BigDecimal relationshipStrength;
        }
    }
}
