package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.PathAdjustmentEngine;
import com.itbook.service.PathAdjustmentEngine.PathAdjustmentRequest;
import com.itbook.service.PathAdjustmentEngine.PathAdjustmentResult;
import com.itbook.service.PathAdjustmentEngine.AdjustmentTrigger;
import com.itbook.service.PathAdjustmentEngine.AdjustmentStrategy;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 路径调整控制器
 * 
 * 提供动态路径调整的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/path-adjustment")
@RequiredArgsConstructor
@Tag(name = "路径调整", description = "动态路径调整相关接口")
public class PathAdjustmentController {

    private final PathAdjustmentEngine pathAdjustmentEngine;

    /**
     * 执行路径调整
     */
    @PostMapping("/adjust")
    @Operation(summary = "执行路径调整", description = "基于用户学习情况和反馈调整学习路径")
    public ResponseEntity<ApiResponse<PathAdjustmentResult>> adjustPath(
            @Parameter(description = "路径调整请求", required = true)
            @Valid @RequestBody AdjustPathRequest request) {
        
        log.info("🔧 执行路径调整: userId={}, pathId={}, trigger={}", 
                request.getUserId(), request.getPathId(), request.getTrigger());
        
        try {
            // 构建调整请求
            PathAdjustmentRequest adjustmentRequest = new PathAdjustmentRequest();
            adjustmentRequest.setUserId(request.getUserId());
            adjustmentRequest.setPathId(request.getPathId());
            adjustmentRequest.setTrigger(AdjustmentTrigger.valueOf(request.getTrigger()));
            
            if (request.getPreferredStrategy() != null) {
                adjustmentRequest.setPreferredStrategy(AdjustmentStrategy.valueOf(request.getPreferredStrategy()));
            }
            
            adjustmentRequest.setAdjustmentData(request.getAdjustmentData() != null ? 
                    request.getAdjustmentData() : new HashMap<>());
            adjustmentRequest.setReason(request.getReason());

            // 执行调整
            PathAdjustmentResult result = pathAdjustmentEngine.adjustPath(adjustmentRequest);

            log.info("✅ 路径调整完成: pathId={}, strategy={}, improvement={}", 
                    request.getPathId(), result.getAppliedStrategy(), result.getQualityImprovement());

            return ResponseEntity.ok(ApiResponse.success("路径调整完成", result));

        } catch (Exception e) {
            log.error("❌ 路径调整失败: userId={}, pathId={}", 
                    request.getUserId(), request.getPathId(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径调整失败: " + e.getMessage()));
        }
    }

    /**
     * 自动路径调整
     */
    @PostMapping("/users/{userId}/paths/{pathId}/auto-adjust")
    @Operation(summary = "自动路径调整", description = "自动检测并执行路径调整")
    public ResponseEntity<ApiResponse<List<PathAdjustmentResult>>> autoAdjustPath(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("🤖 自动路径调整: userId={}, pathId={}", userId, pathId);
        
        try {
            // 执行自动调整
            List<PathAdjustmentResult> results = pathAdjustmentEngine.autoAdjustPath(userId, pathId);

            log.info("✅ 自动路径调整完成: userId={}, pathId={}, adjustmentCount={}", 
                    userId, pathId, results.size());

            return ResponseEntity.ok(ApiResponse.success("自动路径调整完成", results));

        } catch (Exception e) {
            log.error("❌ 自动路径调整失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("自动路径调整失败: " + e.getMessage()));
        }
    }

    /**
     * 评估调整必要性
     */
    @GetMapping("/users/{userId}/paths/{pathId}/evaluation")
    @Operation(summary = "评估调整必要性", description = "评估学习路径是否需要调整")
    public ResponseEntity<ApiResponse<Map<String, Object>>> evaluateAdjustmentNeed(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("📊 评估路径调整必要性: userId={}, pathId={}", userId, pathId);
        
        try {
            // 执行评估
            Map<String, Object> evaluation = pathAdjustmentEngine.evaluateAdjustmentNeed(userId, pathId);

            log.info("✅ 路径调整必要性评估完成: userId={}, pathId={}, adjustmentScore={}", 
                    userId, pathId, evaluation.get("adjustmentScore"));

            return ResponseEntity.ok(ApiResponse.success("路径调整必要性评估完成", evaluation));

        } catch (Exception e) {
            log.error("❌ 路径调整必要性评估失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("路径调整必要性评估失败: " + e.getMessage()));
        }
    }

    /**
     * 获取调整策略列表
     */
    @GetMapping("/strategies")
    @Operation(summary = "获取调整策略", description = "获取所有可用的路径调整策略")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAdjustmentStrategies() {
        
        log.info("📋 获取路径调整策略列表");
        
        try {
            Map<String, Object> strategies = new HashMap<>();
            
            for (AdjustmentStrategy strategy : AdjustmentStrategy.values()) {
                Map<String, String> strategyInfo = new HashMap<>();
                switch (strategy) {
                    case DIFFICULTY_ADJUSTMENT:
                        strategyInfo.put("name", "难度调整");
                        strategyInfo.put("description", "根据用户表现调整学习内容的难度级别");
                        break;
                    case CONTENT_REPLACEMENT:
                        strategyInfo.put("name", "内容替换");
                        strategyInfo.put("description", "替换质量较低或不适合的学习内容");
                        break;
                    case STEP_REORDERING:
                        strategyInfo.put("name", "步骤重排序");
                        strategyInfo.put("description", "优化学习步骤的顺序以提高效率");
                        break;
                    case PATH_EXTENSION:
                        strategyInfo.put("name", "路径扩展");
                        strategyInfo.put("description", "添加补充学习内容以增强学习效果");
                        break;
                    case PATH_SIMPLIFICATION:
                        strategyInfo.put("name", "路径简化");
                        strategyInfo.put("description", "移除非必要步骤以简化学习路径");
                        break;
                    case COMPLETE_REGENERATION:
                        strategyInfo.put("name", "完全重新生成");
                        strategyInfo.put("description", "基于当前状况重新生成整个学习路径");
                        break;
                }
                strategies.put(strategy.name(), strategyInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("strategies", strategies);
            responseData.put("totalCount", strategies.size());

            return ResponseEntity.ok(ApiResponse.success("获取路径调整策略成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取路径调整策略失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取路径调整策略失败: " + e.getMessage()));
        }
    }

    /**
     * 获取调整触发条件列表
     */
    @GetMapping("/triggers")
    @Operation(summary = "获取调整触发条件", description = "获取所有可用的路径调整触发条件")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAdjustmentTriggers() {
        
        log.info("📋 获取路径调整触发条件列表");
        
        try {
            Map<String, Object> triggers = new HashMap<>();
            
            for (AdjustmentTrigger trigger : AdjustmentTrigger.values()) {
                Map<String, String> triggerInfo = new HashMap<>();
                switch (trigger) {
                    case POOR_PERFORMANCE:
                        triggerInfo.put("name", "学习表现不佳");
                        triggerInfo.put("description", "用户学习完成率或质量较低");
                        break;
                    case HIGH_DIFFICULTY_RATING:
                        triggerInfo.put("name", "难度评分过高");
                        triggerInfo.put("description", "用户反馈内容难度过高");
                        break;
                    case LOW_QUALITY_RATING:
                        triggerInfo.put("name", "质量评分过低");
                        triggerInfo.put("description", "用户反馈内容质量较低");
                        break;
                    case SLOW_PROGRESS:
                        triggerInfo.put("name", "进度缓慢");
                        triggerInfo.put("description", "学习进度明显低于预期");
                        break;
                    case USER_FEEDBACK:
                        triggerInfo.put("name", "用户反馈");
                        triggerInfo.put("description", "基于用户主动反馈的调整需求");
                        break;
                    case SCHEDULED_OPTIMIZATION:
                        triggerInfo.put("name", "定期优化");
                        triggerInfo.put("description", "系统定期进行的路径优化");
                        break;
                }
                triggers.put(trigger.name(), triggerInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("triggers", triggers);
            responseData.put("totalCount", triggers.size());

            return ResponseEntity.ok(ApiResponse.success("获取路径调整触发条件成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取路径调整触发条件失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取路径调整触发条件失败: " + e.getMessage()));
        }
    }

    /**
     * 路径调整请求参数
     */
    public static class AdjustPathRequest {
        private Long userId;
        private Long pathId;
        private String trigger;
        private String preferredStrategy;
        private Map<String, Object> adjustmentData;
        private String reason;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public String getTrigger() { return trigger; }
        public void setTrigger(String trigger) { this.trigger = trigger; }
        
        public String getPreferredStrategy() { return preferredStrategy; }
        public void setPreferredStrategy(String preferredStrategy) { this.preferredStrategy = preferredStrategy; }
        
        public Map<String, Object> getAdjustmentData() { return adjustmentData; }
        public void setAdjustmentData(Map<String, Object> adjustmentData) { this.adjustmentData = adjustmentData; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
