package com.itbook.service;

import com.itbook.entity.AtomicSkill;
import com.itbook.entity.SkillRelationship;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.repository.SkillRelationshipRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 前置技能分析服务
 * 提供技能依赖分析、学习路径生成、前置技能查询等功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Slf4j
@Service
public class PrerequisiteAnalysisService {

    @Autowired
    private SkillRelationshipRepository skillRelationshipRepository;
    
    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    /**
     * 前置技能分析结果
     */
    public static class PrerequisiteAnalysisResult {
        private Long targetSkillId;
        private String targetSkillName;
        private List<SkillDependencyNode> directPrerequisites;
        private List<SkillDependencyNode> allPrerequisites;
        private List<LearningPathStep> recommendedPath;
        private int totalPrerequisites;
        private int maxDepth;
        private double complexity;
        private boolean hasCycles;

        // Constructors, getters and setters
        public PrerequisiteAnalysisResult() {}

        public PrerequisiteAnalysisResult(Long targetSkillId, String targetSkillName) {
            this.targetSkillId = targetSkillId;
            this.targetSkillName = targetSkillName;
            this.directPrerequisites = new ArrayList<>();
            this.allPrerequisites = new ArrayList<>();
            this.recommendedPath = new ArrayList<>();
        }

        // Getters and Setters
        public Long getTargetSkillId() { return targetSkillId; }
        public void setTargetSkillId(Long targetSkillId) { this.targetSkillId = targetSkillId; }
        
        public String getTargetSkillName() { return targetSkillName; }
        public void setTargetSkillName(String targetSkillName) { this.targetSkillName = targetSkillName; }
        
        public List<SkillDependencyNode> getDirectPrerequisites() { return directPrerequisites; }
        public void setDirectPrerequisites(List<SkillDependencyNode> directPrerequisites) { this.directPrerequisites = directPrerequisites; }
        
        public List<SkillDependencyNode> getAllPrerequisites() { return allPrerequisites; }
        public void setAllPrerequisites(List<SkillDependencyNode> allPrerequisites) { this.allPrerequisites = allPrerequisites; }
        
        public List<LearningPathStep> getRecommendedPath() { return recommendedPath; }
        public void setRecommendedPath(List<LearningPathStep> recommendedPath) { this.recommendedPath = recommendedPath; }
        
        public int getTotalPrerequisites() { return totalPrerequisites; }
        public void setTotalPrerequisites(int totalPrerequisites) { this.totalPrerequisites = totalPrerequisites; }
        
        public int getMaxDepth() { return maxDepth; }
        public void setMaxDepth(int maxDepth) { this.maxDepth = maxDepth; }
        
        public double getComplexity() { return complexity; }
        public void setComplexity(double complexity) { this.complexity = complexity; }
        
        public boolean isHasCycles() { return hasCycles; }
        public void setHasCycles(boolean hasCycles) { this.hasCycles = hasCycles; }
    }

    /**
     * 技能依赖节点
     */
    public static class SkillDependencyNode {
        private Long skillId;
        private String skillName;
        private String category;
        private String difficultyLevel;
        private int depth;
        private double importance;
        private String relationshipType;
        private List<SkillDependencyNode> children;

        public SkillDependencyNode() {
            this.children = new ArrayList<>();
        }

        public SkillDependencyNode(Long skillId, String skillName, String category, String difficultyLevel) {
            this();
            this.skillId = skillId;
            this.skillName = skillName;
            this.category = category;
            this.difficultyLevel = difficultyLevel;
        }

        public SkillDependencyNode(Long skillId, String skillName, String category, AtomicSkill.DifficultyLevel difficultyLevel) {
            this();
            this.skillId = skillId;
            this.skillName = skillName;
            this.category = category;
            this.difficultyLevel = difficultyLevel != null ? difficultyLevel.name().toLowerCase() : "beginner";
        }

        // Getters and Setters
        public Long getSkillId() { return skillId; }
        public void setSkillId(Long skillId) { this.skillId = skillId; }
        
        public String getSkillName() { return skillName; }
        public void setSkillName(String skillName) { this.skillName = skillName; }
        
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        
        public String getDifficultyLevel() { return difficultyLevel; }
        public void setDifficultyLevel(String difficultyLevel) { this.difficultyLevel = difficultyLevel; }
        
        public int getDepth() { return depth; }
        public void setDepth(int depth) { this.depth = depth; }
        
        public double getImportance() { return importance; }
        public void setImportance(double importance) { this.importance = importance; }
        
        public String getRelationshipType() { return relationshipType; }
        public void setRelationshipType(String relationshipType) { this.relationshipType = relationshipType; }
        
        public List<SkillDependencyNode> getChildren() { return children; }
        public void setChildren(List<SkillDependencyNode> children) { this.children = children; }
    }

    /**
     * 学习路径步骤
     */
    public static class LearningPathStep {
        private int step;
        private Long skillId;
        private String skillName;
        private String category;
        private String difficultyLevel;
        private int estimatedHours;
        private String reason;
        private List<Long> prerequisiteSkillIds;

        public LearningPathStep() {
            this.prerequisiteSkillIds = new ArrayList<>();
        }

        public LearningPathStep(int step, Long skillId, String skillName) {
            this();
            this.step = step;
            this.skillId = skillId;
            this.skillName = skillName;
        }

        // Getters and Setters
        public int getStep() { return step; }
        public void setStep(int step) { this.step = step; }
        
        public Long getSkillId() { return skillId; }
        public void setSkillId(Long skillId) { this.skillId = skillId; }
        
        public String getSkillName() { return skillName; }
        public void setSkillName(String skillName) { this.skillName = skillName; }
        
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        
        public String getDifficultyLevel() { return difficultyLevel; }
        public void setDifficultyLevel(String difficultyLevel) { this.difficultyLevel = difficultyLevel; }
        
        public int getEstimatedHours() { return estimatedHours; }
        public void setEstimatedHours(int estimatedHours) { this.estimatedHours = estimatedHours; }
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        
        public List<Long> getPrerequisiteSkillIds() { return prerequisiteSkillIds; }
        public void setPrerequisiteSkillIds(List<Long> prerequisiteSkillIds) { this.prerequisiteSkillIds = prerequisiteSkillIds; }
    }

    /**
     * 分析指定技能的前置技能
     *
     * @param skillId 目标技能ID
     * @return 前置技能分析结果
     */
    @Transactional(readOnly = true)
    public PrerequisiteAnalysisResult analyzePrerequisites(Long skillId) {
        log.info("开始分析技能前置依赖: skillId={}", skillId);
        
        // 获取目标技能信息
        Optional<AtomicSkill> targetSkillOpt = atomicSkillRepository.findById(skillId);
        if (!targetSkillOpt.isPresent()) {
            throw new RuntimeException("技能不存在: " + skillId);
        }
        
        AtomicSkill targetSkill = targetSkillOpt.get();
        PrerequisiteAnalysisResult result = new PrerequisiteAnalysisResult(skillId, targetSkill.getName());
        
        // 查找直接前置技能
        List<SkillDependencyNode> directPrerequisites = findDirectPrerequisites(skillId);
        result.setDirectPrerequisites(directPrerequisites);
        
        // 查找所有前置技能（递归）
        Set<Long> visited = new HashSet<>();
        List<SkillDependencyNode> allPrerequisites = findAllPrerequisites(skillId, visited, 0);
        result.setAllPrerequisites(allPrerequisites);
        
        // 生成推荐学习路径
        List<LearningPathStep> recommendedPath = generateLearningPath(skillId);
        result.setRecommendedPath(recommendedPath);
        
        // 计算统计信息
        result.setTotalPrerequisites(allPrerequisites.size());
        result.setMaxDepth(calculateMaxDepth(allPrerequisites));
        result.setComplexity(calculateComplexity(allPrerequisites));
        result.setHasCycles(detectCycles(skillId));
        
        log.info("前置技能分析完成: skillId={}, 直接前置={}, 总前置={}, 最大深度={}", 
                skillId, directPrerequisites.size(), allPrerequisites.size(), result.getMaxDepth());
        
        return result;
    }

    /**
     * 查找直接前置技能
     */
    private List<SkillDependencyNode> findDirectPrerequisites(Long skillId) {
        List<SkillRelationship> relationships = skillRelationshipRepository
                .findByTargetSkillIdAndRelationshipType(skillId, SkillRelationship.RelationshipType.PREREQUISITE);

        return relationships.stream()
                .map(rel -> {
                    AtomicSkill sourceSkill = rel.getSourceSkill();
                    SkillDependencyNode node = new SkillDependencyNode(
                            sourceSkill.getId(),
                            sourceSkill.getName(),
                            sourceSkill.getCategory(),
                            sourceSkill.getDifficultyLevel()
                    );
                    node.setDepth(1);
                    node.setRelationshipType(rel.getRelationshipType().name());
                    node.setImportance(rel.getRelationshipStrength() != null ? rel.getRelationshipStrength().doubleValue() : 1.0);
                    return node;
                })
                .collect(Collectors.toList());
    }

    /**
     * 递归查找所有前置技能
     */
    private List<SkillDependencyNode> findAllPrerequisites(Long skillId, Set<Long> visited, int depth) {
        if (visited.contains(skillId) || depth > 10) { // 防止无限递归
            return new ArrayList<>();
        }

        visited.add(skillId);
        List<SkillDependencyNode> allPrerequisites = new ArrayList<>();

        List<SkillRelationship> relationships = skillRelationshipRepository
                .findByTargetSkillIdAndRelationshipType(skillId, SkillRelationship.RelationshipType.PREREQUISITE);

        for (SkillRelationship rel : relationships) {
            AtomicSkill sourceSkill = rel.getSourceSkill();
            SkillDependencyNode node = new SkillDependencyNode(
                    sourceSkill.getId(),
                    sourceSkill.getName(),
                    sourceSkill.getCategory(),
                    sourceSkill.getDifficultyLevel()
            );
            node.setDepth(depth + 1);
            node.setRelationshipType(rel.getRelationshipType().name());
            node.setImportance(rel.getRelationshipStrength() != null ? rel.getRelationshipStrength().doubleValue() : 1.0);

            // 递归查找子前置技能
            List<SkillDependencyNode> childPrerequisites = findAllPrerequisites(sourceSkill.getId(), new HashSet<>(visited), depth + 1);
            node.setChildren(childPrerequisites);

            allPrerequisites.add(node);
            allPrerequisites.addAll(childPrerequisites);
        }

        return allPrerequisites;
    }

    /**
     * 生成推荐学习路径（拓扑排序）
     */
    private List<LearningPathStep> generateLearningPath(Long targetSkillId) {
        // 获取所有相关技能和关系
        Set<Long> allSkillIds = new HashSet<>();
        Map<Long, List<Long>> prerequisites = new HashMap<>();
        
        collectSkillsAndPrerequisites(targetSkillId, allSkillIds, prerequisites, new HashSet<>());
        
        // 执行拓扑排序
        List<Long> sortedSkillIds = topologicalSort(allSkillIds, prerequisites);
        
        // 构建学习路径步骤
        List<LearningPathStep> path = new ArrayList<>();
        for (int i = 0; i < sortedSkillIds.size(); i++) {
            Long skillId = sortedSkillIds.get(i);
            Optional<AtomicSkill> skillOpt = atomicSkillRepository.findById(skillId);
            
            if (skillOpt.isPresent()) {
                AtomicSkill skill = skillOpt.get();
                LearningPathStep step = new LearningPathStep(i + 1, skillId, skill.getName());
                step.setCategory(skill.getCategory());
                step.setDifficultyLevel(skill.getDifficultyLevel() != null ? skill.getDifficultyLevel().name().toLowerCase() : "beginner");
                step.setEstimatedHours(estimateHours(skill.getDifficultyLevel()));
                step.setReason(generateStepReason(skillId, prerequisites));
                step.setPrerequisiteSkillIds(prerequisites.getOrDefault(skillId, new ArrayList<>()));

                path.add(step);
            }
        }
        
        return path;
    }

    /**
     * 收集技能和前置关系
     */
    private void collectSkillsAndPrerequisites(Long skillId, Set<Long> allSkillIds,
                                             Map<Long, List<Long>> prerequisites, Set<Long> visited) {
        if (visited.contains(skillId)) {
            return;
        }

        visited.add(skillId);
        allSkillIds.add(skillId);

        List<SkillRelationship> relationships = skillRelationshipRepository
                .findByTargetSkillIdAndRelationshipType(skillId, SkillRelationship.RelationshipType.PREREQUISITE);

        List<Long> prereqIds = relationships.stream()
                .map(rel -> rel.getSourceSkill().getId())
                .collect(Collectors.toList());

        prerequisites.put(skillId, prereqIds);

        // 递归处理前置技能
        for (Long prereqId : prereqIds) {
            collectSkillsAndPrerequisites(prereqId, allSkillIds, prerequisites, visited);
        }
    }

    /**
     * 拓扑排序算法
     */
    private List<Long> topologicalSort(Set<Long> skillIds, Map<Long, List<Long>> prerequisites) {
        Map<Long, Integer> inDegree = new HashMap<>();
        Queue<Long> queue = new LinkedList<>();
        List<Long> result = new ArrayList<>();
        
        // 计算入度
        for (Long skillId : skillIds) {
            inDegree.put(skillId, 0);
        }
        
        for (Long skillId : skillIds) {
            List<Long> prereqs = prerequisites.getOrDefault(skillId, new ArrayList<>());
            for (Long prereq : prereqs) {
                inDegree.put(skillId, inDegree.get(skillId) + 1);
            }
        }
        
        // 找到入度为0的节点
        for (Long skillId : skillIds) {
            if (inDegree.get(skillId) == 0) {
                queue.offer(skillId);
            }
        }
        
        // 拓扑排序
        while (!queue.isEmpty()) {
            Long current = queue.poll();
            result.add(current);
            
            // 更新依赖当前技能的其他技能的入度
            for (Long skillId : skillIds) {
                List<Long> prereqs = prerequisites.getOrDefault(skillId, new ArrayList<>());
                if (prereqs.contains(current)) {
                    inDegree.put(skillId, inDegree.get(skillId) - 1);
                    if (inDegree.get(skillId) == 0) {
                        queue.offer(skillId);
                    }
                }
            }
        }
        
        return result;
    }

    /**
     * 计算最大深度
     */
    private int calculateMaxDepth(List<SkillDependencyNode> nodes) {
        return nodes.stream()
                .mapToInt(SkillDependencyNode::getDepth)
                .max()
                .orElse(0);
    }

    /**
     * 计算复杂度
     */
    private double calculateComplexity(List<SkillDependencyNode> nodes) {
        if (nodes.isEmpty()) {
            return 0.0;
        }
        
        int totalNodes = nodes.size();
        int maxDepth = calculateMaxDepth(nodes);
        double avgImportance = nodes.stream()
                .mapToDouble(SkillDependencyNode::getImportance)
                .average()
                .orElse(1.0);
        
        return (totalNodes * 0.4 + maxDepth * 0.4 + avgImportance * 0.2);
    }

    /**
     * 检测循环依赖
     */
    private boolean detectCycles(Long skillId) {
        Set<Long> visited = new HashSet<>();
        Set<Long> recursionStack = new HashSet<>();
        
        return hasCycleDFS(skillId, visited, recursionStack);
    }

    /**
     * DFS检测循环依赖
     */
    private boolean hasCycleDFS(Long skillId, Set<Long> visited, Set<Long> recursionStack) {
        if (recursionStack.contains(skillId)) {
            return true; // 发现循环
        }

        if (visited.contains(skillId)) {
            return false; // 已访问过，无循环
        }

        visited.add(skillId);
        recursionStack.add(skillId);

        List<SkillRelationship> relationships = skillRelationshipRepository
                .findByTargetSkillIdAndRelationshipType(skillId, SkillRelationship.RelationshipType.PREREQUISITE);

        for (SkillRelationship rel : relationships) {
            if (hasCycleDFS(rel.getSourceSkill().getId(), visited, recursionStack)) {
                return true;
            }
        }

        recursionStack.remove(skillId);
        return false;
    }

    /**
     * 估算学习时间
     */
    private int estimateHours(AtomicSkill.DifficultyLevel difficultyLevel) {
        if (difficultyLevel == null) {
            return 30;
        }

        switch (difficultyLevel) {
            case BEGINNER: return 20;
            case INTERMEDIATE: return 40;
            case ADVANCED: return 60;
            case EXPERT: return 80;
            default: return 30;
        }
    }

    /**
     * 生成步骤说明
     */
    private String generateStepReason(Long skillId, Map<Long, List<Long>> prerequisites) {
        List<Long> prereqs = prerequisites.getOrDefault(skillId, new ArrayList<>());
        if (prereqs.isEmpty()) {
            return "基础技能，可以直接开始学习";
        } else {
            return String.format("需要先掌握 %d 个前置技能", prereqs.size());
        }
    }
}
