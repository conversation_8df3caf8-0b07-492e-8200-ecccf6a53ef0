import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  StyleSheet
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../hooks/useThemeColors';
import { tokens } from '../../design-tokens';
import { LearningPathService } from '../../services/LearningPathService';
import { DynamicLearningPathService } from '../../services/DynamicLearningPathService';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';

/**
 * 路径管理页面
 * 提供完整的学习路径管理功能，包括创建、编辑、删除、历史记录等
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
const PathManagementScreen: React.FC = () => {
  const colors = useThemeColors();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'my-paths' | 'templates' | 'history'>('my-paths');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [myPaths, setMyPaths] = useState<any[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [pathHistory, setPathHistory] = useState<any[]>([]);

  // 创建路径表单状态
  const [createForm, setCreateForm] = useState({
    name: '',
    description: '',
    pathType: 'PERSONALIZED' as 'STANDARD' | 'PERSONALIZED' | 'TEMPLATE' | 'CUSTOM',
    difficultyLevel: 'BEGINNER' as 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED',
    learningObjectives: [] as string[],
    tags: [] as string[]
  });

  // 从Redux获取用户信息
  const { user } = useSelector((state: RootState) => state.auth);
  const userId = user?.id || 2;

  useEffect(() => {
    loadData();
  }, [activeTab]);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      
      switch (activeTab) {
        case 'my-paths':
          await loadMyPaths();
          break;
        case 'templates':
          await loadTemplates();
          break;
        case 'history':
          await loadPathHistory();
          break;
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      Alert.alert('加载失败', '数据加载失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 加载我的路径
  const loadMyPaths = async () => {
    try {
      const learningPathService = new LearningPathService();
      const paths = await learningPathService.getUserLearningPaths(userId);
      setMyPaths(paths || []);
    } catch (error) {
      console.error('加载我的路径失败:', error);
      setMyPaths([]);
    }
  };

  // 加载路径模板
  const loadTemplates = async () => {
    try {
      const learningPathService = new LearningPathService();
      const templatePaths = await learningPathService.getTemplates();
      setTemplates(templatePaths || []);
    } catch (error) {
      console.error('加载路径模板失败:', error);
      setTemplates([]);
    }
  };

  // 加载路径历史
  const loadPathHistory = async () => {
    try {
      const dynamicService = new DynamicLearningPathService();
      const history = await dynamicService.getPathHistory(userId);
      setPathHistory(history?.data || []);
    } catch (error) {
      console.error('加载路径历史失败:', error);
      setPathHistory([]);
    }
  };

  // 创建新路径
  const createNewPath = async () => {
    try {
      if (!createForm.name.trim()) {
        Alert.alert('提示', '请输入路径名称');
        return;
      }

      setLoading(true);
      const learningPathService = new LearningPathService();
      
      const newPath = await learningPathService.createLearningPath({
        name: createForm.name,
        description: createForm.description,
        creatorId: userId,
        pathType: createForm.pathType,
        difficultyLevel: createForm.difficultyLevel,
        learningObjectives: createForm.learningObjectives,
        tags: createForm.tags
      });

      Alert.alert('成功', '学习路径创建成功！', [
        { text: '确定', onPress: () => {
          setShowCreateModal(false);
          resetCreateForm();
          loadMyPaths();
        }}
      ]);
    } catch (error) {
      console.error('创建路径失败:', error);
      Alert.alert('创建失败', '创建学习路径失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 重置创建表单
  const resetCreateForm = () => {
    setCreateForm({
      name: '',
      description: '',
      pathType: 'PERSONALIZED',
      difficultyLevel: 'BEGINNER',
      learningObjectives: [],
      tags: []
    });
  };

  // 处理刷新
  const handleRefresh = () => {
    setRefreshing(true);
    loadData().finally(() => setRefreshing(false));
  };

  // 渲染标签页
  const renderTabs = () => (
    <View style={[styles.tabContainer, { backgroundColor: colors.surface }]}>
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'my-paths' && { borderBottomColor: colors.primary }
        ]}
        onPress={() => setActiveTab('my-paths')}
        activeOpacity={0.7}
      >
        <Ionicons 
          name="folder-outline" 
          size={20} 
          color={activeTab === 'my-paths' ? colors.primary : colors.textSecondary} 
        />
        <Text style={[
          styles.tabText,
          { color: activeTab === 'my-paths' ? colors.primary : colors.textSecondary }
        ]}>
          我的路径
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'templates' && { borderBottomColor: colors.primary }
        ]}
        onPress={() => setActiveTab('templates')}
        activeOpacity={0.7}
      >
        <Ionicons 
          name="library-outline" 
          size={20} 
          color={activeTab === 'templates' ? colors.primary : colors.textSecondary} 
        />
        <Text style={[
          styles.tabText,
          { color: activeTab === 'templates' ? colors.primary : colors.textSecondary }
        ]}>
          路径模板
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'history' && { borderBottomColor: colors.primary }
        ]}
        onPress={() => setActiveTab('history')}
        activeOpacity={0.7}
      >
        <Ionicons 
          name="time-outline" 
          size={20} 
          color={activeTab === 'history' ? colors.primary : colors.textSecondary} 
        />
        <Text style={[
          styles.tabText,
          { color: activeTab === 'history' ? colors.primary : colors.textSecondary }
        ]}>
          历史记录
        </Text>
      </TouchableOpacity>
    </View>
  );

  // 渲染路径卡片
  const renderPathCard = (path: any, type: 'path' | 'template' | 'history') => (
    <TouchableOpacity
      key={path.id}
      style={[styles.pathCard, { backgroundColor: colors.surface }]}
      activeOpacity={0.7}
    >
      <View style={styles.pathHeader}>
        <View style={styles.pathInfo}>
          <Text style={[styles.pathTitle, { color: colors.text }]}>
            {path.name || path.title}
          </Text>
          <Text style={[styles.pathDescription, { color: colors.textSecondary }]}>
            {path.description}
          </Text>
        </View>
        <View style={styles.pathActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primaryContainer }]}
            activeOpacity={0.7}
          >
            <Ionicons name="play-outline" size={16} color={colors.onPrimaryContainer} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.surfaceVariant }]}
            activeOpacity={0.7}
          >
            <Ionicons name="ellipsis-horizontal" size={16} color={colors.onSurfaceVariant} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.pathMeta}>
        <View style={styles.pathStats}>
          <View style={styles.statItem}>
            <Ionicons name="time-outline" size={14} color={colors.textTertiary} />
            <Text style={[styles.statText, { color: colors.textTertiary }]}>
              {path.estimatedHours || 0}小时
            </Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="book-outline" size={14} color={colors.textTertiary} />
            <Text style={[styles.statText, { color: colors.textTertiary }]}>
              {path.stepCount || 0}步骤
            </Text>
          </View>
          {path.difficultyLevel && (
            <View style={[
              styles.difficultyBadge,
              { backgroundColor: getDifficultyColor(path.difficultyLevel) }
            ]}>
              <Text style={[styles.difficultyText, { color: colors.surface }]}>
                {getDifficultyLabel(path.difficultyLevel)}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  // 获取难度颜色
  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'BEGINNER': return '#4CAF50';
      case 'INTERMEDIATE': return '#FF9800';
      case 'ADVANCED': return '#F44336';
      default: return colors.primary;
    }
  };

  // 获取难度标签
  const getDifficultyLabel = (level: string) => {
    switch (level) {
      case 'BEGINNER': return '初级';
      case 'INTERMEDIATE': return '中级';
      case 'ADVANCED': return '高级';
      default: return '未知';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* 头部 */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          路径管理
        </Text>
        <TouchableOpacity
          style={[styles.createButton, { backgroundColor: colors.primary }]}
          onPress={() => setShowCreateModal(true)}
          activeOpacity={0.7}
        >
          <Ionicons name="add" size={20} color={colors.onPrimary} />
          <Text style={[styles.createButtonText, { color: colors.onPrimary }]}>
            创建路径
          </Text>
        </TouchableOpacity>
      </View>

      {/* 标签页 */}
      {renderTabs()}

      {/* 内容区域 */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {activeTab === 'my-paths' && (
          <View style={styles.pathList}>
            {myPaths.length > 0 ? (
              myPaths.map(path => renderPathCard(path, 'path'))
            ) : (
              <View style={styles.emptyState}>
                <Ionicons name="folder-open-outline" size={64} color={colors.textTertiary} />
                <Text style={[styles.emptyTitle, { color: colors.textSecondary }]}>
                  还没有创建路径
                </Text>
                <Text style={[styles.emptyDescription, { color: colors.textTertiary }]}>
                  点击右上角"创建路径"开始制定你的学习计划
                </Text>
              </View>
            )}
          </View>
        )}

        {activeTab === 'templates' && (
          <View style={styles.pathList}>
            {templates.length > 0 ? (
              templates.map(template => renderPathCard(template, 'template'))
            ) : (
              <View style={styles.emptyState}>
                <Ionicons name="library-outline" size={64} color={colors.textTertiary} />
                <Text style={[styles.emptyTitle, { color: colors.textSecondary }]}>
                  暂无路径模板
                </Text>
                <Text style={[styles.emptyDescription, { color: colors.textTertiary }]}>
                  路径模板正在准备中，敬请期待
                </Text>
              </View>
            )}
          </View>
        )}

        {activeTab === 'history' && (
          <View style={styles.pathList}>
            {pathHistory.length > 0 ? (
              pathHistory.map(history => renderPathCard(history, 'history'))
            ) : (
              <View style={styles.emptyState}>
                <Ionicons name="time-outline" size={64} color={colors.textTertiary} />
                <Text style={[styles.emptyTitle, { color: colors.textSecondary }]}>
                  暂无历史记录
                </Text>
                <Text style={[styles.emptyDescription, { color: colors.textTertiary }]}>
                  开始学习后，这里会显示你的学习历史
                </Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>

      {/* 创建路径模态框 */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: colors.surface }]}>
            <TouchableOpacity
              onPress={() => setShowCreateModal(false)}
              activeOpacity={0.7}
            >
              <Text style={[styles.modalCancelText, { color: colors.textSecondary }]}>
                取消
              </Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              创建学习路径
            </Text>
            <TouchableOpacity
              onPress={createNewPath}
              disabled={loading}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.modalSaveText,
                { color: loading ? colors.textTertiary : colors.primary }
              ]}>
                {loading ? '创建中...' : '创建'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {/* 路径名称 */}
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                路径名称 *
              </Text>
              <TextInput
                style={[styles.formInput, {
                  backgroundColor: colors.surface,
                  color: colors.text,
                  borderColor: colors.outline
                }]}
                placeholder="请输入路径名称"
                placeholderTextColor={colors.textTertiary}
                value={createForm.name}
                onChangeText={(text) => setCreateForm(prev => ({ ...prev, name: text }))}
                maxLength={50}
              />
            </View>

            {/* 路径描述 */}
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                路径描述
              </Text>
              <TextInput
                style={[styles.formTextArea, {
                  backgroundColor: colors.surface,
                  color: colors.text,
                  borderColor: colors.outline
                }]}
                placeholder="请描述这个学习路径的目标和内容"
                placeholderTextColor={colors.textTertiary}
                value={createForm.description}
                onChangeText={(text) => setCreateForm(prev => ({ ...prev, description: text }))}
                multiline
                numberOfLines={4}
                maxLength={200}
              />
            </View>

            {/* 路径类型 */}
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                路径类型
              </Text>
              <View style={styles.optionGroup}>
                {[
                  { value: 'PERSONALIZED', label: '个性化路径' },
                  { value: 'CUSTOM', label: '自定义路径' },
                  { value: 'TEMPLATE', label: '模板路径' }
                ].map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.optionItem,
                      {
                        backgroundColor: createForm.pathType === option.value
                          ? colors.primaryContainer
                          : colors.surface,
                        borderColor: createForm.pathType === option.value
                          ? colors.primary
                          : colors.outline
                      }
                    ]}
                    onPress={() => setCreateForm(prev => ({
                      ...prev,
                      pathType: option.value as any
                    }))}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.optionText,
                      {
                        color: createForm.pathType === option.value
                          ? colors.onPrimaryContainer
                          : colors.text
                      }
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* 难度级别 */}
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                难度级别
              </Text>
              <View style={styles.optionGroup}>
                {[
                  { value: 'BEGINNER', label: '初级', color: '#4CAF50' },
                  { value: 'INTERMEDIATE', label: '中级', color: '#FF9800' },
                  { value: 'ADVANCED', label: '高级', color: '#F44336' }
                ].map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.optionItem,
                      {
                        backgroundColor: createForm.difficultyLevel === option.value
                          ? option.color + '20'
                          : colors.surface,
                        borderColor: createForm.difficultyLevel === option.value
                          ? option.color
                          : colors.outline
                      }
                    ]}
                    onPress={() => setCreateForm(prev => ({
                      ...prev,
                      difficultyLevel: option.value as any
                    }))}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.optionText,
                      {
                        color: createForm.difficultyLevel === option.value
                          ? option.color
                          : colors.text
                      }
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing('lg'),
    paddingVertical: tokens.spacing('md'),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerTitle: {
    fontSize: tokens.fontSize('title-lg'),
    fontWeight: tokens.fontWeight('bold'),
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing('md'),
    paddingVertical: tokens.spacing('sm'),
    borderRadius: tokens.radius('md'),
  },
  createButtonText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
    marginLeft: tokens.spacing('xs'),
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: tokens.spacing('md'),
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: tokens.fontSize('sm'),
    fontWeight: tokens.fontWeight('medium'),
    marginLeft: tokens.spacing('xs'),
  },
  content: {
    flex: 1,
  },
  pathList: {
    padding: tokens.spacing('md'),
  },
  pathCard: {
    borderRadius: tokens.radius('md'),
    padding: tokens.spacing('lg'),
    marginBottom: tokens.spacing('md'),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  pathHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: tokens.spacing('md'),
  },
  pathInfo: {
    flex: 1,
    marginRight: tokens.spacing('md'),
  },
  pathTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('bold'),
    marginBottom: tokens.spacing('xs'),
  },
  pathDescription: {
    fontSize: tokens.fontSize('sm'),
    lineHeight: 20,
  },
  pathActions: {
    flexDirection: 'row',
    gap: tokens.spacing('xs'),
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: tokens.radius('sm'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  pathMeta: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    paddingTop: tokens.spacing('md'),
  },
  pathStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing('md'),
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing('xs'),
  },
  statText: {
    fontSize: tokens.fontSize('xs'),
  },
  difficultyBadge: {
    paddingHorizontal: tokens.spacing('sm'),
    paddingVertical: tokens.spacing('xs'),
    borderRadius: tokens.radius('sm'),
    marginLeft: 'auto',
  },
  difficultyText: {
    fontSize: tokens.fontSize('xs'),
    fontWeight: tokens.fontWeight('medium'),
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: tokens.spacing('xl') * 2,
  },
  emptyTitle: {
    fontSize: tokens.fontSize('title-sm'),
    fontWeight: tokens.fontWeight('medium'),
    marginTop: tokens.spacing('lg'),
    marginBottom: tokens.spacing('sm'),
  },
  emptyDescription: {
    fontSize: tokens.fontSize('sm'),
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default PathManagementScreen;
