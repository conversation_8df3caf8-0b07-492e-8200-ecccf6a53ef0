-- =====================================================
-- ITBook项目 - UserCareerGoal废弃字段清理迁移脚本
-- 
-- 目标：清理user_career_goal表中的废弃字段
-- 1. 删除target_job_id字段及其相关约束
-- 2. 删除target_level枚举字段
-- 3. 确保career_goal_id和career_level_id字段正常工作
-- 
-- 作者：ITBook Team
-- 日期：2025-07-23
-- 版本：V2025072301
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 第一步：记录迁移开始
-- =====================================================

-- 创建迁移日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `migration_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `migration_name` varchar(255) NOT NULL,
  `description` text,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('STARTED','COMPLETED','FAILED','ROLLBACK') NOT NULL DEFAULT 'STARTED',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_migration_name` (`migration_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 记录迁移开始时间
INSERT INTO migration_log (migration_name, description, start_time, status) 
VALUES ('V2025072301_cleanup_user_career_goal_deprecated_fields', 
        '清理user_career_goal表中的废弃字段：target_job_id和target_level', 
        NOW(), 'STARTED')
ON DUPLICATE KEY UPDATE 
    start_time = NOW(), 
    status = 'STARTED',
    end_time = NULL;

-- =====================================================
-- 第二步：数据备份
-- =====================================================

-- 创建备份表（如果不存在）
CREATE TABLE IF NOT EXISTS `user_career_goal_cleanup_backup` AS 
SELECT * FROM `user_career_goal` LIMIT 0;

-- 备份当前数据
INSERT INTO `user_career_goal_cleanup_backup` 
SELECT * FROM `user_career_goal`;

-- =====================================================
-- 第三步：验证前置条件
-- =====================================================

-- 检查career_goal_id字段是否存在且有数据
SELECT 
    'career_goal_id字段检查' as check_type,
    COUNT(*) as total_records,
    COUNT(career_goal_id) as records_with_career_goal_id,
    COUNT(*) - COUNT(career_goal_id) as null_career_goal_id_count
FROM user_career_goal;

-- 检查career_level_id字段是否存在且有数据
SELECT 
    'career_level_id字段检查' as check_type,
    COUNT(*) as total_records,
    COUNT(career_level_id) as records_with_career_level_id,
    COUNT(*) - COUNT(career_level_id) as null_career_level_id_count
FROM user_career_goal;

-- =====================================================
-- 第四步：删除废弃字段的外键约束
-- =====================================================

-- 删除target_job_id的外键约束（如果存在）
SET @constraint_exists = (
    SELECT COUNT(*) 
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'user_career_goal' 
    AND CONSTRAINT_NAME = 'fk_user_career_goal_job'
);

SET @sql = IF(@constraint_exists > 0, 
    'ALTER TABLE `user_career_goal` DROP FOREIGN KEY `fk_user_career_goal_job`',
    'SELECT "外键约束fk_user_career_goal_job不存在，跳过删除" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第五步：删除废弃字段的索引
-- =====================================================

-- 删除target_job_id的索引（如果存在）
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'user_career_goal' 
    AND INDEX_NAME = 'idx_target_job_id'
);

SET @sql = IF(@index_exists > 0, 
    'ALTER TABLE `user_career_goal` DROP INDEX `idx_target_job_id`',
    'SELECT "索引idx_target_job_id不存在，跳过删除" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第六步：删除废弃字段
-- =====================================================

-- 删除target_job_id字段（如果存在）
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'user_career_goal' 
    AND COLUMN_NAME = 'target_job_id'
);

SET @sql = IF(@column_exists > 0, 
    'ALTER TABLE `user_career_goal` DROP COLUMN `target_job_id`',
    'SELECT "字段target_job_id不存在，跳过删除" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除target_level字段（如果存在）
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'user_career_goal' 
    AND COLUMN_NAME = 'target_level'
);

SET @sql = IF(@column_exists > 0, 
    'ALTER TABLE `user_career_goal` DROP COLUMN `target_level`',
    'SELECT "字段target_level不存在，跳过删除" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第七步：确保新字段的约束和索引存在
-- =====================================================

-- 确保career_goal_id字段有索引
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'user_career_goal' 
    AND INDEX_NAME = 'idx_career_goal_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE `user_career_goal` ADD INDEX `idx_career_goal_id` (`career_goal_id`)',
    'SELECT "索引idx_career_goal_id已存在，跳过创建" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保career_level_id字段有索引
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'user_career_goal' 
    AND INDEX_NAME = 'idx_career_level_id'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE `user_career_goal` ADD INDEX `idx_career_level_id` (`career_level_id`)',
    'SELECT "索引idx_career_level_id已存在，跳过创建" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第八步：验证迁移结果
-- =====================================================

-- 检查表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_career_goal'
ORDER BY ORDINAL_POSITION;

-- 检查索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_career_goal'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 检查外键约束
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_career_goal' 
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 检查数据完整性
SELECT 
    '数据完整性检查' as check_type,
    COUNT(*) as total_records,
    COUNT(career_goal_id) as records_with_career_goal_id,
    COUNT(career_level_id) as records_with_career_level_id
FROM user_career_goal;

-- =====================================================
-- 第九步：记录迁移完成
-- =====================================================

-- 更新迁移日志
UPDATE migration_log 
SET end_time = NOW(), 
    status = 'COMPLETED',
    description = CONCAT(description, ' - 废弃字段已成功删除，新字段约束已确认')
WHERE migration_name = 'V2025072301_cleanup_user_career_goal_deprecated_fields';

-- 提交事务
COMMIT;

-- =====================================================
-- 迁移说明
-- =====================================================

/*
本迁移脚本完成以下任务：

1. 数据备份：创建user_career_goal_cleanup_backup表
2. 删除废弃字段：target_job_id和target_level
3. 删除相关约束：外键约束和索引
4. 确保新字段：career_goal_id和career_level_id的约束和索引
5. 数据验证：验证迁移后的数据完整性

清理的字段：
- target_job_id：已被career_goal_id替代
- target_level：已被career_level_id替代

保留的字段：
- career_goal_id：关联到career_goal表
- career_level_id：关联到career_level表

注意事项：
- 执行前确保已运行career-level-association-migration.sql
- 确保Java实体类已更新，移除废弃方法
- 确保前端代码已更新，使用新的字段名
- 建议在测试环境充分验证后再在生产环境执行

后续步骤：
1. 验证前后端联调正常
2. 清理备份表（如果确认无问题）
3. 更新相关文档
*/
