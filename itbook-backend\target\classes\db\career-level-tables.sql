-- 职业级别配置表
CREATE TABLE career_level (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    career_goal_id BIGINT NOT NULL COMMENT '职业目标ID',
    level_code VARCHAR(50) NOT NULL COMMENT '级别代码',
    level_name VARCHAR(100) NOT NULL COMMENT '级别名称',
    description VARCHAR(500) COMMENT '级别描述',
    min_experience_years INT DEFAULT 0 COMMENT '最小经验年限',
    max_experience_years INT DEFAULT NULL COMMENT '最大经验年限',
    salary_range_min DECIMAL(10,2) COMMENT '薪资范围最小值',
    salary_range_max DECIMAL(10,2) COMMENT '薪资范围最大值',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (career_goal_id) REFERENCES career_goal(id) ON DELETE CASCADE,
    UNIQUE KEY uk_career_goal_level (career_goal_id, level_code),
    INDEX idx_career_goal_id (career_goal_id),
    INDEX idx_level_code (level_code),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) COMMENT='职业级别配置表';

-- 初始化职业级别数据
-- 为Java后端工程师（career_goal_id=1）创建级别
INSERT INTO career_level (career_goal_id, level_code, level_name, description, min_experience_years, max_experience_years, salary_range_min, salary_range_max, sort_order) VALUES
(1, 'junior', '初级工程师', '0-2年经验，掌握基础技能', 0, 2, 8000, 15000, 1),
(1, 'mid', '中级工程师', '2-5年经验，具备独立开发能力', 2, 5, 15000, 25000, 2),
(1, 'senior', '高级工程师', '5-8年经验，具备架构设计能力', 5, 8, 25000, 40000, 3),
(1, 'expert', '专家级工程师', '8-12年经验，技术专家', 8, 12, 40000, 60000, 4),
(1, 'architect', '架构师', '10年以上经验，系统架构师', 10, NULL, 60000, 100000, 5);

-- 为前端开发工程师（career_goal_id=2，如果存在）创建级别
INSERT INTO career_level (career_goal_id, level_code, level_name, description, min_experience_years, max_experience_years, salary_range_min, salary_range_max, sort_order) VALUES
(2, 'junior', '初级前端工程师', '0-2年经验，掌握基础前端技能', 0, 2, 7000, 14000, 1),
(2, 'mid', '中级前端工程师', '2-5年经验，具备独立前端开发能力', 2, 5, 14000, 23000, 2),
(2, 'senior', '高级前端工程师', '5-8年经验，具备前端架构设计能力', 5, 8, 23000, 38000, 3),
(2, 'expert', '前端专家', '8-12年经验，前端技术专家', 8, 12, 38000, 55000, 4),
(2, 'architect', '前端架构师', '10年以上经验，前端架构师', 10, NULL, 55000, 90000, 5);
