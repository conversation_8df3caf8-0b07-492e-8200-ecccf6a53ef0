-- =====================================================
-- ITBook项目 - Career/Job概念统一数据库迁移脚本
-- 
-- 目标：将UserCareerGoal表中的Job概念统一为CareerGoal概念
-- 作者：ITBook Team
-- 日期：2025-07-13
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 第一步：备份现有数据
-- =====================================================

-- 创建备份表
CREATE TABLE `user_career_goal_backup` AS SELECT * FROM `user_career_goal`;

-- 记录迁移开始时间
INSERT INTO migration_log (migration_name, start_time, status) 
VALUES ('career-goal-concept-unification', NOW(), 'STARTED');

-- =====================================================
-- 第二步：删除现有外键约束
-- =====================================================

-- 删除user_career_goal表的外键约束
ALTER TABLE `user_career_goal` DROP FOREIGN KEY `fk_user_career_goal_job`;

-- 删除learning_path表的外键约束（如果存在）
ALTER TABLE `learning_path` DROP FOREIGN KEY IF EXISTS `FKggcv077g6vwxpn3xd4rmcgyr0`;

-- =====================================================
-- 第三步：删除现有索引
-- =====================================================

-- 删除target_job_id相关索引
ALTER TABLE `user_career_goal` DROP INDEX `idx_target_job_id`;
ALTER TABLE `learning_path` DROP INDEX IF EXISTS `FKggcv077g6vwxpn3xd4rmcgyr0`;

-- =====================================================
-- 第四步：重命名字段
-- =====================================================

-- 重命名user_career_goal表的字段
ALTER TABLE `user_career_goal` 
CHANGE COLUMN `target_job_id` `career_goal_id` BIGINT(20) NULL DEFAULT NULL COMMENT '职业目标ID';

-- 重命名learning_path表的字段（如果存在）
ALTER TABLE `learning_path` 
CHANGE COLUMN `target_job_id` `career_goal_id` BIGINT(20) NULL DEFAULT NULL COMMENT '关联的职业目标ID';

-- =====================================================
-- 第五步：创建新的索引
-- =====================================================

-- 为user_career_goal表创建新索引
ALTER TABLE `user_career_goal` 
ADD INDEX `idx_career_goal_id` (`career_goal_id`);

-- 为learning_path表创建新索引（如果需要）
ALTER TABLE `learning_path` 
ADD INDEX `idx_career_goal_id` (`career_goal_id`);

-- =====================================================
-- 第六步：创建新的外键约束
-- =====================================================

-- 为user_career_goal表创建新的外键约束
-- 注意：这里暂时关联到job表，后续需要迁移到career_goal表
ALTER TABLE `user_career_goal` 
ADD CONSTRAINT `fk_user_career_goal_career_goal` 
FOREIGN KEY (`career_goal_id`) REFERENCES `job` (`id`) 
ON DELETE SET NULL ON UPDATE RESTRICT;

-- 为learning_path表创建新的外键约束（如果需要）
ALTER TABLE `learning_path` 
ADD CONSTRAINT `fk_learning_path_career_goal` 
FOREIGN KEY (`career_goal_id`) REFERENCES `job` (`id`) 
ON DELETE SET NULL ON UPDATE RESTRICT;

-- =====================================================
-- 第七步：更新注释和元数据
-- =====================================================

-- 更新表注释
ALTER TABLE `user_career_goal` COMMENT = '用户职业目标表（已统一Career概念）';

-- 更新字段注释
ALTER TABLE `user_career_goal` 
MODIFY COLUMN `career_goal_id` BIGINT(20) NULL DEFAULT NULL COMMENT '职业目标ID（关联career_goal表）';

-- =====================================================
-- 第八步：验证数据完整性
-- =====================================================

-- 检查数据迁移是否成功
SELECT 
    '数据迁移验证' as check_type,
    COUNT(*) as total_records,
    COUNT(career_goal_id) as records_with_career_goal_id,
    COUNT(*) - COUNT(career_goal_id) as null_career_goal_id_count
FROM user_career_goal;

-- 检查外键约束是否正确
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'user_career_goal' 
AND CONSTRAINT_NAME LIKE 'fk_%';

-- =====================================================
-- 第九步：记录迁移完成
-- =====================================================

-- 更新迁移日志
UPDATE migration_log 
SET end_time = NOW(), status = 'COMPLETED' 
WHERE migration_name = 'career-goal-concept-unification';

-- 提交事务
COMMIT;

-- =====================================================
-- 回滚脚本（如果需要）
-- =====================================================

/*
-- 如果需要回滚，执行以下脚本：

START TRANSACTION;

-- 删除新的外键约束
ALTER TABLE `user_career_goal` DROP FOREIGN KEY `fk_user_career_goal_career_goal`;
ALTER TABLE `learning_path` DROP FOREIGN KEY IF EXISTS `fk_learning_path_career_goal`;

-- 删除新的索引
ALTER TABLE `user_career_goal` DROP INDEX `idx_career_goal_id`;
ALTER TABLE `learning_path` DROP INDEX IF EXISTS `idx_career_goal_id`;

-- 恢复原字段名
ALTER TABLE `user_career_goal` 
CHANGE COLUMN `career_goal_id` `target_job_id` BIGINT(20) NULL DEFAULT NULL COMMENT '目标岗位ID';

ALTER TABLE `learning_path` 
CHANGE COLUMN `career_goal_id` `target_job_id` BIGINT(20) NULL DEFAULT NULL COMMENT '目标岗位ID';

-- 恢复原索引
ALTER TABLE `user_career_goal` ADD INDEX `idx_target_job_id` (`target_job_id`);

-- 恢复原外键约束
ALTER TABLE `user_career_goal` 
ADD CONSTRAINT `fk_user_career_goal_job` 
FOREIGN KEY (`target_job_id`) REFERENCES `job` (`id`) 
ON DELETE SET NULL ON UPDATE RESTRICT;

-- 更新迁移日志
UPDATE migration_log 
SET end_time = NOW(), status = 'ROLLBACK' 
WHERE migration_name = 'career-goal-concept-unification';

COMMIT;
*/

-- =====================================================
-- 迁移说明
-- =====================================================

/*
本迁移脚本完成以下任务：

1. 数据备份：创建user_career_goal_backup表备份原始数据
2. 字段重命名：target_job_id → career_goal_id
3. 索引更新：重建相关索引
4. 外键约束更新：更新外键约束名称和注释
5. 数据验证：验证迁移后的数据完整性

注意事项：
- 本脚本只完成字段重命名，实际的Job→CareerGoal实体关联需要后续处理
- 外键约束暂时还是指向job表，需要在CareerGoal表完善后再次迁移
- 建议在测试环境充分验证后再在生产环境执行
- 执行前请确保数据库备份完整

后续步骤：
1. 完善CareerGoal表结构
2. 迁移Job数据到CareerGoal表
3. 更新外键约束指向CareerGoal表
4. 更新应用代码中的实体关联
*/
