/**
 * 个人简介编辑表单组件
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';

interface SummaryEditFormProps {
  summary?: string;
  onSave: (summary: string) => void;
  onCancel: () => void;
}

export const SummaryEditForm: React.FC<SummaryEditFormProps> = ({
  summary,
  onSave,
  onCancel,
}) => {
  const colors = useThemeColors();

  // 表单数据状态
  const [summaryText, setSummaryText] = useState(summary || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // 字符计数
  const maxLength = 500;
  const currentLength = summaryText.length;

  // 表单验证
  const validateForm = (): boolean => {
    if (!summaryText.trim()) {
      setError('请输入个人简介');
      return false;
    }

    if (summaryText.length > maxLength) {
      setError(`个人简介不能超过${maxLength}个字符`);
      return false;
    }

    setError('');
    return true;
  };

  // 处理文本变更
  const handleTextChange = (text: string) => {
    setSummaryText(text);
    if (error) {
      setError('');
    }
  };

  // 处理保存
  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('提示', '请检查表单中的错误信息');
      return;
    }

    try {
      setLoading(true);
      onSave(summaryText.trim());
    } catch (error) {
      console.error('保存个人简介失败:', error);
      Alert.alert('错误', '保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取字符计数颜色
  const getCharCountColor = () => {
    if (currentLength > maxLength) {
      return colors.error;
    } else if (currentLength > maxLength * 0.8) {
      return colors.warning || colors.textSecondary;
    }
    return colors.textSecondary;
  };

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background,
    }}>
      {/* 头部 */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: tokens.spacing('lg'),
        paddingVertical: tokens.spacing('md'),
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
        backgroundColor: colors.surface,
      }}>
        <TouchableOpacity
          onPress={onCancel}
          activeOpacity={0.7}
        >
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>

        <Text style={{
          fontSize: tokens.fontSize('title-sm'),
          fontWeight: tokens.fontWeight('bold') as any,
          color: colors.text,
        }}>
          编辑个人简介
        </Text>

        <TouchableOpacity
          onPress={handleSave}
          activeOpacity={0.7}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Text style={{
              fontSize: tokens.fontSize('body'),
              color: colors.primary,
              fontWeight: tokens.fontWeight('medium') as any,
            }}>
              保存
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* 表单内容 */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          padding: tokens.spacing('lg'),
        }}
        showsVerticalScrollIndicator={false}
      >
        {/* 说明文字 */}
        <Text style={{
          fontSize: tokens.fontSize('body'),
          color: colors.textSecondary,
          marginBottom: tokens.spacing('lg'),
          lineHeight: 20,
        }}>
          个人简介是您简历的重要组成部分，请简要描述您的专业背景、核心技能和职业目标。
        </Text>

        {/* 输入框标题 */}
        <Text style={{
          fontSize: tokens.fontSize('body'),
          fontWeight: tokens.fontWeight('medium') as any,
          color: colors.text,
          marginBottom: tokens.spacing('sm'),
        }}>
          个人简介<Text style={{ color: colors.error }}>*</Text>
        </Text>

        {/* 多行文本输入框 */}
        <TextInput
          style={{
            borderWidth: 1,
            borderColor: error ? colors.error : colors.border,
            borderRadius: tokens.radius('sm'),
            padding: tokens.spacing('md'),
            fontSize: tokens.fontSize('body'),
            color: colors.text,
            backgroundColor: colors.surface,
            minHeight: 120,
            maxHeight: 200,
            textAlignVertical: 'top',
          }}
          value={summaryText}
          onChangeText={handleTextChange}
          placeholder="请输入您的个人简介，包括专业背景、核心技能、工作经验和职业目标等..."
          placeholderTextColor={colors.textSecondary}
          multiline={true}
          numberOfLines={6}
          maxLength={maxLength + 50} // 允许稍微超出以便显示错误
        />

        {/* 字符计数和错误信息 */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: tokens.spacing('sm'),
        }}>
          <Text style={{
            fontSize: tokens.fontSize('caption'),
            color: colors.error,
          }}>
            {error}
          </Text>
          
          <Text style={{
            fontSize: tokens.fontSize('caption'),
            color: getCharCountColor(),
            fontWeight: tokens.fontWeight('medium') as any,
          }}>
            {currentLength}/{maxLength}
          </Text>
        </View>

        {/* 写作提示 */}
        <View style={{
          backgroundColor: colors.surface,
          borderRadius: tokens.radius('sm'),
          padding: tokens.spacing('md'),
          marginTop: tokens.spacing('lg'),
          borderLeftWidth: 3,
          borderLeftColor: colors.primary,
        }}>
          <Text style={{
            fontSize: tokens.fontSize('body'),
            fontWeight: tokens.fontWeight('semibold') as any,
            color: colors.text,
            marginBottom: tokens.spacing('sm'),
          }}>
            💡 写作建议
          </Text>
          
          <Text style={{
            fontSize: tokens.fontSize('caption'),
            color: colors.textSecondary,
            lineHeight: 18,
          }}>
            • 突出您的核心技能和专业优势{'\n'}
            • 简要描述相关工作经验和成就{'\n'}
            • 表达您的职业目标和发展方向{'\n'}
            • 保持简洁明了，避免冗长描述{'\n'}
            • 使用积极正面的语言表达
          </Text>
        </View>

        {/* 示例模板 */}
        <View style={{
          backgroundColor: colors.surface,
          borderRadius: tokens.radius('sm'),
          padding: tokens.spacing('md'),
          marginTop: tokens.spacing('md'),
          borderLeftWidth: 3,
          borderLeftColor: colors.secondary || colors.primary,
        }}>
          <Text style={{
            fontSize: tokens.fontSize('body'),
            fontWeight: tokens.fontWeight('semibold') as any,
            color: colors.text,
            marginBottom: tokens.spacing('sm'),
          }}>
            📝 参考模板
          </Text>
          
          <Text style={{
            fontSize: tokens.fontSize('caption'),
            color: colors.textSecondary,
            lineHeight: 18,
            fontStyle: 'italic',
          }}>
            "具有X年[专业领域]经验，熟练掌握[核心技能]，擅长[专业特长]。曾在[公司类型]担任[职位]，负责[主要工作]，取得了[具体成就]。希望在[目标领域]继续发展，为团队和公司创造更大价值。"
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};
