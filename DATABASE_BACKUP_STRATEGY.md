# ITBook原子技能系统数据备份和恢复策略

## 🎯 备份策略概述

### **备份目标**
- **数据安全**：确保原子技能系统数据不丢失
- **业务连续性**：最小化系统停机时间
- **快速恢复**：在故障时能够快速恢复服务
- **版本管理**：支持数据版本回滚

### **备份范围**
- ✅ 原子技能库数据 (atomic_skill)
- ✅ 技能关系图谱 (skill_relationship)
- ✅ 用户掌握度数据 (user_atomic_skill_mastery)
- ✅ 动态学习路径 (dynamic_learning_path, dynamic_path_step)
- ✅ 技能映射关系 (career_skill_mapping)
- ✅ 评估记录 (skill_assessment_record)
- ✅ 现有业务数据 (user, career_goal, learning_path等)

## 📋 备份策略设计

### **1. 全量备份策略**
```bash
# 每日全量备份 (凌晨2:00执行)
# 备份整个itbook_dev数据库
mysqldump -u root -pNW1M5@18N1YYzNlNz \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --hex-blob \
  --default-character-set=utf8mb4 \
  itbook_dev > /backup/full/itbook_dev_$(date +%Y%m%d_%H%M%S).sql

# 压缩备份文件
gzip /backup/full/itbook_dev_$(date +%Y%m%d_%H%M%S).sql
```

### **2. 增量备份策略**
```bash
# 每小时增量备份 (基于binlog)
# 启用MySQL binlog
# 在my.cnf中配置：
# log-bin=mysql-bin
# binlog-format=ROW
# expire_logs_days=7

# 增量备份脚本
mysqlbinlog --start-datetime="$(date -d '1 hour ago' '+%Y-%m-%d %H:00:00')" \
  --stop-datetime="$(date '+%Y-%m-%d %H:00:00')" \
  /var/lib/mysql/mysql-bin.* > /backup/incremental/binlog_$(date +%Y%m%d_%H).sql
```

### **3. 关键表实时备份**
```bash
# 原子技能系统核心表的实时备份
# 每30分钟执行一次
TABLES="atomic_skill skill_relationship user_atomic_skill_mastery dynamic_learning_path dynamic_path_step career_skill_mapping skill_assessment_record"

for table in $TABLES; do
  mysqldump -u root -pNW1M5@18N1YYzNlNz \
    --single-transaction \
    --where="updated_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)" \
    itbook_dev $table > /backup/realtime/${table}_$(date +%Y%m%d_%H%M).sql
done
```

## 🔄 恢复策略设计

### **1. 完全恢复流程**
```bash
# 步骤1: 停止应用服务
systemctl stop itbook-backend

# 步骤2: 创建恢复数据库
mysql -u root -pNW1M5@18N1YYzNlNz -e "CREATE DATABASE itbook_recovery;"

# 步骤3: 恢复全量备份
gunzip -c /backup/full/itbook_dev_20250721_020000.sql.gz | \
  mysql -u root -pNW1M5@18N1YYzNlNz itbook_recovery

# 步骤4: 应用增量备份
for binlog in /backup/incremental/binlog_*.sql; do
  mysql -u root -pNW1M5@18N1YYzNlNz itbook_recovery < $binlog
done

# 步骤5: 验证数据完整性
mysql -u root -pNW1M5@18N1YYzNlNz itbook_recovery -e "
  SELECT COUNT(*) as atomic_skills FROM atomic_skill;
  SELECT COUNT(*) as relationships FROM skill_relationship;
  SELECT COUNT(*) as user_masteries FROM user_atomic_skill_mastery;
"

# 步骤6: 切换数据库
mysql -u root -pNW1M5@18N1YYzNlNz -e "
  RENAME TABLE itbook_dev.atomic_skill TO itbook_dev.atomic_skill_backup;
  RENAME TABLE itbook_recovery.atomic_skill TO itbook_dev.atomic_skill;
  -- 对所有表执行类似操作
"

# 步骤7: 重启应用服务
systemctl start itbook-backend
```

### **2. 点对点恢复流程**
```bash
# 恢复到特定时间点 (例如：2025-07-21 14:30:00)
TARGET_TIME="2025-07-21 14:30:00"

# 步骤1: 恢复最近的全量备份
gunzip -c /backup/full/itbook_dev_20250721_020000.sql.gz | \
  mysql -u root -pNW1M5@18N1YYzNlNz itbook_recovery

# 步骤2: 应用binlog到目标时间点
mysqlbinlog --stop-datetime="$TARGET_TIME" \
  /var/lib/mysql/mysql-bin.* | \
  mysql -u root -pNW1M5@18N1YYzNlNz itbook_recovery
```

### **3. 单表恢复流程**
```bash
# 恢复单个表 (例如：atomic_skill表)
TABLE_NAME="atomic_skill"

# 从最新备份中提取单表
mysqldump -u root -pNW1M5@18N1YYzNlNz \
  --single-transaction \
  itbook_dev $TABLE_NAME > /tmp/${TABLE_NAME}_recovery.sql

# 恢复到临时表
mysql -u root -pNW1M5@18N1YYzNlNz itbook_dev -e "
  CREATE TABLE ${TABLE_NAME}_temp LIKE $TABLE_NAME;
"

mysql -u root -pNW1M5@18N1YYzNlNz itbook_dev < /tmp/${TABLE_NAME}_recovery.sql

# 验证后替换原表
mysql -u root -pNW1M5@18N1YYzNlNz itbook_dev -e "
  RENAME TABLE $TABLE_NAME TO ${TABLE_NAME}_backup,
               ${TABLE_NAME}_temp TO $TABLE_NAME;
"
```

## 📅 备份计划表

### **自动化备份时间表**
```
每日 02:00 - 全量备份
每小时 :00 - 增量备份 (binlog)
每30分钟 - 核心表实时备份
每周日 04:00 - 备份文件清理 (保留30天)
每月1日 06:00 - 备份验证测试
```

### **备份保留策略**
- **全量备份**：保留30天
- **增量备份**：保留7天
- **实时备份**：保留24小时
- **月度归档**：保留12个月

## 🔧 备份脚本实现

### **主备份脚本**
```bash
#!/bin/bash
# /scripts/backup_itbook.sh

# 配置变量
DB_USER="root"
DB_PASS="NW1M5@18N1YYzNlNz"
DB_NAME="itbook_dev"
BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR/{full,incremental,realtime}

# 全量备份函数
full_backup() {
    echo "开始全量备份: $(date)"
    mysqldump -u $DB_USER -p$DB_PASS \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        $DB_NAME > $BACKUP_DIR/full/${DB_NAME}_${DATE}.sql
    
    if [ $? -eq 0 ]; then
        gzip $BACKUP_DIR/full/${DB_NAME}_${DATE}.sql
        echo "全量备份完成: ${DB_NAME}_${DATE}.sql.gz"
    else
        echo "全量备份失败!"
        exit 1
    fi
}

# 核心表备份函数
core_tables_backup() {
    echo "开始核心表备份: $(date)"
    TABLES="atomic_skill skill_relationship user_atomic_skill_mastery dynamic_learning_path dynamic_path_step career_skill_mapping skill_assessment_record"
    
    for table in $TABLES; do
        mysqldump -u $DB_USER -p$DB_PASS \
            --single-transaction \
            $DB_NAME $table > $BACKUP_DIR/realtime/${table}_${DATE}.sql
        
        if [ $? -eq 0 ]; then
            echo "表 $table 备份完成"
        else
            echo "表 $table 备份失败!"
        fi
    done
}

# 清理旧备份函数
cleanup_old_backups() {
    echo "清理旧备份文件: $(date)"
    # 删除30天前的全量备份
    find $BACKUP_DIR/full -name "*.sql.gz" -mtime +30 -delete
    # 删除7天前的增量备份
    find $BACKUP_DIR/incremental -name "*.sql" -mtime +7 -delete
    # 删除24小时前的实时备份
    find $BACKUP_DIR/realtime -name "*.sql" -mtime +1 -delete
}

# 备份验证函数
verify_backup() {
    BACKUP_FILE=$1
    echo "验证备份文件: $BACKUP_FILE"
    
    if [ -f "$BACKUP_FILE" ]; then
        # 检查文件大小
        SIZE=$(stat -f%z "$BACKUP_FILE" 2>/dev/null || stat -c%s "$BACKUP_FILE" 2>/dev/null)
        if [ $SIZE -gt 1000 ]; then
            echo "备份文件验证通过: ${SIZE} bytes"
            return 0
        else
            echo "备份文件过小，可能损坏: ${SIZE} bytes"
            return 1
        fi
    else
        echo "备份文件不存在: $BACKUP_FILE"
        return 1
    fi
}

# 主执行逻辑
case "$1" in
    "full")
        full_backup
        ;;
    "core")
        core_tables_backup
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    "verify")
        verify_backup "$2"
        ;;
    *)
        echo "用法: $0 {full|core|cleanup|verify <file>}"
        exit 1
        ;;
esac
```

## 📊 监控和告警

### **备份状态监控**
```bash
# 备份状态检查脚本
#!/bin/bash
# /scripts/backup_monitor.sh

# 检查最近24小时内是否有成功的全量备份
LATEST_BACKUP=$(find /backup/full -name "*.sql.gz" -mtime -1 | head -1)
if [ -z "$LATEST_BACKUP" ]; then
    echo "警告: 24小时内没有全量备份!"
    # 发送告警邮件或通知
fi

# 检查备份文件完整性
for backup in $(find /backup/full -name "*.sql.gz" -mtime -7); do
    if ! gzip -t "$backup"; then
        echo "错误: 备份文件损坏 - $backup"
    fi
done
```

## 🚨 应急恢复预案

### **紧急情况处理流程**
1. **数据丢失检测** → 立即停止写入操作
2. **评估损失范围** → 确定需要恢复的数据范围
3. **选择恢复策略** → 全量恢复 vs 增量恢复
4. **执行恢复操作** → 按照恢复流程执行
5. **数据验证** → 确保恢复数据的完整性
6. **服务恢复** → 重启应用服务
7. **事后分析** → 分析故障原因，改进备份策略

### **恢复时间目标 (RTO)**
- **全量恢复**: < 2小时
- **增量恢复**: < 30分钟
- **单表恢复**: < 15分钟

### **恢复点目标 (RPO)**
- **核心数据**: < 30分钟
- **一般数据**: < 1小时

这个备份和恢复策略确保了ITBook原子技能系统数据的安全性和业务连续性。
