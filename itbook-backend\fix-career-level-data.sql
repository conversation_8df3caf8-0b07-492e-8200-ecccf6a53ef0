-- =====================================================
-- ITBook项目 - 修复职业级别数据不一致问题
-- 
-- 问题：career_goal_id=2 (Java后端工程师) 缺少对应的career_level数据
-- 解决方案：为Java后端工程师添加正确的职业级别数据
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 删除可能存在的错误数据（career_goal_id=2的前端工程师级别数据）
DELETE FROM career_level WHERE career_goal_id = 2 AND level_name LIKE '%前端%';

-- 为Java后端工程师（career_goal_id=2）添加正确的职业级别数据
INSERT INTO career_level (career_goal_id, level_code, level_name, description, min_experience_years, max_experience_years, salary_range_min, salary_range_max, sort_order, is_active) VALUES
(2, 'junior', '初级Java后端工程师', '0-2年经验，掌握Java基础和Spring Boot', 0, 2, 8000, 15000, 1, TRUE),
(2, 'mid', '中级Java后端工程师', '2-5年经验，具备独立开发能力，熟悉微服务架构', 2, 5, 15000, 25000, 2, TRUE),
(2, 'senior', '高级Java后端工程师', '5-8年经验，具备架构设计能力，能够带领团队', 5, 8, 25000, 40000, 3, TRUE),
(2, 'expert', '专家级Java后端工程师', '8-12年经验，技术专家，具备深度技术能力', 8, 12, 40000, 60000, 4, TRUE),
(2, 'architect', 'Java架构师', '10年以上经验，系统架构师，具备全栈技术视野', 10, NULL, 60000, 100000, 5, TRUE)
ON DUPLICATE KEY UPDATE
level_name = VALUES(level_name),
description = VALUES(description),
min_experience_years = VALUES(min_experience_years),
max_experience_years = VALUES(max_experience_years),
salary_range_min = VALUES(salary_range_min),
salary_range_max = VALUES(salary_range_max),
is_active = VALUES(is_active);

-- 如果React前端工程师（假设career_goal_id=3）缺少级别数据，也添加上
INSERT INTO career_level (career_goal_id, level_code, level_name, description, min_experience_years, max_experience_years, salary_range_min, salary_range_max, sort_order, is_active) VALUES
(3, 'junior', '初级React前端工程师', '0-2年经验，掌握基础前端技能', 0, 2, 7000, 14000, 1, TRUE),
(3, 'mid', '中级React前端工程师', '2-5年经验，具备独立前端开发能力', 2, 5, 14000, 23000, 2, TRUE),
(3, 'senior', '高级React前端工程师', '5-8年经验，具备前端架构设计能力', 5, 8, 23000, 38000, 3, TRUE),
(3, 'expert', 'React前端专家', '8-12年经验，前端技术专家', 8, 12, 38000, 55000, 4, TRUE),
(3, 'architect', 'React前端架构师', '10年以上经验，前端架构师', 10, NULL, 55000, 90000, 5, TRUE)
ON DUPLICATE KEY UPDATE
level_name = VALUES(level_name),
description = VALUES(description),
min_experience_years = VALUES(min_experience_years),
max_experience_years = VALUES(max_experience_years),
salary_range_min = VALUES(salary_range_min),
salary_range_max = VALUES(salary_range_max),
is_active = VALUES(is_active);

-- 提交事务
COMMIT;

-- 验证数据
SELECT 
    cl.id,
    cl.career_goal_id,
    cg.name as career_goal_name,
    cl.level_code,
    cl.level_name,
    cl.is_active
FROM career_level cl
LEFT JOIN career_goal cg ON cl.career_goal_id = cg.id
WHERE cl.career_goal_id IN (2, 3)
ORDER BY cl.career_goal_id, cl.sort_order;
