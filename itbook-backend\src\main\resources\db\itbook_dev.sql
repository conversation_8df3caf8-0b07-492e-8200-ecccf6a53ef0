/*
 Navicat Premium Data Transfer

 Source Server         : 本地环境
 Source Server Type    : MySQL
 Source Server Version : 80040
 Source Host           : localhost:3306
 Source Schema         : itbook_dev

 Target Server Type    : MySQL
 Target Server Version : 80040
 File Encoding         : 65001

 Date: 10/07/2025 10:26:12
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for article
-- ----------------------------
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `author_id` bigint(0) NULL DEFAULT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `comment_count` int(0) NULL DEFAULT 0,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `created_at` datetime(6) NOT NULL,
  `is_featured` tinyint(1) NULL DEFAULT 0,
  `like_count` int(0) NULL DEFAULT 0,
  `published_at` datetime(6) NULL DEFAULT NULL,
  `status` enum('DRAFT','PUBLISHED','ARCHIVED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'DRAFT',
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `tags` json NULL,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `view_count` int(0) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKb70m3e0cghmyrbgn8i17k7l1f`(`author_id`) USING BTREE,
  CONSTRAINT `FKb70m3e0cghmyrbgn8i17k7l1f` FOREIGN KEY (`author_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company
-- ----------------------------
DROP TABLE IF EXISTS `company`;
CREATE TABLE `company`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `industry` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `logo_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `rating` decimal(3, 2) NULL DEFAULT 0.00,
  `review_count` int(0) NULL DEFAULT 0,
  `size_range` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `website` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for course
-- ----------------------------
DROP TABLE IF EXISTS `course`;
CREATE TABLE `course`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `certificate_available` tinyint(1) NULL DEFAULT 0,
  `created_at` datetime(6) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `difficulty` enum('beginner','intermediate','advanced') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'beginner',
  `duration` int(0) NULL DEFAULT 0,
  `enrollment_count` int(0) NULL DEFAULT 0,
  `instructor` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_featured` tinyint(1) NULL DEFAULT 0,
  `is_popular` tinyint(1) NULL DEFAULT 0,
  `learning_outcomes` json NULL,
  `prerequisites` json NULL,
  `preview_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `price` decimal(10, 2) NULL DEFAULT 0.00,
  `rating` decimal(3, 2) NULL DEFAULT 0.00,
  `review_count` int(0) NULL DEFAULT 0,
  `status` enum('DRAFT','PUBLISHED','ARCHIVED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'DRAFT',
  `tags` json NULL,
  `thumbnail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for job
-- ----------------------------
DROP TABLE IF EXISTS `job`;
CREATE TABLE `job`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `application_count` int(0) NULL DEFAULT 0,
  `benefits` json NULL,
  `company_id` bigint(0) NULL DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `currency` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY',
  `deadline` date NULL DEFAULT NULL,
  `education_requirement` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `experience_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_remote` tinyint(1) NULL DEFAULT 0,
  `is_urgent` tinyint(1) NULL DEFAULT 0,
  `job_type` enum('full-time','part-time','contract','internship','remote') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'full-time',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `published_at` datetime(6) NULL DEFAULT NULL,
  `requirements` json NULL,
  `responsibilities` json NULL COMMENT '工作职责列表，JSON格式存储职责描述数组',
  `salary_max` decimal(10, 2) NULL DEFAULT NULL,
  `salary_min` decimal(10, 2) NULL DEFAULT NULL,
  `status` enum('ACTIVE','CLOSED','EXPIRED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ACTIVE',
  `skills` json NULL COMMENT '职位技能要求列表，JSON格式存储技能名称数组',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `view_count` int(0) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK5q04favsasq8y70bsei7wv8fc`(`company_id`) USING BTREE,
  CONSTRAINT `FK5q04favsasq8y70bsei7wv8fc` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for learning_path
-- ----------------------------
DROP TABLE IF EXISTS `learning_path`;
CREATE TABLE `learning_path`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `completion_count` int(0) NULL DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `creator_id` bigint(0) NULL DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `difficulty_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `estimated_hours` int(0) NULL DEFAULT NULL,
  `is_recommended` bit(1) NULL DEFAULT NULL,
  `is_template` bit(1) NULL DEFAULT NULL,
  `learner_count` int(0) NULL DEFAULT NULL,
  `learning_objectives` json NULL,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `path_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `rating` decimal(3, 2) NULL DEFAULT NULL,
  `review_count` int(0) NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tags` json NULL,
  `target_job_id` bigint(0) NULL DEFAULT NULL,
  `updated_at` datetime(6) NOT NULL,
  `skill_tags` json NULL COMMENT '技能标签，JSON格式存储技能名称、等级、权重等信息',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKe0ch8ws08hq9mlmx5n2we9oqy`(`creator_id`) USING BTREE,
  INDEX `FKggcv077g6vwxpn3xd4rmcgyr0`(`target_job_id`) USING BTREE,
  CONSTRAINT `FKe0ch8ws08hq9mlmx5n2we9oqy` FOREIGN KEY (`creator_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKggcv077g6vwxpn3xd4rmcgyr0` FOREIGN KEY (`target_job_id`) REFERENCES `job` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for learning_path_step
-- ----------------------------
DROP TABLE IF EXISTS `learning_path_step`;
CREATE TABLE `learning_path_step`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `content_id` bigint(0) NULL DEFAULT NULL,
  `content_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `course_id` bigint(0) NULL DEFAULT NULL COMMENT '关联课程ID（当step_type为COURSE时）',
  `created_at` datetime(6) NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `difficulty_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `estimated_minutes` int(0) NULL DEFAULT NULL,
  `is_required` bit(1) NULL DEFAULT NULL,
  `learning_path_id` bigint(0) NOT NULL,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `step_order` int(0) NOT NULL,
  `step_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `weight` decimal(5, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKbooyn3iuiam4bgc08lbht0in2`(`learning_path_id`) USING BTREE,
  INDEX `idx_course_id`(`course_id`) USING BTREE,
  CONSTRAINT `FKbooyn3iuiam4bgc08lbht0in2` FOREIGN KEY (`learning_path_id`) REFERENCES `learning_path` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_learning_path_step_course` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for lesson
-- ----------------------------
DROP TABLE IF EXISTS `lesson`;
CREATE TABLE `lesson`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `course_id` bigint(0) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `document_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `duration` int(0) NULL DEFAULT 0,
  `is_preview` tinyint(1) NULL DEFAULT 0,
  `order_index` int(0) NULL DEFAULT 0,
  `resources` json NULL,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('video','document','quiz','practice') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'video',
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKjs3c7skmg8bvdddok5lc7s807`(`course_id`) USING BTREE,
  CONSTRAINT `FKjs3c7skmg8bvdddok5lc7s807` FOREIGN KEY (`course_id`) REFERENCES `course` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '个人简介',
  `created_at` datetime(6) NULL DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `password` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `gender` enum('MALE','FEMALE','PREFER_NOT_TO_SAY') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '性别',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所在城市',
  `website` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '个人网站',
  `profession` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '职业/职位',
  `company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公司/组织',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK_ob8kqyqqgmefl0aco34akdtpe`(`email`) USING BTREE,
  UNIQUE INDEX `UK_sb8bbouer5wak8vyiiy4pf2bx`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_learning_path_progress
-- ----------------------------
DROP TABLE IF EXISTS `user_learning_path_progress`;
CREATE TABLE `user_learning_path_progress`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `completed_at` datetime(6) NULL DEFAULT NULL,
  `completed_steps` int(0) NULL DEFAULT NULL,
  `completion_percentage` decimal(5, 2) NULL DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `current_step_id` bigint(0) NULL DEFAULT NULL,
  `custom_tags` json NULL,
  `estimated_remaining_minutes` int(0) NULL DEFAULT NULL,
  `is_bookmarked` bit(1) NULL DEFAULT NULL,
  `is_recommended` bit(1) NULL DEFAULT NULL,
  `last_studied_at` datetime(6) NULL DEFAULT NULL,
  `learning_path_id` bigint(0) NOT NULL,
  `learning_preferences` json NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `rating` decimal(3, 2) NULL DEFAULT NULL,
  `review_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `started_at` datetime(6) NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `studied_minutes` int(0) NULL DEFAULT NULL,
  `target_completion_date` datetime(6) NULL DEFAULT NULL,
  `total_steps` int(0) NULL DEFAULT NULL,
  `updated_at` datetime(6) NOT NULL,
  `user_id` bigint(0) NOT NULL,
  `career_goal_id` bigint(0) NULL DEFAULT NULL COMMENT '关联的职业目标ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UKog9142sr8ym340t5npkf3x6um`(`user_id`, `learning_path_id`) USING BTREE,
  INDEX `FK32ed2ldpdda1qk42wshun0uy8`(`current_step_id`) USING BTREE,
  INDEX `FKr2frmjdxogs2jwp20t211aqsb`(`learning_path_id`) USING BTREE,
  INDEX `idx_career_goal_id`(`career_goal_id`) USING BTREE,
  CONSTRAINT `FK1uodm0qwy5pf05v4scoj97rnj` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK32ed2ldpdda1qk42wshun0uy8` FOREIGN KEY (`current_step_id`) REFERENCES `learning_path_step` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKr2frmjdxogs2jwp20t211aqsb` FOREIGN KEY (`learning_path_id`) REFERENCES `learning_path` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_learning_path_progress_career_goal` FOREIGN KEY (`career_goal_id`) REFERENCES `user_career_goal` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_career_goal
-- ----------------------------
DROP TABLE IF EXISTS `user_career_goal`;
CREATE TABLE `user_career_goal`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `career_goal_id` bigint(0) NULL DEFAULT NULL COMMENT '职业目标ID（关联career_goal表）',
  `career_level_id` bigint(0) NOT NULL COMMENT '职业级别ID（关联career_level表）',
  `set_at` datetime(6) NOT NULL COMMENT '设置时间',
  `target_completion_date` datetime(6) NULL DEFAULT NULL COMMENT '目标完成时间',
  `is_active` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否激活',
  `priority` int(0) NULL DEFAULT 1 COMMENT '优先级',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '目标描述',
  `motivation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '动机说明',
  `created_at` datetime(6) NOT NULL COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_career_goal_id`(`career_goal_id`) USING BTREE,
  INDEX `idx_career_level_id`(`career_level_id`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  UNIQUE INDEX `uk_user_active_goal`(`user_id`, `is_active`) USING BTREE COMMENT '每个用户只能有一个激活的职业目标',
  CONSTRAINT `fk_user_career_goal_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_career_goal_career_goal` FOREIGN KEY (`career_goal_id`) REFERENCES `career_goal` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_career_goal_career_level` FOREIGN KEY (`career_level_id`) REFERENCES `career_level` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户职业目标表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_step_progress
-- ----------------------------
DROP TABLE IF EXISTS `user_step_progress`;
CREATE TABLE `user_step_progress`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `step_id` bigint(0) NOT NULL COMMENT '学习路径步骤ID',
  `status` enum('NOT_STARTED','IN_PROGRESS','COMPLETED','PAUSED','SKIPPED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NOT_STARTED' COMMENT '学习状态',
  `completion_percentage` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '完成百分比',
  `studied_minutes` int(0) NOT NULL DEFAULT 0 COMMENT '已学习时间（分钟）',
  `course_progress_detail` json NULL COMMENT '课程详细进度（JSON格式）',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '学习笔记',
  `difficulty_rating` int(0) NULL DEFAULT NULL COMMENT '难度评分(1-5)',
  `quality_rating` int(0) NULL DEFAULT NULL COMMENT '质量评分(1-5)',
  `started_at` datetime(6) NULL DEFAULT NULL COMMENT '开始学习时间',
  `last_studied_at` datetime(6) NULL DEFAULT NULL COMMENT '最后学习时间',
  `completed_at` datetime(6) NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime(6) NOT NULL COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_step_id`(`step_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_last_studied_at`(`last_studied_at`) USING BTREE,
  UNIQUE INDEX `uk_user_step`(`user_id`, `step_id`) USING BTREE COMMENT '用户和步骤的唯一约束',
  CONSTRAINT `fk_user_step_progress_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_step_progress_step` FOREIGN KEY (`step_id`) REFERENCES `learning_path_step` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户学习步骤进度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for personalized_path
-- ----------------------------
DROP TABLE IF EXISTS `personalized_path`;
CREATE TABLE `personalized_path`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `job_id` bigint(0) NULL DEFAULT NULL COMMENT '目标岗位ID（可选）',
  `personalized_path_id` bigint(0) NOT NULL COMMENT '个性化学习路径ID',
  `personalization_score` decimal(5, 3) NOT NULL COMMENT '个性化匹配分数',
  `personalization_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '个性化理由',
  `algorithm_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '算法版本',
  `personalization_factors` json NULL COMMENT '个性化因子详情',
  `is_accepted` bit(1) NULL DEFAULT NULL COMMENT '用户是否接受个性化路径',
  `feedback_rating` int(0) NULL DEFAULT NULL COMMENT '用户反馈评分(1-5)',
  `user_feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户反馈文本',
  `created_at` datetime(6) NOT NULL COMMENT '创建时间',
  `updated_at` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_job_id`(`job_id`) USING BTREE,
  INDEX `idx_personalized_path_id`(`personalized_path_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_personalized_path_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_personalized_path_job` FOREIGN KEY (`job_id`) REFERENCES `job` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_personalized_path_learning_path` FOREIGN KEY (`personalized_path_id`) REFERENCES `learning_path` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic COMMENT = '个性化学习路径记录表';

-- ----------------------------
-- Table structure for user_role
-- ----------------------------
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role`  (
  `user_id` bigint(0) NOT NULL,
  `role_id` bigint(0) NOT NULL,
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE,
  INDEX `FKa68196081fvovjhkek5m97n3y`(`role_id`) USING BTREE,
  CONSTRAINT `FKa68196081fvovjhkek5m97n3y` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKfgsgxvihks805qcq8sq26ab7c` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for recommendation_feedback
-- ----------------------------
DROP TABLE IF EXISTS `recommendation_feedback`;
CREATE TABLE `recommendation_feedback`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `path_id` bigint(0) NOT NULL COMMENT '学习路径ID',
  `recommendation_id` bigint(0) NULL DEFAULT NULL COMMENT '推荐记录ID',
  `action` enum('ACCEPT','REJECT','LIKE','DISLIKE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '反馈动作类型',
  `rating` int(0) NULL DEFAULT NULL COMMENT '评分(1-5分)',
  `feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '反馈内容',
  `feedback_time` datetime(6) NOT NULL COMMENT '反馈时间',
  `is_helpful` tinyint(1) NULL DEFAULT NULL COMMENT '是否有用',
  `tags` json NULL COMMENT '反馈标签',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理信息',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `created_at` datetime(6) NOT NULL COMMENT '创建时间',
  `updated_at` datetime(6) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_path_id`(`path_id`) USING BTREE,
  INDEX `idx_recommendation_id`(`recommendation_id`) USING BTREE,
  INDEX `idx_feedback_time`(`feedback_time`) USING BTREE,
  INDEX `idx_action`(`action`) USING BTREE,
  CONSTRAINT `fk_recommendation_feedback_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_recommendation_feedback_path` FOREIGN KEY (`path_id`) REFERENCES `learning_path` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_recommendation_feedback_personalized_path` FOREIGN KEY (`recommendation_id`) REFERENCES `personalized_path` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '推荐反馈表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
