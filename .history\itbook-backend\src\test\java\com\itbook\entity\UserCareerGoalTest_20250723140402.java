package com.itbook.entity;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * UserCareerGoal实体类测试
 * 验证修复后的字段映射和枚举类型是否正确工作
 */
public class UserCareerGoalTest {

    @Test
    public void testTargetLevelEnum() {
        // 测试枚举值
        assertEquals("junior", UserCareerGoal.TargetLevel.junior.name());
        assertEquals("mid", UserCareerGoal.TargetLevel.mid.name());
        assertEquals("senior", UserCareerGoal.TargetLevel.senior.name());
    }

    @Test
    public void testConstructorWithNewFields() {
        // 测试新的构造函数
        Long userId = 2L;
        Long careerGoalId = 3L;
        UserCareerGoal.TargetLevel targetLevel = UserCareerGoal.TargetLevel.junior;

        UserCareerGoal careerGoal = new UserCareerGoal(userId, careerGoalId, targetLevel);

        assertEquals(userId, careerGoal.getUserId());
        assertEquals(careerGoalId, careerGoal.getCareerGoalId());
        assertEquals(targetLevel, careerGoal.getTargetLevel());
        assertTrue(careerGoal.getIsActive());
        assertNotNull(careerGoal.getSetAt());
    }

    @Test
    public void testBackwardCompatibilityMethods() {
        // 测试向后兼容的方法
        UserCareerGoal careerGoal = new UserCareerGoal();
        careerGoal.setCareerGoalId(5L);
        careerGoal.setTargetLevel(UserCareerGoal.TargetLevel.mid);

        // 测试废弃的getter方法
        assertEquals(5L, careerGoal.getTargetJobId());
        assertEquals(1L, careerGoal.getCareerLevelId()); // mid的ordinal是1

        // 测试废弃的setter方法
        careerGoal.setTargetJobId(10L);
        assertEquals(10L, careerGoal.getCareerGoalId());

        careerGoal.setCareerLevelId(2L); // senior的ordinal是2
        assertEquals(UserCareerGoal.TargetLevel.senior, careerGoal.getTargetLevel());
    }

    @Test
    public void testToString() {
        UserCareerGoal careerGoal = new UserCareerGoal();
        careerGoal.setUserId(1L);
        careerGoal.setCareerGoalId(2L);
        careerGoal.setTargetLevel(UserCareerGoal.TargetLevel.junior);
        careerGoal.setDescription("测试描述");
        careerGoal.setMotivation("测试动机");

        String toString = careerGoal.toString();
        assertTrue(toString.contains("careerGoalId=2"));
        assertTrue(toString.contains("targetLevel=junior"));
        assertTrue(toString.contains("userId=1"));
    }

    @Test
    public void testSettersAndGetters() {
        UserCareerGoal careerGoal = new UserCareerGoal();
        
        // 测试新字段的setter和getter
        careerGoal.setTargetJobId(100L);
        assertEquals(100L, careerGoal.getTargetJobId());

        careerGoal.setTargetLevel(UserCareerGoal.TargetLevel.senior);
        assertEquals(UserCareerGoal.TargetLevel.senior, careerGoal.getTargetLevel());

        careerGoal.setDescription("Java后端工程师");
        assertEquals("Java后端工程师", careerGoal.getDescription());

        careerGoal.setMotivation("提升技能");
        assertEquals("提升技能", careerGoal.getMotivation());
    }
}
