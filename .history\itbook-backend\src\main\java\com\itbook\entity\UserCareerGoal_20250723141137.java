package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 用户职业目标实体类
 * 用于存储用户的职业发展目标和相关信息
 * 
 * <AUTHOR> Team
 * @since 2025-07-11
 */
@Entity
@Table(name = "user_career_goal")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class UserCareerGoal {
 
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 用户（关联查询）
     */
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    /**
     * 职业目标ID（对应数据库中的career_goal_id字段）
     */
    @Column(name = "career_goal_id")
    private Long careerGoalId;

    /**
     * 职业目标（关联查询）
     * 关联到CareerGoal表，表示职业目标模板
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "career_goal_id", insertable = false, updatable = false)
    private CareerGoal careerGoal;

    /**
     * 目标级别（对应数据库中的target_level字段）
     * 使用枚举类型，直接映射数据库的enum字段
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "target_level", nullable = false)
    @NotNull(message = "目标级别不能为空")
    private TargetLevel targetLevel;

    /**
     * 目标级别枚举
     */
    public enum TargetLevel {
        junior, mid, senior
    }

    /**
     * 设置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "set_at", nullable = false)
    @NotNull(message = "设置时间不能为空")
    private LocalDateTime setAt;

    /**
     * 目标完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "target_completion_date")
    private LocalDateTime targetCompletionDate;

    /**
     * 是否激活
     */
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    /**
     * 优先级
     */
    @Column(name = "priority")
    private Integer priority = 1;

    /**
     * 目标描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 动机说明
     */
    @Column(name = "motivation", columnDefinition = "TEXT")
    private String motivation;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 构造函数
    public UserCareerGoal() {}

    public UserCareerGoal(Long userId, Long careerGoalId, TargetLevel targetLevel) {
        this.userId = userId;
        this.careerGoalId = careerGoalId;
        this.targetLevel = targetLevel;
        this.setAt = LocalDateTime.now();
        this.isActive = true;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Long getCareerGoalId() {
        return careerGoalId;
    }

    public void setCareerGoalId(Long careerGoalId) {
        this.careerGoalId = careerGoalId;
    }

    public CareerGoal getCareerGoal() {
        return careerGoal;
    }

    public void setCareerGoal(CareerGoal careerGoal) {
        this.careerGoal = careerGoal;
    }

    public TargetLevel getTargetLevel() {
        return targetLevel;
    }

    public void setTargetLevel(TargetLevel targetLevel) {
        this.targetLevel = targetLevel;
    }

    // 为了向后兼容，保留旧的方法名（已废弃）
    @Deprecated
    public Long getTargetJobId() {
        return careerGoalId;
    }

    @Deprecated
    public void setTargetJobId(Long targetJobId) {
        this.careerGoalId = targetJobId;
    }

    @Deprecated
    public Job getTargetJob() {
        // 注意：这是向后兼容方法，但现在careerGoal是CareerGoal类型，不是Job类型
        // 返回null以避免类型转换错误，建议使用getCareerGoal()方法
        return null;
    }

    @Deprecated
    public void setTargetJob(Job targetJob) {
        // 注意：这是向后兼容方法，但现在careerGoal是CareerGoal类型，不是Job类型
        // 此方法已废弃，建议使用setCareerGoal()方法
        // 不执行任何操作以避免类型转换错误
    }

    @Deprecated
    public Long getCareerLevelId() {
        return targetLevel != null ? (long) targetLevel.ordinal() : null;
    }

    @Deprecated
    public void setCareerLevelId(Long careerLevelId) {
        if (careerLevelId != null) {
            TargetLevel[] levels = TargetLevel.values();
            if (careerLevelId < levels.length) {
                this.targetLevel = levels[careerLevelId.intValue()];
            }
        }
    }

    public LocalDateTime getSetAt() {
        return setAt;
    }

    public void setSetAt(LocalDateTime setAt) {
        this.setAt = setAt;
    }

    public LocalDateTime getTargetCompletionDate() {
        return targetCompletionDate;
    }

    public void setTargetCompletionDate(LocalDateTime targetCompletionDate) {
        this.targetCompletionDate = targetCompletionDate;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMotivation() {
        return motivation;
    }

    public void setMotivation(String motivation) {
        this.motivation = motivation;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "UserCareerGoal{" +
                "id=" + id +
                ", userId=" + userId +
                ", careerGoalId=" + careerGoalId +
                ", targetLevel=" + targetLevel +
                ", setAt=" + setAt +
                ", targetCompletionDate=" + targetCompletionDate +
                ", isActive=" + isActive +
                ", priority=" + priority +
                ", description='" + description + '\'' +
                ", motivation='" + motivation + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
