package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.entity.AtomicSkill;
import com.itbook.entity.SkillRelationship;
import com.itbook.service.SkillRelationshipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 技能关系管理控制器
 * 提供技能关系图谱的REST API接口，实现知识图谱构建的核心功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@RestController
@RequestMapping("/skill-relationships")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "技能关系管理", description = "技能关系图谱的查询、创建、更新、删除等操作")
public class SkillRelationshipController {

    private final SkillRelationshipService skillRelationshipService;

    /**
     * 获取所有技能关系
     */
    @GetMapping
    @Operation(summary = "获取技能关系列表", description = "获取所有活跃的技能关系")
    public ResponseEntity<ApiResponse<List<SkillRelationship>>> getAllSkillRelationships() {
        log.debug("获取所有技能关系");
        
        try {
            List<SkillRelationship> relationships = skillRelationshipService.getAllActiveRelationships();
            return ResponseEntity.ok(ApiResponse.success(relationships));
        } catch (Exception e) {
            log.error("获取技能关系时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取技能关系失败"));
        }
    }

    /**
     * 根据技能ID获取前置技能
     */
    @GetMapping("/skills/{skillId}/prerequisites")
    @Operation(summary = "获取前置技能", description = "获取指定技能的前置技能列表")
    public ResponseEntity<ApiResponse<List<AtomicSkill>>> getPrerequisiteSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.debug("获取前置技能: skillId={}", skillId);
        
        try {
            List<AtomicSkill> prerequisites = skillRelationshipService.getPrerequisiteSkills(skillId);
            return ResponseEntity.ok(ApiResponse.success(prerequisites));
        } catch (Exception e) {
            log.error("获取前置技能时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取前置技能失败"));
        }
    }

    /**
     * 根据技能ID获取后续技能
     */
    @GetMapping("/skills/{skillId}/successors")
    @Operation(summary = "获取后续技能", description = "获取指定技能的后续技能列表")
    public ResponseEntity<ApiResponse<List<AtomicSkill>>> getSuccessorSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.debug("获取后续技能: skillId={}", skillId);
        
        try {
            List<AtomicSkill> successors = skillRelationshipService.getSuccessorSkills(skillId);
            return ResponseEntity.ok(ApiResponse.success(successors));
        } catch (Exception e) {
            log.error("获取后续技能时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取后续技能失败"));
        }
    }

    /**
     * 根据技能ID获取相关技能
     */
    @GetMapping("/skills/{skillId}/related")
    @Operation(summary = "获取相关技能", description = "获取指定技能的相关技能列表")
    public ResponseEntity<ApiResponse<List<AtomicSkill>>> getRelatedSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.debug("获取相关技能: skillId={}", skillId);
        
        try {
            List<AtomicSkill> relatedSkills = skillRelationshipService.getRelatedSkills(skillId);
            return ResponseEntity.ok(ApiResponse.success(relatedSkills));
        } catch (Exception e) {
            log.error("获取相关技能时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取相关技能失败"));
        }
    }

    /**
     * 根据技能ID获取并行技能
     */
    @GetMapping("/skills/{skillId}/corequisites")
    @Operation(summary = "获取并行技能", description = "获取指定技能的并行技能列表")
    public ResponseEntity<ApiResponse<List<AtomicSkill>>> getCorequisiteSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.debug("获取并行技能: skillId={}", skillId);
        
        try {
            List<AtomicSkill> corequisites = skillRelationshipService.getCorequisiteSkills(skillId);
            return ResponseEntity.ok(ApiResponse.success(corequisites));
        } catch (Exception e) {
            log.error("获取并行技能时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取并行技能失败"));
        }
    }

    /**
     * 根据技能ID获取替代技能
     */
    @GetMapping("/skills/{skillId}/alternatives")
    @Operation(summary = "获取替代技能", description = "获取指定技能的替代技能列表")
    public ResponseEntity<ApiResponse<List<AtomicSkill>>> getAlternativeSkills(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.debug("获取替代技能: skillId={}", skillId);
        
        try {
            List<AtomicSkill> alternatives = skillRelationshipService.getAlternativeSkills(skillId);
            return ResponseEntity.ok(ApiResponse.success(alternatives));
        } catch (Exception e) {
            log.error("获取替代技能时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取替代技能失败"));
        }
    }

    /**
     * 构建技能学习路径
     */
    @GetMapping("/skills/{skillId}/learning-path")
    @Operation(summary = "构建学习路径", description = "基于技能关系构建完整的学习路径")
    public ResponseEntity<ApiResponse<SkillRelationshipService.SkillLearningPath>> buildLearningPath(
            @Parameter(description = "目标技能ID") @PathVariable Long skillId) {
        
        log.debug("构建技能学习路径: skillId={}", skillId);
        
        try {
            SkillRelationshipService.SkillLearningPath learningPath = skillRelationshipService.buildLearningPath(skillId);
            return ResponseEntity.ok(ApiResponse.success(learningPath));
        } catch (Exception e) {
            log.error("构建学习路径时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("构建学习路径失败"));
        }
    }

    /**
     * 创建技能关系
     */
    @PostMapping
    @Operation(summary = "创建技能关系", description = "创建新的技能关系")
    public ResponseEntity<ApiResponse<SkillRelationship>> createSkillRelationship(
            @Parameter(description = "技能关系信息") @Valid @RequestBody SkillRelationship relationship) {
        
        log.info("创建技能关系: sourceSkillId={}, targetSkillId={}, type={}", 
                relationship.getSourceSkillId(), relationship.getTargetSkillId(), relationship.getRelationshipType());
        
        try {
            SkillRelationship createdRelationship = skillRelationshipService.createRelationship(relationship);
            return ResponseEntity.ok(ApiResponse.success(createdRelationship));
        } catch (Exception e) {
            log.error("创建技能关系时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("创建技能关系失败"));
        }
    }

    /**
     * 根据关系类型获取技能关系
     */
    @GetMapping("/type/{relationshipType}")
    @Operation(summary = "根据类型获取关系", description = "根据关系类型获取技能关系列表")
    public ResponseEntity<ApiResponse<List<SkillRelationship>>> getRelationshipsByType(
            @Parameter(description = "关系类型") @PathVariable SkillRelationship.RelationshipType relationshipType) {
        
        log.debug("根据类型获取技能关系: relationshipType={}", relationshipType);
        
        try {
            List<SkillRelationship> relationships = skillRelationshipService.getRelationshipsByType(relationshipType);
            return ResponseEntity.ok(ApiResponse.success(relationships));
        } catch (Exception e) {
            log.error("根据类型获取技能关系时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取技能关系失败"));
        }
    }

    /**
     * 获取技能关系统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取关系统计", description = "获取技能关系的统计信息")
    public ResponseEntity<ApiResponse<SkillRelationshipService.RelationshipStatistics>> getRelationshipStatistics() {
        
        log.debug("获取技能关系统计信息");
        
        try {
            SkillRelationshipService.RelationshipStatistics statistics = skillRelationshipService.getRelationshipStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            log.error("获取技能关系统计时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error("获取关系统计失败"));
        }
    }
}
