package com.itbook.entity;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * UserCareerGoal实体类测试
 * 验证修复后的字段映射和枚举类型是否正确工作
 */
public class UserCareerGoalTest {

    @Test
    public void testCareerLevelId() {
        // 测试职业级别ID
        UserCareerGoal careerGoal = new UserCareerGoal();
        careerGoal.setCareerLevelId(1L);
        assertEquals(1L, careerGoal.getCareerLevelId());

        careerGoal.setCareerLevelId(2L);
        assertEquals(2L, careerGoal.getCareerLevelId());
    }

    @Test
    public void testConstructorWithNewFields() {
        // 测试新的构造函数
        Long userId = 2L;
        Long careerGoalId = 3L;
        UserCareerGoal.TargetLevel targetLevel = UserCareerGoal.TargetLevel.junior;

        UserCareerGoal careerGoal = new UserCareerGoal(userId, careerGoalId, targetLevel);

        assertEquals(userId, careerGoal.getUserId());
        assertEquals(careerGoalId, careerGoal.getCareerGoalId());
        assertEquals(targetLevel, careerGoal.getTargetLevel());
        assertTrue(careerGoal.getIsActive());
        assertNotNull(careerGoal.getSetAt());
    }

    @Test
    public void testFieldMappings() {
        // 测试字段映射
        UserCareerGoal careerGoal = new UserCareerGoal();
        careerGoal.setCareerGoalId(5L);
        careerGoal.setTargetLevel(UserCareerGoal.TargetLevel.mid);

        // 测试基本的getter方法
        assertEquals(5L, careerGoal.getCareerGoalId());
        assertEquals(UserCareerGoal.TargetLevel.mid, careerGoal.getTargetLevel());

        // 测试setter方法
        careerGoal.setCareerGoalId(10L);
        assertEquals(10L, careerGoal.getCareerGoalId());

        careerGoal.setTargetLevel(UserCareerGoal.TargetLevel.senior);
        assertEquals(UserCareerGoal.TargetLevel.senior, careerGoal.getTargetLevel());
    }

    @Test
    public void testToString() {
        UserCareerGoal careerGoal = new UserCareerGoal();
        careerGoal.setUserId(1L);
        careerGoal.setCareerGoalId(2L);
        careerGoal.setTargetLevel(UserCareerGoal.TargetLevel.junior);
        careerGoal.setDescription("测试描述");
        careerGoal.setMotivation("测试动机");

        String toString = careerGoal.toString();
        assertTrue(toString.contains("careerGoalId=2"));
        assertTrue(toString.contains("targetLevel=junior"));
        assertTrue(toString.contains("userId=1"));
    }

    @Test
    public void testSettersAndGetters() {
        UserCareerGoal careerGoal = new UserCareerGoal();

        // 测试新字段的setter和getter
        careerGoal.setCareerGoalId(100L);
        assertEquals(100L, careerGoal.getCareerGoalId());

        careerGoal.setTargetLevel(UserCareerGoal.TargetLevel.senior);
        assertEquals(UserCareerGoal.TargetLevel.senior, careerGoal.getTargetLevel());

        careerGoal.setDescription("Java后端工程师");
        assertEquals("Java后端工程师", careerGoal.getDescription());

        careerGoal.setMotivation("提升技能");
        assertEquals("提升技能", careerGoal.getMotivation());
    }

    @Test
    public void testCareerGoalAssociation() {
        UserCareerGoal userCareerGoal = new UserCareerGoal();

        // 创建一个CareerGoal对象用于测试
        CareerGoal careerGoal = new CareerGoal();
        careerGoal.setId(1L);
        careerGoal.setName("Java后端工程师");
        careerGoal.setCategory("后端开发");

        // 测试CareerGoal关联
        userCareerGoal.setCareerGoal(careerGoal);
        assertEquals(careerGoal, userCareerGoal.getCareerGoal());
        assertEquals("Java后端工程师", userCareerGoal.getCareerGoal().getName());
        assertEquals("后端开发", userCareerGoal.getCareerGoal().getCategory());
    }

    @Test
    public void testCareerGoalRelationship() {
        UserCareerGoal userCareerGoal = new UserCareerGoal();

        // 测试CareerGoal关联关系
        // 初始状态下careerGoal应该为null
        assertNull(userCareerGoal.getCareerGoal());

        // 设置careerGoalId
        userCareerGoal.setCareerGoalId(1L);
        assertEquals(1L, userCareerGoal.getCareerGoalId());
    }
}
