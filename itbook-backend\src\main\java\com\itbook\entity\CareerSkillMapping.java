package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 职业技能映射实体
 * 将现有career_skill与原子技能关联，建立新旧技能体系的映射关系
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Entity
@Table(name = "career_skill_mapping")
public class CareerSkillMapping {

    /**
     * 重要程度枚举
     */
    public enum Importance {
        CRITICAL("关键"),
        IMPORTANT("重要"),
        NICE_TO_HAVE("加分项");
        
        private final String description;
        
        Importance(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 映射来源枚举
     */
    public enum MappingSource {
        MANUAL("手动映射"),
        AUTO_GENERATED("自动生成"),
        ML_INFERRED("机器学习推断");
        
        private final String description;
        
        MappingSource(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 要求掌握水平枚举
     */
    public enum RequiredMasteryLevel {
        BASIC("基础"),
        INTERMEDIATE("中级"),
        ADVANCED("高级"),
        EXPERT("专家");
        
        private final String description;
        
        RequiredMasteryLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 职业技能ID（关联career_skill表）
     */
    @NotNull(message = "职业技能ID不能为空")
    @Column(name = "career_skill_id", nullable = false)
    private Long careerSkillId;

    /**
     * 职业技能（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "career_skill_id", insertable = false, updatable = false)
    @JsonIgnore
    private CareerSkill careerSkill;

    /**
     * 原子技能ID
     */
    @NotNull(message = "原子技能ID不能为空")
    @Column(name = "atomic_skill_id", nullable = false)
    private Long atomicSkillId;

    /**
     * 原子技能（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "atomic_skill_id", insertable = false, updatable = false)
    @JsonIgnore
    private AtomicSkill atomicSkill;

    /**
     * 权重(0-1)
     */
    @DecimalMin(value = "0.0", message = "权重不能小于0")
    @DecimalMax(value = "1.0", message = "权重不能大于1")
    @Column(name = "weight", precision = 3, scale = 2)
    private BigDecimal weight = BigDecimal.ONE;

    /**
     * 重要程度
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "importance", nullable = false)
    private Importance importance = Importance.IMPORTANT;

    /**
     * 要求掌握水平
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "required_mastery_level", nullable = false)
    private RequiredMasteryLevel requiredMasteryLevel = RequiredMasteryLevel.INTERMEDIATE;

    /**
     * 映射原因
     */
    @Column(name = "mapping_reason", columnDefinition = "TEXT")
    private String mappingReason;

    /**
     * 映射来源
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "mapping_source")
    private MappingSource mappingSource = MappingSource.MANUAL;

    /**
     * 置信度分数(0-1)
     */
    @DecimalMin(value = "0.0", message = "置信度分数不能小于0")
    @DecimalMax(value = "1.0", message = "置信度分数不能大于1")
    @Column(name = "confidence_score", precision = 3, scale = 2)
    private BigDecimal confidenceScore = BigDecimal.ONE;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    // 构造函数
    public CareerSkillMapping() {}

    public CareerSkillMapping(Long careerSkillId, Long atomicSkillId) {
        this.careerSkillId = careerSkillId;
        this.atomicSkillId = atomicSkillId;
    }

    public CareerSkillMapping(Long careerSkillId, Long atomicSkillId, Importance importance, RequiredMasteryLevel requiredMasteryLevel) {
        this.careerSkillId = careerSkillId;
        this.atomicSkillId = atomicSkillId;
        this.importance = importance;
        this.requiredMasteryLevel = requiredMasteryLevel;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getCareerSkillId() { return careerSkillId; }
    public void setCareerSkillId(Long careerSkillId) { this.careerSkillId = careerSkillId; }

    public CareerSkill getCareerSkill() { return careerSkill; }
    public void setCareerSkill(CareerSkill careerSkill) { this.careerSkill = careerSkill; }

    public Long getAtomicSkillId() { return atomicSkillId; }
    public void setAtomicSkillId(Long atomicSkillId) { this.atomicSkillId = atomicSkillId; }

    public AtomicSkill getAtomicSkill() { return atomicSkill; }
    public void setAtomicSkill(AtomicSkill atomicSkill) { this.atomicSkill = atomicSkill; }

    public BigDecimal getWeight() { return weight; }
    public void setWeight(BigDecimal weight) { this.weight = weight; }

    public Importance getImportance() { return importance; }
    public void setImportance(Importance importance) { this.importance = importance; }

    public RequiredMasteryLevel getRequiredMasteryLevel() { return requiredMasteryLevel; }
    public void setRequiredMasteryLevel(RequiredMasteryLevel requiredMasteryLevel) { this.requiredMasteryLevel = requiredMasteryLevel; }

    public String getMappingReason() { return mappingReason; }
    public void setMappingReason(String mappingReason) { this.mappingReason = mappingReason; }

    public MappingSource getMappingSource() { return mappingSource; }
    public void setMappingSource(MappingSource mappingSource) { this.mappingSource = mappingSource; }

    public BigDecimal getConfidenceScore() { return confidenceScore; }
    public void setConfidenceScore(BigDecimal confidenceScore) { this.confidenceScore = confidenceScore; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public Long getCreatedBy() { return createdBy; }
    public void setCreatedBy(Long createdBy) { this.createdBy = createdBy; }

    @Override
    public String toString() {
        return "CareerSkillMapping{" +
                "id=" + id +
                ", careerSkillId=" + careerSkillId +
                ", atomicSkillId=" + atomicSkillId +
                ", importance=" + importance +
                ", requiredMasteryLevel=" + requiredMasteryLevel +
                ", weight=" + weight +
                ", confidenceScore=" + confidenceScore +
                '}';
    }
}
