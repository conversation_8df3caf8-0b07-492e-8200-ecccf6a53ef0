package com.itbook.service;

import com.itbook.dto.RecommendedJobDTO;
import com.itbook.entity.Job;
import com.itbook.entity.User;
import com.itbook.entity.UserCareerGoal;
import com.itbook.entity.UserSkillAssessment;
import com.itbook.repository.JobRepository;
import com.itbook.repository.UserRepository;
import com.itbook.repository.UserCareerGoalRepository;
import com.itbook.repository.UserSkillAssessmentRepository;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 岗位服务类
 * 提供岗位相关的业务逻辑处理，包括职位推荐功能
 */
@Service
@Transactional
public class JobService {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserCareerGoalRepository userCareerGoalRepository;

    @Autowired
    private UserSkillAssessmentRepository userSkillAssessmentRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 🗑️ 已删除 getAllActiveJobs() 方法 - 技术债清理，统一使用推荐接口

    /**
     * 分页获取活跃岗位
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param sortDir 排序方向
     * @return 分页岗位数据
     */
    public Page<Job> getActiveJobsWithPagination(int page, int size, String sortBy, String sortDir) {
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        // 这里需要创建一个自定义查询方法来支持分页
        return jobRepository.findAll(pageable);
    }

    /**
     * 根据ID获取岗位详情
     * @param id 岗位ID
     * @return 岗位详情
     */
    public Optional<Job> getJobById(Long id) {
        Optional<Job> job = jobRepository.findById(id);
        if (job.isPresent()) {
            // 增加浏览次数
            Job jobEntity = job.get();
            jobEntity.setViewCount(jobEntity.getViewCount() + 1);
            jobRepository.save(jobEntity);
        }
        return job;
    }

    /**
     * 根据标题搜索岗位
     * @param title 标题关键词
     * @return 匹配的岗位列表
     */
    public List<Job> searchJobsByTitle(String title) {
        return jobRepository.findByTitleContaining(title);
    }

    /**
     * 根据地点搜索岗位
     * @param location 地点关键词
     * @return 匹配的岗位列表
     */
    public List<Job> searchJobsByLocation(String location) {
        return jobRepository.findByLocationContaining(location);
    }

    /**
     * 根据工作类型获取岗位
     * @param jobType 工作类型
     * @return 岗位列表
     */
    public List<Job> getJobsByType(Job.JobType jobType) {
        return jobRepository.findByJobType(jobType);
    }

    /**
     * 根据薪资范围搜索岗位
     * @param minSalary 最低薪资
     * @param maxSalary 最高薪资
     * @return 匹配的岗位列表
     */
    public List<Job> searchJobsBySalaryRange(BigDecimal minSalary, BigDecimal maxSalary) {
        return jobRepository.findBySalaryRange(minSalary, maxSalary);
    }

    /**
     * 根据经验要求获取岗位
     * @param experienceLevel 经验要求
     * @return 岗位列表
     */
    public List<Job> getJobsByExperience(String experienceLevel) {
        return jobRepository.findByExperienceLevel(experienceLevel);
    }

    /**
     * 获取热门岗位
     * @param limit 限制数量
     * @return 热门岗位列表
     */
    public List<Job> getPopularJobs(int limit) {
        return jobRepository.findPopularJobs(limit);
    }

    /**
     * 获取最新岗位
     * @param limit 限制数量
     * @return 最新岗位列表
     */
    public List<Job> getLatestJobs(int limit) {
        return jobRepository.findLatestJobs(limit);
    }

    /**
     * 获取紧急岗位
     * @return 紧急岗位列表
     */
    public List<Job> getUrgentJobs() {
        return jobRepository.findUrgentJobs();
    }

    /**
     * 获取远程岗位
     * @return 远程岗位列表
     */
    public List<Job> getRemoteJobs() {
        return jobRepository.findRemoteJobs();
    }



    /**
     * 创建新岗位
     * @param job 岗位信息
     * @return 创建的岗位
     */
    public Job createJob(Job job) {
        job.setPublishedAt(LocalDateTime.now());
        job.setStatus(Job.JobStatus.ACTIVE);
        job.setApplicationCount(0);
        job.setViewCount(0);
        return jobRepository.save(job);
    }

    /**
     * 更新岗位信息
     * @param id 岗位ID
     * @param job 更新的岗位信息
     * @return 更新后的岗位
     */
    public Optional<Job> updateJob(Long id, Job job) {
        return jobRepository.findById(id).map(existingJob -> {
            existingJob.setTitle(job.getTitle());
            existingJob.setRequirements(job.getRequirements());
            existingJob.setResponsibilities(job.getResponsibilities());
            existingJob.setBenefits(job.getBenefits());
            existingJob.setLocation(job.getLocation());
            existingJob.setJobType(job.getJobType());
            existingJob.setExperienceLevel(job.getExperienceLevel());
            existingJob.setEducationRequirement(job.getEducationRequirement());
            existingJob.setSalaryMin(job.getSalaryMin());
            existingJob.setSalaryMax(job.getSalaryMax());
            existingJob.setCurrency(job.getCurrency());
            existingJob.setSkills(job.getSkills());
            existingJob.setIsUrgent(job.getIsUrgent());
            existingJob.setIsRemote(job.getIsRemote());
            existingJob.setDeadline(job.getDeadline());
            return jobRepository.save(existingJob);
        });
    }

    /**
     * 删除岗位
     * @param id 岗位ID
     * @return 是否删除成功
     */
    public boolean deleteJob(Long id) {
        if (jobRepository.existsById(id)) {
            jobRepository.deleteById(id);
            return true;
        }
        return false;
    }

    /**
     * 关闭岗位
     * @param id 岗位ID
     * @return 是否关闭成功
     */
    public boolean closeJob(Long id) {
        return jobRepository.findById(id).map(job -> {
            job.setStatus(Job.JobStatus.CLOSED);
            jobRepository.save(job);
            return true;
        }).orElse(false);
    }

    /**
     * 增加岗位申请数量
     * @param id 岗位ID
     * @return 是否更新成功
     */
    public boolean incrementApplicationCount(Long id) {
        return jobRepository.findById(id).map(job -> {
            job.setApplicationCount(job.getApplicationCount() + 1);
            jobRepository.save(job);
            return true;
        }).orElse(false);
    }

    /**
     * 综合搜索岗位
     * @param keyword 关键词（搜索标题和描述）
     * @param location 地点
     * @param jobType 工作类型
     * @param experienceLevel 经验要求
     * @param minSalary 最低薪资
     * @param maxSalary 最高薪资
     * @return 匹配的岗位列表
     */
    public List<Job> searchJobs(String keyword, String location, Job.JobType jobType, 
                               String experienceLevel, BigDecimal minSalary, BigDecimal maxSalary) {
        // 这里需要在Repository中实现复杂查询方法
        // 暂时返回所有活跃岗位，后续可以优化
        return jobRepository.findActiveJobs();
    }

    /**
     * 获取岗位统计信息
     * @return 岗位统计数据
     */
    public JobStatistics getJobStatistics() {
        long totalJobs = jobRepository.count();
        long activeJobs = jobRepository.findByStatus(Job.JobStatus.ACTIVE).size();
        long urgentJobs = jobRepository.findUrgentJobs().size();
        long remoteJobs = jobRepository.findRemoteJobs().size();
        
        return new JobStatistics(totalJobs, activeJobs, urgentJobs, remoteJobs);
    }

    // ==================== 推荐职位相关方法 ====================

    /**
     * 获取用户推荐职位列表（性能优化版本）
     * @param userId 用户ID（可选，未登录用户传null）
     * @param limit 推荐数量限制
     * @param companyId 公司ID（可选，用于过滤指定公司的职位）
     * @return 推荐职位列表
     */
    public List<RecommendedJobDTO> getRecommendedJobs(Long userId, int limit, Long companyId) {
        // 获取活跃职位（包含公司信息和申请状态）- 性能优化：一次查询获取所有需要的数据
        List<Object[]> jobsWithApplicationStatus = jobRepository.findByStatusWithCompanyAndApplicationStatus(Job.JobStatus.ACTIVE, userId);

        if (jobsWithApplicationStatus.isEmpty()) {
            return new ArrayList<>();
        }

        // 转换查询结果为Job对象和申请状态的映射
        List<JobWithApplicationStatus> activeJobs = jobsWithApplicationStatus.stream()
                .map(result -> new JobWithApplicationStatus((Job) result[0], (Boolean) result[1]))
                .collect(Collectors.toList());

        if (activeJobs.isEmpty()) {
            return new ArrayList<>();
        }

        // 如果指定了公司ID，先进行过滤
        if (companyId != null) {
            activeJobs = activeJobs.stream()
                    .filter(jobWithStatus -> jobWithStatus.getJob().getCompanyId() != null &&
                            jobWithStatus.getJob().getCompanyId().equals(companyId))
                    .collect(Collectors.toList());

            // 如果过滤后没有职位，返回空列表
            if (activeJobs.isEmpty()) {
                return new ArrayList<>();
            }
        }

        // 如果用户未登录，返回热门职位
        if (userId == null) {
            return getPopularJobRecommendationsWithStatus(activeJobs, limit);
        }

        // 获取用户信息
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            return getPopularJobRecommendationsWithStatus(activeJobs, limit);
        }

        User user = userOpt.get();

        // 获取用户职业目标
        Pageable goalPageable = PageRequest.of(0, 10);
        List<UserCareerGoal> userCareerGoals = userCareerGoalRepository.findByUserId(userId, goalPageable).getContent();

        // 获取用户技能评估
        List<UserSkillAssessment> userSkills = userSkillAssessmentRepository.findByUserId(userId);

        // 计算每个职位的匹配分数
        List<JobMatchResultWithStatus> jobMatches = activeJobs.stream()
                .map(jobWithStatus -> new JobMatchResultWithStatus(
                        jobWithStatus.getJob(),
                        calculateMatchScore(jobWithStatus.getJob(), user, userCareerGoals, userSkills),
                        jobWithStatus.getIsApplied()))
                .sorted((a, b) -> Integer.compare(b.getMatchScore(), a.getMatchScore()))
                .limit(limit)
                .collect(Collectors.toList());

        // 转换为DTO（包含申请状态）
        return jobMatches.stream()
                .map(match -> RecommendedJobDTO.fromJob(match.getJob(), match.getMatchScore(), match.getIsApplied()))
                .collect(Collectors.toList());
    }

    /**
     * 获取用户推荐职位列表（向后兼容的重载方法）
     * @param userId 用户ID（可选，未登录用户传null）
     * @param limit 推荐数量限制
     * @return 推荐职位列表
     */
    public List<RecommendedJobDTO> getRecommendedJobs(Long userId, int limit) {
        // 调用新方法，不指定公司ID
        return getRecommendedJobs(userId, limit, null);
    }

    /**
     * 获取热门职位推荐（用于未登录用户）
     * @param jobs 职位列表
     * @param limit 限制数量
     * @return 推荐职位列表
     */
    private List<RecommendedJobDTO> getPopularJobRecommendations(List<Job> jobs, int limit) {
        return jobs.stream()
                .sorted((a, b) -> {
                    // 按申请数量和浏览数量排序
                    int scoreA = (a.getApplicationCount() != null ? a.getApplicationCount() : 0) * 2 +
                                (a.getViewCount() != null ? a.getViewCount() : 0);
                    int scoreB = (b.getApplicationCount() != null ? b.getApplicationCount() : 0) * 2 +
                                (b.getViewCount() != null ? b.getViewCount() : 0);
                    return Integer.compare(scoreB, scoreA);
                })
                .limit(limit)
                .map(job -> RecommendedJobDTO.fromJob(job, generateRandomMatchScore()))
                .collect(Collectors.toList());
    }

    /**
     * 获取热门职位推荐（包含申请状态的版本）
     * @param jobsWithStatus 职位和申请状态列表
     * @param limit 限制数量
     * @return 推荐职位列表
     */
    private List<RecommendedJobDTO> getPopularJobRecommendationsWithStatus(List<JobWithApplicationStatus> jobsWithStatus, int limit) {
        return jobsWithStatus.stream()
                .sorted((a, b) -> {
                    // 按申请数量和浏览数量排序
                    Job jobA = a.getJob();
                    Job jobB = b.getJob();
                    int scoreA = (jobA.getApplicationCount() != null ? jobA.getApplicationCount() : 0) * 2 +
                                (jobA.getViewCount() != null ? jobA.getViewCount() : 0);
                    int scoreB = (jobB.getApplicationCount() != null ? jobB.getApplicationCount() : 0) * 2 +
                                (jobB.getViewCount() != null ? jobB.getViewCount() : 0);
                    return Integer.compare(scoreB, scoreA);
                })
                .limit(limit)
                .map(jobWithStatus -> RecommendedJobDTO.fromJob(jobWithStatus.getJob(), generateRandomMatchScore(), jobWithStatus.getIsApplied()))
                .collect(Collectors.toList());
    }

    /**
     * 计算职位匹配分数
     * @param job 职位信息
     * @param user 用户信息
     * @param userCareerGoals 用户职业目标
     * @param userSkills 用户技能评估
     * @return 匹配分数（0-100）
     */
    private Integer calculateMatchScore(Job job, User user, List<UserCareerGoal> userCareerGoals,
                                     List<UserSkillAssessment> userSkills) {
        int totalScore = 0;

        // 1. 职业目标匹配（权重40%）
        int careerGoalScore = calculateCareerGoalMatch(job, userCareerGoals);
        totalScore += (int) (careerGoalScore * 0.4);

        // 2. 技能匹配（权重35%）
        int skillScore = calculateSkillMatch(job, userSkills);
        totalScore += (int) (skillScore * 0.35);

        // 3. 地理位置匹配（权重15%）
        int locationScore = calculateLocationMatch(job, user);
        totalScore += (int) (locationScore * 0.15);

        // 4. 薪资匹配（权重10%）
        int salaryScore = calculateSalaryMatch(job, userCareerGoals);
        totalScore += (int) (salaryScore * 0.1);

        // 确保分数在合理范围内
        return Math.min(Math.max(totalScore, 60), 95); // 最低60分，最高95分
    }

    /**
     * 计算职业目标匹配度
     * @param job 职位信息
     * @param userCareerGoals 用户职业目标
     * @return 匹配分数（0-100）
     */
    private int calculateCareerGoalMatch(Job job, List<UserCareerGoal> userCareerGoals) {
        if (userCareerGoals.isEmpty()) {
            return 70; // 默认分数
        }

        // 简化实现：基于职位标题关键词匹配
        String jobTitle = job.getTitle().toLowerCase();

        for (UserCareerGoal careerGoal : userCareerGoals) {
            if (careerGoal.getCareerGoal() != null) {
                String goalTitle = careerGoal.getCareerGoal().getName().toLowerCase();
                if (jobTitle.contains("java") && goalTitle.contains("java")) {
                    return 90;
                }
                if (jobTitle.contains("前端") && goalTitle.contains("前端")) {
                    return 90;
                }
                if (jobTitle.contains("后端") && goalTitle.contains("后端")) {
                    return 90;
                }
                if (jobTitle.contains("全栈") && goalTitle.contains("全栈")) {
                    return 85;
                }
            }
        }

        return 75; // 默认匹配度
    }

    /**
     * 计算技能匹配度
     * @param job 职位信息
     * @param userSkills 用户技能评估
     * @return 匹配分数（0-100）
     */
    private int calculateSkillMatch(Job job, List<UserSkillAssessment> userSkills) {
        if (userSkills.isEmpty()) {
            return 70; // 默认分数
        }

        // 解析职位技能要求
        List<String> jobRequirements = parseJsonToStringList(job.getRequirements());
        if (jobRequirements.isEmpty()) {
            return 75;
        }

        // 获取用户技能列表
        Set<String> userSkillNames = userSkills.stream()
                .map(skill -> skill.getSkillName().toLowerCase())
                .collect(Collectors.toSet());

        // 计算匹配的技能数量
        long matchedSkills = jobRequirements.stream()
                .map(String::toLowerCase)
                .filter(userSkillNames::contains)
                .count();

        // 计算匹配度
        double matchRate = (double) matchedSkills / jobRequirements.size();
        return (int) (matchRate * 100);
    }

    /**
     * 计算地理位置匹配度
     * @param job 职位信息
     * @param user 用户信息
     * @return 匹配分数（0-100）
     */
    private int calculateLocationMatch(Job job, User user) {
        // 简化实现：如果是远程工作，给高分
        if (job.getIsRemote() != null && job.getIsRemote()) {
            return 100;
        }

        // 如果用户没有设置位置偏好，给默认分数
        return 80;
    }

    /**
     * 计算薪资匹配度
     * @param job 职位信息
     * @param userCareerGoals 用户职业目标
     * @return 匹配分数（0-100）
     */
    private int calculateSalaryMatch(Job job, List<UserCareerGoal> userCareerGoals) {
        // 简化实现：根据薪资范围给分
        if (job.getSalaryMax() != null) {
            int maxSalary = job.getSalaryMax().intValue();
            if (maxSalary >= 40000) {
                return 90;
            } else if (maxSalary >= 25000) {
                return 80;
            } else if (maxSalary >= 15000) {
                return 70;
            }
        }

        return 75; // 默认分数
    }

    /**
     * 解析JSON字符串为字符串列表
     * @param jsonString JSON字符串
     * @return 字符串列表
     */
    private List<String> parseJsonToStringList(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(jsonString, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 生成随机匹配分数（用于未登录用户）
     * @return 随机匹配分数
     */
    private Integer generateRandomMatchScore() {
        Random random = new Random();
        return 70 + random.nextInt(26); // 70-95之间的随机数
    }

    /**
     * 职位匹配结果内部类
     */
    private static class JobMatchResult {
        private final Job job;
        private final Integer matchScore;

        public JobMatchResult(Job job, Integer matchScore) {
            this.job = job;
            this.matchScore = matchScore;
        }

        public Job getJob() {
            return job;
        }

        public Integer getMatchScore() {
            return matchScore;
        }
    }

    /**
     * 岗位统计信息内部类
     */
    public static class JobStatistics {
        private final long totalJobs;
        private final long activeJobs;
        private final long urgentJobs;
        private final long remoteJobs;

        public JobStatistics(long totalJobs, long activeJobs, long urgentJobs, long remoteJobs) {
            this.totalJobs = totalJobs;
            this.activeJobs = activeJobs;
            this.urgentJobs = urgentJobs;
            this.remoteJobs = remoteJobs;
        }

        // Getters
        public long getTotalJobs() { return totalJobs; }
        public long getActiveJobs() { return activeJobs; }
        public long getUrgentJobs() { return urgentJobs; }
        public long getRemoteJobs() { return remoteJobs; }
    }

    /**
     * 内部类：职位和申请状态的组合对象
     * 用于性能优化，避免多次查询数据库
     */
    private static class JobWithApplicationStatus {
        private final Job job;
        private final Boolean isApplied;

        public JobWithApplicationStatus(Job job, Boolean isApplied) {
            this.job = job;
            this.isApplied = isApplied != null ? isApplied : false;
        }

        public Job getJob() {
            return job;
        }

        public Boolean getIsApplied() {
            return isApplied;
        }
    }

    /**
     * 内部类：职位匹配结果和申请状态的组合对象
     * 用于性能优化的推荐算法
     */
    private static class JobMatchResultWithStatus {
        private final Job job;
        private final Integer matchScore;
        private final Boolean isApplied;

        public JobMatchResultWithStatus(Job job, Integer matchScore, Boolean isApplied) {
            this.job = job;
            this.matchScore = matchScore;
            this.isApplied = isApplied != null ? isApplied : false;
        }

        public Job getJob() {
            return job;
        }

        public Integer getMatchScore() {
            return matchScore;
        }

        public Boolean getIsApplied() {
            return isApplied;
        }
    }
}
