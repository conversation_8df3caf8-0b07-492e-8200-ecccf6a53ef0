/**
 * 动态学习路径相关类型定义
 * 支持个性化学习路径生成、管理和跟踪
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */

import { AtomicSkill, MasteryLevel } from './atomicSkill';

export type UUID = string;
export type Timestamp = string;

/**
 * 路径类型
 */
export enum PathType {
  CAREER_ORIENTED = 'CAREER_ORIENTED', // 职业导向
  SKILL_FOCUSED = 'SKILL_FOCUSED',     // 技能专注
  PROJECT_BASED = 'PROJECT_BASED',     // 项目驱动
  CUSTOM = 'CUSTOM'                    // 自定义
}

/**
 * 学习风格
 */
export enum LearningStyle {
  THEORETICAL = 'THEORETICAL',         // 理论型
  PRACTICAL = 'PRACTICAL',             // 实践型
  PROJECT_DRIVEN = 'PROJECT_DRIVEN',   // 项目驱动型
  MIXED = 'MIXED'                      // 混合型
}

/**
 * 难度偏好
 */
export enum DifficultyPreference {
  GRADUAL = 'GRADUAL',                 // 渐进式
  CHALLENGING = 'CHALLENGING',         // 挑战式
  MIXED = 'MIXED'                      // 混合式
}

/**
 * 路径状态
 */
export enum PathStatus {
  DRAFT = 'DRAFT',                     // 草稿
  ACTIVE = 'ACTIVE',                   // 进行中
  PAUSED = 'PAUSED',                   // 暂停
  COMPLETED = 'COMPLETED',             // 已完成
  ABANDONED = 'ABANDONED'              // 已放弃
}

/**
 * 步骤类型
 */
export enum StepType {
  LEARN = 'LEARN',                     // 学习
  PRACTICE = 'PRACTICE',               // 练习
  ASSESS = 'ASSESS',                   // 评估
  PROJECT = 'PROJECT',                 // 项目
  REVIEW = 'REVIEW'                    // 复习
}

/**
 * 步骤状态
 */
export enum StepStatus {
  NOT_STARTED = 'NOT_STARTED',         // 未开始
  IN_PROGRESS = 'IN_PROGRESS',         // 进行中
  COMPLETED = 'COMPLETED',             // 已完成
  SKIPPED = 'SKIPPED'                  // 已跳过
}

/**
 * 个性化因子
 */
export interface PersonalizationFactors {
  // 用户基本信息
  userId: number;
  currentSkillLevel: number; // 1-5级
  learningStyle: LearningStyle;
  availableTimePerWeek: number; // 小时
  preferredLearningPace: 'FAST' | 'NORMAL' | 'SLOW';
  hasProgrammingExperience: boolean;
  
  // 学习偏好
  difficultyPreference: DifficultyPreference;
  preferredContentTypes: string[]; // ['video', 'text', 'interactive', 'project']
  learningGoals: string[];
  
  // 历史数据
  totalSkillsMastered: number;
  averageCompletionRate: number;
  preferredDifficulty: string;
  strongCategories: string[];
  weakCategories: string[];
  
  // 时间约束
  targetCompletionDate?: Timestamp;
  dailyStudyTime?: number; // 分钟
  weeklySchedule?: WeeklySchedule;
  
  // 动机和目标
  careerGoals: string[];
  motivationLevel: number; // 1-10
  learningMotivation: string;
}

/**
 * 周学习计划
 */
export interface WeeklySchedule {
  monday?: number;    // 分钟
  tuesday?: number;
  wednesday?: number;
  thursday?: number;
  friday?: number;
  saturday?: number;
  sunday?: number;
}

/**
 * 动态学习路径
 */
export interface DynamicLearningPath {
  id: number;
  userId: number;
  careerGoalId?: number;
  basePathId?: number;
  
  // 路径基本信息
  name: string;
  description?: string;
  pathType: PathType;
  
  // 个性化参数
  personalizationFactors?: PersonalizationFactors;
  learningStyle: LearningStyle;
  difficultyPreference: DifficultyPreference;
  timeConstraint?: number; // 周
  
  // 路径统计
  totalSkills: number;
  estimatedHours: number;
  completionRate: number;
  currentStep: number;
  
  // 算法信息
  generationAlgorithm: string;
  generationParams?: any;
  qualityScore: number; // 0-1
  
  // 状态管理
  status: PathStatus;
  isTemplate: boolean;
  
  // 时间戳
  createdAt: Timestamp;
  updatedAt: Timestamp;
  startedAt?: Timestamp;
  completedAt?: Timestamp;
  
  // 关联数据
  steps?: DynamicPathStep[];
  progress?: PathProgress;
}

/**
 * 动态路径步骤
 */
export interface DynamicPathStep {
  id: number;
  pathId: number;
  atomicSkillId: number;
  atomicSkill?: AtomicSkill;
  
  // 步骤信息
  stepOrder: number;
  stepType: StepType;
  estimatedHours: number;
  
  // 个性化调整
  difficultyAdjustment: number; // 调整系数
  priorityWeight: number;
  personalizationReason?: string;
  
  // 学习资源
  recommendedResources?: any[];
  alternativeResources?: any[];
  
  // 进度跟踪
  status: StepStatus;
  progressPercentage: number;
  completionScore?: number;
  
  // 时间记录
  startedAt?: Timestamp;
  completedAt?: Timestamp;
  actualHours?: number;
  
  // 反馈和调整
  userFeedback?: any;
  systemFeedback?: any;
  adjustmentHistory?: any[];
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * 路径进度
 */
export interface PathProgress {
  pathId: number;
  userId: number;
  
  // 整体进度
  overallProgress: number; // 0-100
  completedSteps: number;
  totalSteps: number;
  
  // 时间统计
  totalTimeSpent: number; // 小时
  averageSessionTime: number; // 分钟
  studyStreak: number; // 连续学习天数
  
  // 技能掌握
  skillsMastered: number;
  averageMasteryScore: number;
  masteryDistribution: Record<MasteryLevel, number>;
  
  // 学习效率
  learningVelocity: number; // 技能/周
  efficiencyScore: number; // 0-1
  adaptationScore: number; // 路径适应性分数
  
  // 里程碑
  milestonesAchieved: Milestone[];
  nextMilestone?: Milestone;
  
  // 预测
  estimatedCompletionDate: Timestamp;
  confidenceInterval: number; // 预测置信度
  
  lastUpdated: Timestamp;
}

/**
 * 里程碑
 */
export interface Milestone {
  id: string;
  name: string;
  description: string;
  type: 'skill' | 'project' | 'assessment' | 'time';
  targetValue: number;
  currentValue: number;
  isAchieved: boolean;
  achievedAt?: Timestamp;
  reward?: string;
}

/**
 * 路径生成请求
 */
export interface DynamicPathGenerationRequest {
  userId: number;
  careerGoalId?: number;
  basePathId?: number;
  pathType: PathType;
  timeConstraint?: number;
  customRequirements?: string[];
  excludeSkillIds?: number[];
  prioritySkillIds?: number[];
  generationOptions?: PathGenerationOptions;
}

/**
 * 路径生成选项
 */
export interface PathGenerationOptions {
  algorithmVersion?: string;
  includeProjects?: boolean;
  includeAssessments?: boolean;
  adaptiveDifficulty?: boolean;
  realTimeAdjustment?: boolean;
  collaborativeLearning?: boolean;
  gamificationElements?: boolean;
}

/**
 * 路径推荐
 */
export interface PathRecommendation {
  path: DynamicLearningPath;
  recommendationScore: number; // 0-1
  matchingFactors: string[];
  estimatedSuccess: number; // 0-1
  alternativePaths?: DynamicLearningPath[];
  customizationSuggestions?: string[];
}

/**
 * 路径分析报告
 */
export interface PathAnalysisReport {
  pathId: number;
  userId: number;
  
  // 完成情况分析
  completionAnalysis: {
    overallCompletion: number;
    skillCompletion: Record<string, number>;
    timeEfficiency: number;
    difficultyProgression: number[];
  };
  
  // 学习模式分析
  learningPatterns: {
    preferredStudyTimes: number[]; // 小时
    sessionDuration: number;
    learningFrequency: number;
    difficultyPreference: string;
  };
  
  // 技能发展分析
  skillDevelopment: {
    strongAreas: string[];
    improvementAreas: string[];
    masteryProgression: Record<string, number[]>;
    skillTransferability: number;
  };
  
  // 路径优化建议
  optimizationSuggestions: {
    sequenceAdjustments: string[];
    resourceRecommendations: string[];
    difficultyAdjustments: string[];
    timeAllocationSuggestions: string[];
  };
  
  // 预测和建议
  predictions: {
    completionProbability: number;
    estimatedCompletionTime: number;
    successFactors: string[];
    riskFactors: string[];
  };
  
  generatedAt: Timestamp;
}

/**
 * 路径调整请求
 */
export interface PathAdjustmentRequest {
  pathId: number;
  userId: number;
  adjustmentType: 'difficulty' | 'sequence' | 'content' | 'timeline';
  adjustmentReason: string;
  specificChanges?: {
    addSkills?: number[];
    removeSkills?: number[];
    reorderSteps?: { stepId: number; newOrder: number }[];
    adjustDifficulty?: { stepId: number; adjustment: number }[];
  };
  userFeedback?: string;
}

/**
 * 学习会话
 */
export interface LearningSession {
  id: string;
  userId: number;
  pathId: number;
  stepId: number;
  
  // 会话信息
  startTime: Timestamp;
  endTime?: Timestamp;
  duration?: number; // 分钟
  
  // 学习内容
  contentType: string;
  contentId: string;
  skillsPracticed: number[];
  
  // 会话结果
  completionStatus: 'completed' | 'partial' | 'abandoned';
  progressMade: number; // 0-100
  skillsImproved: Record<number, number>; // skillId -> improvement
  
  // 用户反馈
  difficultyRating?: number; // 1-5
  engagementRating?: number; // 1-5
  satisfactionRating?: number; // 1-5
  feedback?: string;
  
  // 系统分析
  focusScore?: number; // 0-1
  efficiencyScore?: number; // 0-1
  strugglingAreas?: string[];
  
  createdAt: Timestamp;
}
