import { apiService } from './ApiService';

/**
 * 动态学习路径服务
 * 提供个性化学习路径生成和推荐功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
export class DynamicLearningPathService {
  
  /**
   * 生成个性化学习路径
   * @param userId 用户ID
   * @param targetJobId 目标岗位ID
   * @returns 个性化学习路径生成结果
   */
  async generatePersonalizedPath(userId: number, targetJobId: number) {
    try {
      console.log('🎯 生成个性化学习路径:', { userId, targetJobId });
      
      const response = await apiService.post('/personalized-paths/generate', null, {
        userId,
        targetJobId
      });
      
      console.log('✅ 个性化学习路径生成响应:', response);
      
      if (response.code === 20000) {
        return {
          success: true,
          data: response.data,
          message: response.message
        };
      } else {
        throw new Error(response.message || '生成个性化学习路径失败');
      }
    } catch (error) {
      console.error('生成个性化学习路径失败:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '生成个性化学习路径失败'
      };
    }
  }

  /**
   * 获取用户个性化推荐
   * @param userId 用户ID
   * @param targetJobId 目标岗位ID（可选）
   * @param limit 推荐数量限制
   * @returns 个性化推荐结果
   */
  async getUserPersonalizedRecommendations(userId: number, targetJobId?: number, limit: number = 10) {
    try {
      console.log('🔍 获取用户个性化推荐:', { userId, targetJobId, limit });
      
      // 构建查询参数
      const params: any = { limit };
      if (targetJobId) {
        params.targetJobId = targetJobId;
      }
      
      const response = await apiService.get(`/personalized-paths/user/${userId}`, params);
      
      console.log('📡 个性化推荐响应:', response);
      
      if (response.code === 20000) {
        return {
          success: true,
          data: response.data,
          message: response.message
        };
      } else {
        throw new Error(response.message || '获取个性化推荐失败');
      }
    } catch (error) {
      console.error('获取个性化推荐失败:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取个性化推荐失败'
      };
    }
  }

  /**
   * 获取技能学习路径
   * @param skillId 技能ID
   * @returns 技能学习路径
   */
  async getSkillLearningPath(skillId: number) {
    try {
      console.log('🛤️ 获取技能学习路径:', { skillId });
      
      const response = await apiService.get(`/skill-graph/learning-path/${skillId}`);
      
      console.log('📊 技能学习路径响应:', response);
      
      if (response.code === 20000) {
        return {
          success: true,
          data: response.data,
          message: response.message
        };
      } else {
        throw new Error(response.message || '获取技能学习路径失败');
      }
    } catch (error) {
      console.error('获取技能学习路径失败:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取技能学习路径失败'
      };
    }
  }

  /**
   * 获取相关技能推荐
   * @param skillId 技能ID
   * @param limit 推荐数量限制
   * @returns 相关技能推荐
   */
  async getRelatedSkillRecommendations(skillId: number, limit: number = 5) {
    try {
      console.log('🔗 获取相关技能推荐:', { skillId, limit });
      
      const response = await apiService.get(`/skill-graph/recommendations/${skillId}`, { limit });
      
      console.log('🎯 相关技能推荐响应:', response);
      
      if (response.code === 20000) {
        return {
          success: true,
          data: response.data,
          message: response.message
        };
      } else {
        throw new Error(response.message || '获取相关技能推荐失败');
      }
    } catch (error) {
      console.error('获取相关技能推荐失败:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '获取相关技能推荐失败'
      };
    }
  }

  /**
   * 分析技能重要性
   * @param skillId 技能ID
   * @returns 技能重要性分析结果
   */
  async analyzeSkillImportance(skillId: number) {
    try {
      console.log('📈 分析技能重要性:', { skillId });
      
      const response = await apiService.get(`/skill-graph/importance/${skillId}`);
      
      console.log('📊 技能重要性分析响应:', response);
      
      if (response.code === 20000) {
        return {
          success: true,
          data: response.data,
          message: response.message
        };
      } else {
        throw new Error(response.message || '分析技能重要性失败');
      }
    } catch (error) {
      console.error('分析技能重要性失败:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '分析技能重要性失败'
      };
    }
  }

  /**
   * 查找技能路径
   * @param sourceSkillId 源技能ID
   * @param targetSkillId 目标技能ID
   * @returns 技能路径
   */
  async findSkillPath(sourceSkillId: number, targetSkillId: number) {
    try {
      console.log('🔍 查找技能路径:', { sourceSkillId, targetSkillId });
      
      const response = await apiService.get('/skill-graph/path', {
        sourceSkillId,
        targetSkillId
      });
      
      console.log('🛤️ 技能路径响应:', response);
      
      if (response.code === 20000) {
        return {
          success: true,
          data: response.data,
          message: response.message
        };
      } else {
        throw new Error(response.message || '查找技能路径失败');
      }
    } catch (error) {
      console.error('查找技能路径失败:', error);
      return {
        success: false,
        data: null,
        message: error instanceof Error ? error.message : '查找技能路径失败'
      };
    }
  }
}

// 创建并导出服务实例
export const dynamicLearningPathService = new DynamicLearningPathService();
export default dynamicLearningPathService;
