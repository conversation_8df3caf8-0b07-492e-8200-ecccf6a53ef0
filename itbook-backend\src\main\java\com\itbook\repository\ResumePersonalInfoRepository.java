package com.itbook.repository;

import com.itbook.entity.ResumePersonalInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 简历个人信息Repository
 * 
 * <AUTHOR> Team
 * @since 2025-07-17
 */
@Repository
public interface ResumePersonalInfoRepository extends JpaRepository<ResumePersonalInfo, Long> {

    /**
     * 根据简历ID查找个人信息
     */
    Optional<ResumePersonalInfo> findByResumeId(Long resumeId);

    /**
     * 根据简历ID删除个人信息
     */
    void deleteByResumeId(Long resumeId);

    /**
     * 检查简历是否存在个人信息
     */
    @Query("SELECT COUNT(p) > 0 FROM ResumePersonalInfo p WHERE p.resumeId = :resumeId")
    boolean existsByResumeId(@Param("resumeId") Long resumeId);
}
