package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itbook.dto.SkillTag;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 学习路径实体类
 * 
 * <AUTHOR> Team
 * @since 2025-07-09
 */
@Entity
@Table(name = "learning_path")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class LearningPath {

    /**
     * 路径类型枚举
     */
    public enum PathType {
        STANDARD,      // 标准路径（每个岗位的官方标准学习路径）
        PERSONALIZED,  // 个性化推荐路径（基于用户画像生成的推荐路径）
        TEMPLATE,      // 模板路径（可复用的路径模板）
        CUSTOM,        // 自定义路径（用户自己创建的路径）
        STRUCTURED     // 结构化路径（系统生成的结构化学习路径）
    }

    /**
     * 难度级别枚举
     */
    public enum DifficultyLevel {
        BEGINNER,      // 初级
        INTERMEDIATE,  // 中级
        ADVANCED       // 高级
    }

    /**
     * 路径状态枚举
     */
    public enum Status {
        DRAFT,         // 草稿
        PUBLISHED,     // 已发布
        ARCHIVED       // 已归档
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 路径名称
     */
    @Column(name = "name", nullable = false, length = 200)
    private String name;

    /**
     * 路径描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 目标岗位ID
     */
    @Column(name = "target_job_id")
    private Long targetJobId;

    /**
     * 目标岗位（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "target_job_id", insertable = false, updatable = false)
    private Job targetJob;

    /**
     * 创建者ID
     */
    @Column(name = "creator_id")
    private Long creatorId;

    /**
     * 创建者（关联查询）
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "creator_id", insertable = false, updatable = false)
    private User creator;

    /**
     * 路径类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "path_type", nullable = false)
    private PathType pathType = PathType.STANDARD;

    /**
     * 难度级别
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "difficulty_level", nullable = false)
    private DifficultyLevel difficultyLevel = DifficultyLevel.BEGINNER;

    /**
     * 预计完成时间（小时）
     */
    @Column(name = "estimated_hours")
    private Integer estimatedHours;

    /**
     * 学习目标（JSON格式存储）
     */
    @Column(name = "learning_objectives", columnDefinition = "JSON")
    private String learningObjectivesJson;

    /**
     * 路径标签（JSON格式存储）
     */
    @Column(name = "tags", columnDefinition = "JSON")
    private String tagsJson;

    /**
     * 技能标签（JSON格式存储）
     * 存储该学习路径涵盖的技能信息，包括技能名称、目标等级、权重等
     */
    @Column(name = "skill_tags", columnDefinition = "JSON")
    private String skillTagsJson;

    /**
     * 路径状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.DRAFT;

    // 移除isRecommended字段，使用pathType=PERSONALIZED来标识个性化推荐路径

    /**
     * 是否为模板路径
     */
    @Column(name = "is_template")
    private Boolean isTemplate = false;

    /**
     * 路径评分
     */
    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;

    /**
     * 评价数量
     */
    @Column(name = "review_count")
    private Integer reviewCount = 0;

    /**
     * 学习人数
     */
    @Column(name = "learner_count")
    private Integer learnerCount = 0;

    /**
     * 完成人数
     */
    @Column(name = "completion_count")
    private Integer completionCount = 0;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 学习路径步骤（一对多关系）
     */
    @OneToMany(mappedBy = "learningPath", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("stepOrder ASC")
    @JsonIgnore // 列表页面不需要显示步骤详情
    private List<LearningPathStep> steps = new ArrayList<>();

    /**
     * 用户学习进度（一对多关系）
     */
    @OneToMany(mappedBy = "learningPath", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore // 列表页面不需要显示用户进度详情
    private List<UserLearningPathProgress> userProgresses = new ArrayList<>();

    // JSON字段的辅助方法
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取学习目标列表
     */
    @Transient
    public List<String> getLearningObjectives() {
        if (learningObjectivesJson == null || learningObjectivesJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(learningObjectivesJson, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            return new ArrayList<>();
        }
    }

    /**
     * 设置学习目标列表
     */
    public void setLearningObjectives(List<String> learningObjectives) {
        try {
            this.learningObjectivesJson = objectMapper.writeValueAsString(learningObjectives);
        } catch (JsonProcessingException e) {
            this.learningObjectivesJson = "[]";
        }
    }

    /**
     * 获取标签列表
     */
    @Transient
    public List<String> getTags() {
        if (tagsJson == null || tagsJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(tagsJson, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            return new ArrayList<>();
        }
    }

    /**
     * 设置标签列表
     */
    public void setTags(List<String> tags) {
        try {
            this.tagsJson = objectMapper.writeValueAsString(tags);
        } catch (JsonProcessingException e) {
            this.tagsJson = "[]";
        }
    }

    /**
     * 获取技能标签列表
     */
    @Transient
    public List<SkillTag> getSkillTags() {
        if (skillTagsJson == null || skillTagsJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(skillTagsJson, new TypeReference<List<SkillTag>>() {});
        } catch (JsonProcessingException e) {
            return new ArrayList<>();
        }
    }

    /**
     * 设置技能标签列表
     */
    public void setSkillTags(List<SkillTag> skillTags) {
        try {
            this.skillTagsJson = objectMapper.writeValueAsString(skillTags);
        } catch (JsonProcessingException e) {
            this.skillTagsJson = "[]";
        }
    }

    /**
     * 计算完成率
     */
    @Transient
    public BigDecimal getCompletionRate() {
        if (learnerCount == null || learnerCount == 0) {
            return BigDecimal.ZERO;
        }
        if (completionCount == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(completionCount)
                .divide(BigDecimal.valueOf(learnerCount), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    // 构造函数
    public LearningPath() {}

    public LearningPath(String name, String description, Long targetJobId, Long creatorId) {
        this.name = name;
        this.description = description;
        this.targetJobId = targetJobId;
        this.creatorId = creatorId;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Long getTargetJobId() { return targetJobId; }
    public void setTargetJobId(Long targetJobId) { this.targetJobId = targetJobId; }

    public Job getTargetJob() { return targetJob; }
    public void setTargetJob(Job targetJob) { this.targetJob = targetJob; }

    public Long getCreatorId() { return creatorId; }
    public void setCreatorId(Long creatorId) { this.creatorId = creatorId; }

    public User getCreator() { return creator; }
    public void setCreator(User creator) { this.creator = creator; }

    public PathType getPathType() { return pathType; }
    public void setPathType(PathType pathType) { this.pathType = pathType; }

    public DifficultyLevel getDifficultyLevel() { return difficultyLevel; }
    public void setDifficultyLevel(DifficultyLevel difficultyLevel) { this.difficultyLevel = difficultyLevel; }

    public Integer getEstimatedHours() { return estimatedHours; }
    public void setEstimatedHours(Integer estimatedHours) { this.estimatedHours = estimatedHours; }

    public String getLearningObjectivesJson() { return learningObjectivesJson; }
    public void setLearningObjectivesJson(String learningObjectivesJson) { this.learningObjectivesJson = learningObjectivesJson; }

    public String getTagsJson() { return tagsJson; }
    public void setTagsJson(String tagsJson) { this.tagsJson = tagsJson; }

    public String getSkillTagsJson() { return skillTagsJson; }
    public void setSkillTagsJson(String skillTagsJson) { this.skillTagsJson = skillTagsJson; }

    public Status getStatus() { return status; }
    public void setStatus(Status status) { this.status = status; }

    // 移除isRecommended相关方法，使用pathType来判断是否为个性化推荐路径

    public Boolean getIsTemplate() { return isTemplate; }
    public void setIsTemplate(Boolean isTemplate) { this.isTemplate = isTemplate; }

    public BigDecimal getRating() { return rating; }
    public void setRating(BigDecimal rating) { this.rating = rating; }

    public Integer getReviewCount() { return reviewCount; }
    public void setReviewCount(Integer reviewCount) { this.reviewCount = reviewCount; }

    public Integer getLearnerCount() { return learnerCount; }
    public void setLearnerCount(Integer learnerCount) { this.learnerCount = learnerCount; }

    public Integer getCompletionCount() { return completionCount; }
    public void setCompletionCount(Integer completionCount) { this.completionCount = completionCount; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public List<LearningPathStep> getSteps() { return steps; }
    public void setSteps(List<LearningPathStep> steps) { this.steps = steps; }

    public List<UserLearningPathProgress> getUserProgresses() { return userProgresses; }
    public void setUserProgresses(List<UserLearningPathProgress> userProgresses) { this.userProgresses = userProgresses; }

    @Override
    public String toString() {
        return "LearningPath{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", pathType=" + pathType +
                ", difficultyLevel=" + difficultyLevel +
                ", status=" + status +
                ", createdAt=" + createdAt +
                '}';
    }
}
