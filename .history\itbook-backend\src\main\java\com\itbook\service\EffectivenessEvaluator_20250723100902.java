package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import com.itbook.service.LearningProgressTracker.LearningBehaviorAnalysis;
import com.itbook.service.FeedbackProcessor.FeedbackAnalysis;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习效果评估器
 * 
 * 核心功能：
 * 1. 评估用户学习成果和技能掌握情况
 * 2. 评估学习路径的有效性和质量
 * 3. 分析学习效率和投入产出比
 * 4. 决定是否需要重新生成学习路径
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EffectivenessEvaluator {

    private final UserStepProgressRepository stepProgressRepository;
    private final DynamicLearningPathRepository dynamicPathRepository;
    private final DynamicPathStepRepository dynamicStepRepository;
    private final LearningProgressTracker learningProgressTracker;
    private final FeedbackProcessor feedbackProcessor;

    /**
     * 评估维度枚举
     */
    public enum EvaluationDimension {
        LEARNING_OUTCOME,      // 学习成果
        PATH_QUALITY,          // 路径质量
        LEARNING_EFFICIENCY,   // 学习效率
        USER_SATISFACTION,     // 用户满意度
        SKILL_MASTERY,         // 技能掌握
        TIME_EFFECTIVENESS     // 时间有效性
    }

    /**
     * 评估等级枚举
     */
    public enum EffectivenessLevel {
        EXCELLENT,    // 优秀 (90-100%)
        GOOD,         // 良好 (75-89%)
        AVERAGE,      // 一般 (60-74%)
        POOR,         // 较差 (40-59%)
        VERY_POOR     // 很差 (<40%)
    }

    /**
     * 重新生成建议枚举
     */
    public enum RegenerationRecommendation {
        NOT_NEEDED,           // 不需要
        MINOR_ADJUSTMENT,     // 小幅调整
        MAJOR_ADJUSTMENT,     // 大幅调整
        COMPLETE_REGENERATION // 完全重新生成
    }

    /**
     * 学习效果评估结果
     */
    public static class EffectivenessEvaluation {
        private Long userId;
        private Long pathId;
        private LocalDateTime evaluationTime;
        
        // 整体评估
        private Double overallEffectiveness;      // 整体有效性 (0-1)
        private EffectivenessLevel effectivenessLevel;
        private String evaluationSummary;
        
        // 各维度评估
        private Map<EvaluationDimension, Double> dimensionScores;
        private Map<EvaluationDimension, String> dimensionAnalysis;
        
        // 学习成果分析
        private Double learningOutcomeScore;      // 学习成果得分
        private Integer skillsAcquired;           // 已掌握技能数
        private Integer totalSkills;              // 总技能数
        private Double skillMasteryRate;          // 技能掌握率
        
        // 路径质量分析
        private Double pathQualityScore;          // 路径质量得分
        private Double contentRelevance;          // 内容相关性
        private Double difficultyAppropriate;    // 难度适宜性
        private Double structureLogic;            // 结构逻辑性
        
        // 学习效率分析
        private Double learningEfficiencyScore;  // 学习效率得分
        private Double timeUtilization;          // 时间利用率
        private Double progressRate;              // 进度速率
        private Integer averageTimePerSkill;     // 平均每技能时间
        
        // 用户满意度分析
        private Double userSatisfactionScore;    // 用户满意度得分
        private Double feedbackRating;           // 反馈评分
        private Integer positiveComments;        // 积极评论数
        private Integer negativeComments;        // 消极评论数
        
        // 改进建议
        private List<String> strengths;          // 优势
        private List<String> weaknesses;         // 不足
        private List<String> improvements;       // 改进建议
        private RegenerationRecommendation regenerationRecommendation;
        private String regenerationReason;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public LocalDateTime getEvaluationTime() { return evaluationTime; }
        public void setEvaluationTime(LocalDateTime evaluationTime) { this.evaluationTime = evaluationTime; }
        
        public Double getOverallEffectiveness() { return overallEffectiveness; }
        public void setOverallEffectiveness(Double overallEffectiveness) { this.overallEffectiveness = overallEffectiveness; }
        
        public EffectivenessLevel getEffectivenessLevel() { return effectivenessLevel; }
        public void setEffectivenessLevel(EffectivenessLevel effectivenessLevel) { this.effectivenessLevel = effectivenessLevel; }
        
        public String getEvaluationSummary() { return evaluationSummary; }
        public void setEvaluationSummary(String evaluationSummary) { this.evaluationSummary = evaluationSummary; }
        
        public Map<EvaluationDimension, Double> getDimensionScores() { return dimensionScores; }
        public void setDimensionScores(Map<EvaluationDimension, Double> dimensionScores) { this.dimensionScores = dimensionScores; }
        
        public Map<EvaluationDimension, String> getDimensionAnalysis() { return dimensionAnalysis; }
        public void setDimensionAnalysis(Map<EvaluationDimension, String> dimensionAnalysis) { this.dimensionAnalysis = dimensionAnalysis; }
        
        public Double getLearningOutcomeScore() { return learningOutcomeScore; }
        public void setLearningOutcomeScore(Double learningOutcomeScore) { this.learningOutcomeScore = learningOutcomeScore; }
        
        public Integer getSkillsAcquired() { return skillsAcquired; }
        public void setSkillsAcquired(Integer skillsAcquired) { this.skillsAcquired = skillsAcquired; }
        
        public Integer getTotalSkills() { return totalSkills; }
        public void setTotalSkills(Integer totalSkills) { this.totalSkills = totalSkills; }
        
        public Double getSkillMasteryRate() { return skillMasteryRate; }
        public void setSkillMasteryRate(Double skillMasteryRate) { this.skillMasteryRate = skillMasteryRate; }
        
        public Double getPathQualityScore() { return pathQualityScore; }
        public void setPathQualityScore(Double pathQualityScore) { this.pathQualityScore = pathQualityScore; }
        
        public Double getContentRelevance() { return contentRelevance; }
        public void setContentRelevance(Double contentRelevance) { this.contentRelevance = contentRelevance; }
        
        public Double getDifficultyAppropriate() { return difficultyAppropriate; }
        public void setDifficultyAppropriate(Double difficultyAppropriate) { this.difficultyAppropriate = difficultyAppropriate; }
        
        public Double getStructureLogic() { return structureLogic; }
        public void setStructureLogic(Double structureLogic) { this.structureLogic = structureLogic; }
        
        public Double getLearningEfficiencyScore() { return learningEfficiencyScore; }
        public void setLearningEfficiencyScore(Double learningEfficiencyScore) { this.learningEfficiencyScore = learningEfficiencyScore; }
        
        public Double getTimeUtilization() { return timeUtilization; }
        public void setTimeUtilization(Double timeUtilization) { this.timeUtilization = timeUtilization; }
        
        public Double getProgressRate() { return progressRate; }
        public void setProgressRate(Double progressRate) { this.progressRate = progressRate; }
        
        public Integer getAverageTimePerSkill() { return averageTimePerSkill; }
        public void setAverageTimePerSkill(Integer averageTimePerSkill) { this.averageTimePerSkill = averageTimePerSkill; }
        
        public Double getUserSatisfactionScore() { return userSatisfactionScore; }
        public void setUserSatisfactionScore(Double userSatisfactionScore) { this.userSatisfactionScore = userSatisfactionScore; }
        
        public Double getFeedbackRating() { return feedbackRating; }
        public void setFeedbackRating(Double feedbackRating) { this.feedbackRating = feedbackRating; }
        
        public Integer getPositiveComments() { return positiveComments; }
        public void setPositiveComments(Integer positiveComments) { this.positiveComments = positiveComments; }
        
        public Integer getNegativeComments() { return negativeComments; }
        public void setNegativeComments(Integer negativeComments) { this.negativeComments = negativeComments; }
        
        public List<String> getStrengths() { return strengths; }
        public void setStrengths(List<String> strengths) { this.strengths = strengths; }
        
        public List<String> getWeaknesses() { return weaknesses; }
        public void setWeaknesses(List<String> weaknesses) { this.weaknesses = weaknesses; }
        
        public List<String> getImprovements() { return improvements; }
        public void setImprovements(List<String> improvements) { this.improvements = improvements; }
        
        public RegenerationRecommendation getRegenerationRecommendation() { return regenerationRecommendation; }
        public void setRegenerationRecommendation(RegenerationRecommendation regenerationRecommendation) { this.regenerationRecommendation = regenerationRecommendation; }
        
        public String getRegenerationReason() { return regenerationReason; }
        public void setRegenerationReason(String regenerationReason) { this.regenerationReason = regenerationReason; }
    }

    /**
     * 评估学习效果
     * 
     * @param userId 用户ID
     * @param pathId 路径ID
     * @return 评估结果
     */
    public EffectivenessEvaluation evaluateEffectiveness(Long userId, Long pathId) {
        log.info("📊 评估学习效果: userId={}, pathId={}", userId, pathId);

        try {
            EffectivenessEvaluation evaluation = new EffectivenessEvaluation();
            evaluation.setUserId(userId);
            evaluation.setPathId(pathId);
            evaluation.setEvaluationTime(LocalDateTime.now());

            // 获取相关数据
            LearningBehaviorAnalysis behaviorAnalysis = learningProgressTracker.analyzeLearningBehavior(userId, pathId);
            FeedbackAnalysis feedbackAnalysis = feedbackProcessor.analyzeFeedback(pathId);
            List<UserStepProgress> stepProgresses = getStepProgressesForPath(userId, pathId);

            // 评估各个维度
            evaluateLearningOutcome(evaluation, stepProgresses, behaviorAnalysis);
            evaluatePathQuality(evaluation, stepProgresses, feedbackAnalysis);
            evaluateLearningEfficiency(evaluation, stepProgresses, behaviorAnalysis);
            evaluateUserSatisfaction(evaluation, feedbackAnalysis);

            // 计算整体有效性
            calculateOverallEffectiveness(evaluation);

            // 生成改进建议和重新生成建议
            generateImprovementSuggestions(evaluation);
            determineRegenerationRecommendation(evaluation);

            log.info("✅ 学习效果评估完成: userId={}, pathId={}, effectiveness={}, level={}", 
                    userId, pathId, evaluation.getOverallEffectiveness(), evaluation.getEffectivenessLevel());

            return evaluation;

        } catch (Exception e) {
            log.error("❌ 学习效果评估失败: userId={}, pathId={}", userId, pathId, e);
            throw new RuntimeException("学习效果评估失败", e);
        }
    }

    /**
     * 批量评估多个路径的效果
     * 
     * @param userId 用户ID
     * @param pathIds 路径ID列表
     * @return 评估结果列表
     */
    public List<EffectivenessEvaluation> batchEvaluateEffectiveness(Long userId, List<Long> pathIds) {
        log.info("📊 批量评估学习效果: userId={}, pathCount={}", userId, pathIds.size());

        List<EffectivenessEvaluation> evaluations = new ArrayList<>();
        
        for (Long pathId : pathIds) {
            try {
                EffectivenessEvaluation evaluation = evaluateEffectiveness(userId, pathId);
                evaluations.add(evaluation);
            } catch (Exception e) {
                log.error("❌ 路径评估失败: userId={}, pathId={}", userId, pathId, e);
                // 继续评估其他路径
            }
        }

        log.info("✅ 批量学习效果评估完成: userId={}, successCount={}/{}", 
                userId, evaluations.size(), pathIds.size());

        return evaluations;
    }

    /**
     * 获取评估摘要统计
     * 
     * @param userId 用户ID
     * @return 评估摘要
     */
    public Map<String, Object> getEvaluationSummary(Long userId) {
        log.info("📈 获取评估摘要: userId={}", userId);

        try {
            Map<String, Object> summary = new HashMap<>();

            // 获取用户的所有路径（简化版本）
            List<Long> pathIds = Arrays.asList(1L, 2L, 3L); // 模拟数据
            List<EffectivenessEvaluation> evaluations = batchEvaluateEffectiveness(userId, pathIds);

            if (evaluations.isEmpty()) {
                summary.put("message", "暂无评估数据");
                return summary;
            }

            // 计算统计信息
            double avgEffectiveness = evaluations.stream()
                    .mapToDouble(EffectivenessEvaluation::getOverallEffectiveness)
                    .average()
                    .orElse(0.0);

            Map<EffectivenessLevel, Long> levelDistribution = evaluations.stream()
                    .collect(Collectors.groupingBy(EffectivenessEvaluation::getEffectivenessLevel, Collectors.counting()));

            Map<RegenerationRecommendation, Long> recommendationDistribution = evaluations.stream()
                    .collect(Collectors.groupingBy(EffectivenessEvaluation::getRegenerationRecommendation, Collectors.counting()));

            // 构建摘要
            summary.put("userId", userId);
            summary.put("totalPaths", evaluations.size());
            summary.put("averageEffectiveness", BigDecimal.valueOf(avgEffectiveness).setScale(3, RoundingMode.HALF_UP));
            summary.put("levelDistribution", levelDistribution);
            summary.put("recommendationDistribution", recommendationDistribution);
            summary.put("evaluationTime", LocalDateTime.now());

            log.info("✅ 评估摘要获取完成: userId={}, avgEffectiveness={}", userId, avgEffectiveness);

            return summary;

        } catch (Exception e) {
            log.error("❌ 评估摘要获取失败: userId={}", userId, e);
            throw new RuntimeException("评估摘要获取失败", e);
        }
    }

    /**
     * 评估学习成果
     */
    private void evaluateLearningOutcome(EffectivenessEvaluation evaluation,
                                        List<UserStepProgress> stepProgresses,
                                        LearningBehaviorAnalysis behaviorAnalysis) {
        // 计算技能掌握情况
        int totalSkills = stepProgresses.size();
        int skillsAcquired = (int) stepProgresses.stream()
                .filter(UserStepProgress::isCompleted)
                .count();

        double skillMasteryRate = totalSkills > 0 ? (double) skillsAcquired / totalSkills : 0.0;

        // 基于完成率和学习质量计算学习成果得分
        double avgCompletionQuality = stepProgresses.stream()
                .filter(UserStepProgress::isCompleted)
                .mapToDouble(p -> p.getCompletionPercentage().doubleValue())
                .average()
                .orElse(0.0) / 100.0;

        double learningOutcomeScore = (skillMasteryRate * 0.7) + (avgCompletionQuality * 0.3);

        evaluation.setTotalSkills(totalSkills);
        evaluation.setSkillsAcquired(skillsAcquired);
        evaluation.setSkillMasteryRate(skillMasteryRate);
        evaluation.setLearningOutcomeScore(learningOutcomeScore);
    }

    /**
     * 评估路径质量
     */
    private void evaluatePathQuality(EffectivenessEvaluation evaluation,
                                    List<UserStepProgress> stepProgresses,
                                    FeedbackAnalysis feedbackAnalysis) {
        // 内容相关性（基于用户反馈）
        double contentRelevance = feedbackAnalysis.getOverallSatisfaction() != null ?
                feedbackAnalysis.getOverallSatisfaction() : 0.5;

        // 难度适宜性（基于难度评分）
        double avgDifficultyRating = stepProgresses.stream()
                .filter(p -> p.getDifficultyRating() != null)
                .mapToInt(UserStepProgress::getDifficultyRating)
                .average()
                .orElse(3.0);

        // 理想难度为3.0，计算适宜性
        double difficultyAppropriate = 1.0 - Math.abs(avgDifficultyRating - 3.0) / 2.0;

        // 结构逻辑性（基于步骤完成的连续性）
        double structureLogic = calculateStructureLogic(stepProgresses);

        // 综合路径质量得分
        double pathQualityScore = (contentRelevance * 0.4) + (difficultyAppropriate * 0.3) + (structureLogic * 0.3);

        evaluation.setContentRelevance(contentRelevance);
        evaluation.setDifficultyAppropriate(difficultyAppropriate);
        evaluation.setStructureLogic(structureLogic);
        evaluation.setPathQualityScore(pathQualityScore);
    }

    /**
     * 评估学习效率
     */
    private void evaluateLearningEfficiency(EffectivenessEvaluation evaluation,
                                           List<UserStepProgress> stepProgresses,
                                           LearningBehaviorAnalysis behaviorAnalysis) {
        // 时间利用率（基于学习时间和完成情况）
        int totalStudyTime = stepProgresses.stream()
                .mapToInt(p -> p.getStudiedMinutes() != null ? p.getStudiedMinutes() : 0)
                .sum();

        int completedSteps = (int) stepProgresses.stream()
                .filter(UserStepProgress::isCompleted)
                .count();

        double timeUtilization = completedSteps > 0 && totalStudyTime > 0 ?
                Math.min(1.0, (double) completedSteps * 60 / totalStudyTime) : 0.0;

        // 进度速率（基于行为分析）
        double progressRate = behaviorAnalysis.getCompletionProbability();

        // 平均每技能时间
        int averageTimePerSkill = completedSteps > 0 ? totalStudyTime / completedSteps : 0;

        // 综合学习效率得分
        double learningEfficiencyScore = (timeUtilization * 0.5) + (progressRate * 0.5);

        evaluation.setTimeUtilization(timeUtilization);
        evaluation.setProgressRate(progressRate);
        evaluation.setAverageTimePerSkill(averageTimePerSkill);
        evaluation.setLearningEfficiencyScore(learningEfficiencyScore);
    }

    /**
     * 评估用户满意度
     */
    private void evaluateUserSatisfaction(EffectivenessEvaluation evaluation,
                                         FeedbackAnalysis feedbackAnalysis) {
        // 反馈评分
        double feedbackRating = feedbackAnalysis.getAverageRating() != null ?
                feedbackAnalysis.getAverageRating() / 5.0 : 0.5;

        // 积极/消极评论统计（简化版本）
        int positiveComments = 0;
        int negativeComments = 0;

        if (feedbackAnalysis.getSentimentDistribution() != null) {
            positiveComments = feedbackAnalysis.getSentimentDistribution()
                    .getOrDefault(FeedbackProcessor.FeedbackSentiment.POSITIVE, 0);
            negativeComments = feedbackAnalysis.getSentimentDistribution()
                    .getOrDefault(FeedbackProcessor.FeedbackSentiment.NEGATIVE, 0);
        }

        // 用户满意度得分
        double userSatisfactionScore = feedbackAnalysis.getOverallSatisfaction() != null ?
                feedbackAnalysis.getOverallSatisfaction() : 0.5;

        evaluation.setFeedbackRating(feedbackRating);
        evaluation.setPositiveComments(positiveComments);
        evaluation.setNegativeComments(negativeComments);
        evaluation.setUserSatisfactionScore(userSatisfactionScore);
    }

    /**
     * 计算整体有效性
     */
    private void calculateOverallEffectiveness(EffectivenessEvaluation evaluation) {
        // 各维度权重
        double learningOutcomeWeight = 0.35;
        double pathQualityWeight = 0.25;
        double learningEfficiencyWeight = 0.25;
        double userSatisfactionWeight = 0.15;

        // 计算加权平均
        double overallEffectiveness =
                (evaluation.getLearningOutcomeScore() * learningOutcomeWeight) +
                (evaluation.getPathQualityScore() * pathQualityWeight) +
                (evaluation.getLearningEfficiencyScore() * learningEfficiencyWeight) +
                (evaluation.getUserSatisfactionScore() * userSatisfactionWeight);

        evaluation.setOverallEffectiveness(overallEffectiveness);

        // 确定有效性等级
        EffectivenessLevel level;
        if (overallEffectiveness >= 0.9) {
            level = EffectivenessLevel.EXCELLENT;
        } else if (overallEffectiveness >= 0.75) {
            level = EffectivenessLevel.GOOD;
        } else if (overallEffectiveness >= 0.6) {
            level = EffectivenessLevel.AVERAGE;
        } else if (overallEffectiveness >= 0.4) {
            level = EffectivenessLevel.POOR;
        } else {
            level = EffectivenessLevel.VERY_POOR;
        }

        evaluation.setEffectivenessLevel(level);

        // 设置维度得分
        Map<EvaluationDimension, Double> dimensionScores = new HashMap<>();
        dimensionScores.put(EvaluationDimension.LEARNING_OUTCOME, evaluation.getLearningOutcomeScore());
        dimensionScores.put(EvaluationDimension.PATH_QUALITY, evaluation.getPathQualityScore());
        dimensionScores.put(EvaluationDimension.LEARNING_EFFICIENCY, evaluation.getLearningEfficiencyScore());
        dimensionScores.put(EvaluationDimension.USER_SATISFACTION, evaluation.getUserSatisfactionScore());
        evaluation.setDimensionScores(dimensionScores);

        // 生成评估摘要
        String summary = String.format("整体学习效果%s（%.1f%%），学习成果%.1f%%，路径质量%.1f%%，学习效率%.1f%%，用户满意度%.1f%%",
                getLevelDescription(level),
                overallEffectiveness * 100,
                evaluation.getLearningOutcomeScore() * 100,
                evaluation.getPathQualityScore() * 100,
                evaluation.getLearningEfficiencyScore() * 100,
                evaluation.getUserSatisfactionScore() * 100);

        evaluation.setEvaluationSummary(summary);
    }

    /**
     * 生成改进建议
     */
    private void generateImprovementSuggestions(EffectivenessEvaluation evaluation) {
        List<String> strengths = new ArrayList<>();
        List<String> weaknesses = new ArrayList<>();
        List<String> improvements = new ArrayList<>();

        // 分析各维度表现
        if (evaluation.getLearningOutcomeScore() >= 0.8) {
            strengths.add("学习成果优秀，技能掌握率高");
        } else if (evaluation.getLearningOutcomeScore() < 0.6) {
            weaknesses.add("学习成果有待提高，技能掌握率偏低");
            improvements.add("建议调整学习方法，增加练习和复习");
        }

        if (evaluation.getPathQualityScore() >= 0.8) {
            strengths.add("学习路径质量良好，内容相关性高");
        } else if (evaluation.getPathQualityScore() < 0.6) {
            weaknesses.add("学习路径质量需要改进");
            improvements.add("建议优化学习内容和难度设置");
        }

        if (evaluation.getLearningEfficiencyScore() >= 0.8) {
            strengths.add("学习效率高，时间利用充分");
        } else if (evaluation.getLearningEfficiencyScore() < 0.6) {
            weaknesses.add("学习效率偏低，时间利用不充分");
            improvements.add("建议优化学习计划，提高学习专注度");
        }

        if (evaluation.getUserSatisfactionScore() >= 0.8) {
            strengths.add("用户满意度高，学习体验良好");
        } else if (evaluation.getUserSatisfactionScore() < 0.6) {
            weaknesses.add("用户满意度有待提升");
            improvements.add("建议收集更多用户反馈，改进学习体验");
        }

        // 如果没有明显优势，添加通用建议
        if (strengths.isEmpty()) {
            strengths.add("学习态度积极，有持续改进的潜力");
        }

        if (improvements.isEmpty()) {
            improvements.add("继续保持当前学习状态，定期评估和调整");
        }

        evaluation.setStrengths(strengths);
        evaluation.setWeaknesses(weaknesses);
        evaluation.setImprovements(improvements);
    }

    /**
     * 确定重新生成建议
     */
    private void determineRegenerationRecommendation(EffectivenessEvaluation evaluation) {
        double effectiveness = evaluation.getOverallEffectiveness();
        RegenerationRecommendation recommendation;
        String reason;

        if (effectiveness >= 0.8) {
            recommendation = RegenerationRecommendation.NOT_NEEDED;
            reason = "学习效果良好，无需重新生成";
        } else if (effectiveness >= 0.6) {
            recommendation = RegenerationRecommendation.MINOR_ADJUSTMENT;
            reason = "学习效果一般，建议进行小幅调整";
        } else if (effectiveness >= 0.4) {
            recommendation = RegenerationRecommendation.MAJOR_ADJUSTMENT;
            reason = "学习效果较差，建议进行大幅调整";
        } else {
            recommendation = RegenerationRecommendation.COMPLETE_REGENERATION;
            reason = "学习效果很差，建议完全重新生成学习路径";
        }

        // 考虑特殊情况
        if (evaluation.getUserSatisfactionScore() < 0.3) {
            recommendation = RegenerationRecommendation.COMPLETE_REGENERATION;
            reason = "用户满意度极低，建议完全重新生成学习路径";
        }

        if (evaluation.getLearningEfficiencyScore() < 0.3) {
            if (recommendation == RegenerationRecommendation.NOT_NEEDED) {
                recommendation = RegenerationRecommendation.MINOR_ADJUSTMENT;
                reason = "学习效率偏低，建议进行调整优化";
            }
        }

        evaluation.setRegenerationRecommendation(recommendation);
        evaluation.setRegenerationReason(reason);
    }

    /**
     * 计算结构逻辑性
     */
    private double calculateStructureLogic(List<UserStepProgress> stepProgresses) {
        if (stepProgresses.isEmpty()) {
            return 0.0;
        }

        // 简化计算：基于步骤完成的连续性
        // 理想情况是按顺序完成，跳跃式完成会降低逻辑性得分

        List<UserStepProgress> completedSteps = stepProgresses.stream()
                .filter(UserStepProgress::isCompleted)
                .sorted((a, b) -> a.getCompletedAt() != null && b.getCompletedAt() != null ?
                        a.getCompletedAt().compareTo(b.getCompletedAt()) : 0)
                .collect(Collectors.toList());

        if (completedSteps.isEmpty()) {
            return 0.5; // 中性得分
        }

        // 计算完成顺序的连续性
        int sequentialCount = 0;
        for (int i = 1; i < completedSteps.size(); i++) {
            // 这里简化处理，实际应该检查步骤的逻辑顺序
            sequentialCount++;
        }

        double sequentialRate = completedSteps.size() > 1 ?
                (double) sequentialCount / (completedSteps.size() - 1) : 1.0;

        return Math.min(1.0, sequentialRate + 0.2); // 给予一定的基础分
    }

    /**
     * 获取等级描述
     */
    private String getLevelDescription(EffectivenessLevel level) {
        switch (level) {
            case EXCELLENT:
                return "优秀";
            case GOOD:
                return "良好";
            case AVERAGE:
                return "一般";
            case POOR:
                return "较差";
            case VERY_POOR:
                return "很差";
            default:
                return "未知";
        }
    }

    /**
     * 获取路径的步骤进度列表
     */
    private List<UserStepProgress> getStepProgressesForPath(Long userId, Long pathId) {
        // 这里需要根据pathId查找对应的步骤，然后获取用户的进度
        // 暂时返回用户的部分步骤进度，后续可以优化为按路径过滤
        return stepProgressRepository.findByUserId(userId).stream()
                .limit(10) // 限制数量避免性能问题
                .collect(Collectors.toList());
    }
}
