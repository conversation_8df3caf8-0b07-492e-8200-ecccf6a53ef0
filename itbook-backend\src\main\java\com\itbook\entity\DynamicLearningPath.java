package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 动态学习路径实体
 * 存储基于用户画像和职业目标生成的个性化学习路径
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Entity
@Table(name = "dynamic_learning_path")
public class DynamicLearningPath {

    /**
     * 路径类型枚举
     */
    public enum PathType {
        CAREER_ORIENTED("职业导向"),
        SKILL_FOCUSED("技能专注"),
        PROJECT_BASED("项目驱动"),
        CUSTOM("自定义");
        
        private final String description;
        
        PathType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 学习风格枚举
     */
    public enum LearningStyle {
        THEORETICAL("理论型"),
        PRACTICAL("实践型"),
        PROJECT_DRIVEN("项目驱动型"),
        MIXED("混合型");
        
        private final String description;
        
        LearningStyle(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 难度偏好枚举
     */
    public enum DifficultyPreference {
        GRADUAL("渐进式"),
        CHALLENGING("挑战式"),
        MIXED("混合式");
        
        private final String description;
        
        DifficultyPreference(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 路径状态枚举
     */
    public enum Status {
        DRAFT("草稿"),
        ACTIVE("进行中"),
        PAUSED("暂停"),
        COMPLETED("已完成"),
        ABANDONED("已放弃");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 用户（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    @JsonIgnore
    private User user;

    /**
     * 关联的职业目标ID
     */
    @Column(name = "career_goal_id")
    private Long careerGoalId;

    /**
     * 职业目标（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "career_goal_id", insertable = false, updatable = false)
    @JsonIgnore
    private CareerGoal careerGoal;

    /**
     * 基础学习路径ID（可选）
     */
    @Column(name = "base_path_id")
    private Long basePathId;

    /**
     * 基础学习路径（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "base_path_id", insertable = false, updatable = false)
    @JsonIgnore
    private LearningPath basePath;

    /**
     * 路径名称
     */
    @NotBlank(message = "路径名称不能为空")
    @Size(max = 200, message = "路径名称长度不能超过200字符")
    @Column(name = "name", nullable = false)
    private String name;

    /**
     * 路径描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 路径类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "path_type", nullable = false)
    private PathType pathType = PathType.CAREER_ORIENTED;

    /**
     * 个性化因子（JSON格式）
     */
    @Column(name = "personalization_factors", columnDefinition = "JSON")
    private String personalizationFactorsJson;

    /**
     * 学习风格
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "learning_style")
    private LearningStyle learningStyle = LearningStyle.MIXED;

    /**
     * 难度偏好
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "difficulty_preference")
    private DifficultyPreference difficultyPreference = DifficultyPreference.GRADUAL;

    /**
     * 时间约束（周）
     */
    @Min(value = 1, message = "时间约束不能小于1周")
    @Column(name = "time_constraint")
    private Integer timeConstraint;

    /**
     * 总技能数
     */
    @Min(value = 0, message = "总技能数不能为负数")
    @Column(name = "total_skills")
    private Integer totalSkills = 0;

    /**
     * 预计总时长（小时）
     */
    @Min(value = 0, message = "预计总时长不能为负数")
    @Column(name = "estimated_hours")
    private Integer estimatedHours = 0;

    /**
     * 完成率
     */
    @DecimalMin(value = "0.00", message = "完成率不能小于0")
    @DecimalMax(value = "100.00", message = "完成率不能大于100")
    @Column(name = "completion_rate", precision = 5, scale = 2)
    private BigDecimal completionRate = BigDecimal.ZERO;

    /**
     * 当前步骤
     */
    @Min(value = 1, message = "当前步骤不能小于1")
    @Column(name = "current_step")
    private Integer currentStep = 1;

    /**
     * 生成算法版本
     */
    @Size(max = 50, message = "算法版本长度不能超过50字符")
    @Column(name = "generation_algorithm")
    private String generationAlgorithm = "v1.0";

    /**
     * 生成参数（JSON格式）
     */
    @Column(name = "generation_params", columnDefinition = "JSON")
    private String generationParamsJson;

    /**
     * 路径质量分数(0-1)
     */
    @DecimalMin(value = "0.00", message = "质量分数不能小于0")
    @DecimalMax(value = "1.00", message = "质量分数不能大于1")
    @Column(name = "quality_score", precision = 3, scale = 2)
    private BigDecimal qualityScore = BigDecimal.ZERO;

    /**
     * 路径状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.DRAFT;

    /**
     * 是否为模板
     */
    @Column(name = "is_template")
    private Boolean isTemplate = false;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "started_at")
    private LocalDateTime startedAt;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    /**
     * 动态路径步骤（一对多关系）
     */
    @OneToMany(mappedBy = "dynamicPath", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("stepOrder ASC")
    @JsonIgnore
    private List<DynamicPathStep> steps = new ArrayList<>();

    // 构造函数
    public DynamicLearningPath() {}

    public DynamicLearningPath(Long userId, String name, PathType pathType) {
        this.userId = userId;
        this.name = name;
        this.pathType = pathType;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }

    public Long getCareerGoalId() { return careerGoalId; }
    public void setCareerGoalId(Long careerGoalId) { this.careerGoalId = careerGoalId; }

    public CareerGoal getCareerGoal() { return careerGoal; }
    public void setCareerGoal(CareerGoal careerGoal) { this.careerGoal = careerGoal; }

    public Long getBasePathId() { return basePathId; }
    public void setBasePathId(Long basePathId) { this.basePathId = basePathId; }

    public LearningPath getBasePath() { return basePath; }
    public void setBasePath(LearningPath basePath) { this.basePath = basePath; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public PathType getPathType() { return pathType; }
    public void setPathType(PathType pathType) { this.pathType = pathType; }

    public String getPersonalizationFactorsJson() { return personalizationFactorsJson; }
    public void setPersonalizationFactorsJson(String personalizationFactorsJson) { this.personalizationFactorsJson = personalizationFactorsJson; }

    public LearningStyle getLearningStyle() { return learningStyle; }
    public void setLearningStyle(LearningStyle learningStyle) { this.learningStyle = learningStyle; }

    public DifficultyPreference getDifficultyPreference() { return difficultyPreference; }
    public void setDifficultyPreference(DifficultyPreference difficultyPreference) { this.difficultyPreference = difficultyPreference; }

    public Integer getTimeConstraint() { return timeConstraint; }
    public void setTimeConstraint(Integer timeConstraint) { this.timeConstraint = timeConstraint; }

    public Integer getTotalSkills() { return totalSkills; }
    public void setTotalSkills(Integer totalSkills) { this.totalSkills = totalSkills; }

    public Integer getEstimatedHours() { return estimatedHours; }
    public void setEstimatedHours(Integer estimatedHours) { this.estimatedHours = estimatedHours; }

    public BigDecimal getCompletionRate() { return completionRate; }
    public void setCompletionRate(BigDecimal completionRate) { this.completionRate = completionRate; }

    public Integer getCurrentStep() { return currentStep; }
    public void setCurrentStep(Integer currentStep) { this.currentStep = currentStep; }

    public String getGenerationAlgorithm() { return generationAlgorithm; }
    public void setGenerationAlgorithm(String generationAlgorithm) { this.generationAlgorithm = generationAlgorithm; }

    public String getGenerationParamsJson() { return generationParamsJson; }
    public void setGenerationParamsJson(String generationParamsJson) { this.generationParamsJson = generationParamsJson; }

    public BigDecimal getQualityScore() { return qualityScore; }
    public void setQualityScore(BigDecimal qualityScore) { this.qualityScore = qualityScore; }

    public Status getStatus() { return status; }
    public void setStatus(Status status) { this.status = status; }

    public Boolean getIsTemplate() { return isTemplate; }
    public void setIsTemplate(Boolean isTemplate) { this.isTemplate = isTemplate; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public LocalDateTime getStartedAt() { return startedAt; }
    public void setStartedAt(LocalDateTime startedAt) { this.startedAt = startedAt; }

    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }

    public List<DynamicPathStep> getSteps() { return steps; }
    public void setSteps(List<DynamicPathStep> steps) { this.steps = steps; }

    @Override
    public String toString() {
        return "DynamicLearningPath{" +
                "id=" + id +
                ", userId=" + userId +
                ", name='" + name + '\'' +
                ", pathType=" + pathType +
                ", status=" + status +
                ", completionRate=" + completionRate +
                '}';
    }
}
