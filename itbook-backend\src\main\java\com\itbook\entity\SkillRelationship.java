package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 技能关系实体
 * 存储原子技能之间的依赖和关联关系，构建知识图谱
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Entity
@Table(name = "skill_relationship")
public class SkillRelationship {

    /**
     * 关系类型枚举
     */
    public enum RelationshipType {
        PREREQUISITE("前置技能"),      // A是B的前置技能
        COREQUISITE("并行技能"),       // A和B需要同时学习
        SUCCESSOR("后续技能"),         // A是B的后续技能
        RELATED("相关技能"),           // A和B相关但无严格顺序
        ALTERNATIVE("替代技能");       // A和B可以互相替代
        
        private final String description;
        
        RelationshipType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 关系来源枚举
     */
    public enum Source {
        MANUAL("手动创建"),
        AUTO_GENERATED("自动生成"),
        ML_INFERRED("机器学习推断");
        
        private final String description;
        
        Source(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 源技能（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "source_skill_id", nullable = false)
    @JsonIgnore
    private AtomicSkill sourceSkill;

    /**
     * 源技能ID
     */
    @NotNull(message = "源技能ID不能为空")
    @Column(name = "source_skill_id", insertable = false, updatable = false)
    private Long sourceSkillId;

    /**
     * 目标技能（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_skill_id", nullable = false)
    @JsonIgnore
    private AtomicSkill targetSkill;

    /**
     * 目标技能ID
     */
    @NotNull(message = "目标技能ID不能为空")
    @Column(name = "target_skill_id", insertable = false, updatable = false)
    private Long targetSkillId;

    /**
     * 关系类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "relationship_type", nullable = false)
    private RelationshipType relationshipType;

    /**
     * 关系强度(0-1)
     */
    @DecimalMin(value = "0.0", message = "关系强度不能小于0")
    @DecimalMax(value = "1.0", message = "关系强度不能大于1")
    @Column(name = "relationship_strength", precision = 3, scale = 2)
    private BigDecimal relationshipStrength = BigDecimal.ONE;

    /**
     * 是否必需关系
     */
    @Column(name = "is_mandatory")
    private Boolean isMandatory = false;

    /**
     * 关系描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 学习顺序权重
     */
    @Column(name = "learning_sequence")
    private Integer learningSequence;

    /**
     * 条件规则（JSON格式）
     */
    @Column(name = "condition_rules", columnDefinition = "JSON")
    private String conditionRulesJson;

    /**
     * 验证规则（JSON格式）
     */
    @Column(name = "validation_rules", columnDefinition = "JSON")
    private String validationRulesJson;

    /**
     * 置信度分数(0-1)
     */
    @DecimalMin(value = "0.0", message = "置信度分数不能小于0")
    @DecimalMax(value = "1.0", message = "置信度分数不能大于1")
    @Column(name = "confidence_score", precision = 3, scale = 2)
    private BigDecimal confidenceScore = BigDecimal.ONE;

    /**
     * 关系来源
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "source")
    private Source source = Source.MANUAL;

    /**
     * 是否激活
     */
    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    // 构造函数
    public SkillRelationship() {}

    public SkillRelationship(AtomicSkill sourceSkill, AtomicSkill targetSkill, RelationshipType relationshipType) {
        this.sourceSkill = sourceSkill;
        this.targetSkill = targetSkill;
        this.relationshipType = relationshipType;
        this.sourceSkillId = sourceSkill.getId();
        this.targetSkillId = targetSkill.getId();
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public AtomicSkill getSourceSkill() { return sourceSkill; }
    public void setSourceSkill(AtomicSkill sourceSkill) { 
        this.sourceSkill = sourceSkill;
        this.sourceSkillId = sourceSkill != null ? sourceSkill.getId() : null;
    }

    public Long getSourceSkillId() { return sourceSkillId; }
    public void setSourceSkillId(Long sourceSkillId) { this.sourceSkillId = sourceSkillId; }

    public AtomicSkill getTargetSkill() { return targetSkill; }
    public void setTargetSkill(AtomicSkill targetSkill) { 
        this.targetSkill = targetSkill;
        this.targetSkillId = targetSkill != null ? targetSkill.getId() : null;
    }

    public Long getTargetSkillId() { return targetSkillId; }
    public void setTargetSkillId(Long targetSkillId) { this.targetSkillId = targetSkillId; }

    public RelationshipType getRelationshipType() { return relationshipType; }
    public void setRelationshipType(RelationshipType relationshipType) { this.relationshipType = relationshipType; }

    public BigDecimal getRelationshipStrength() { return relationshipStrength; }
    public void setRelationshipStrength(BigDecimal relationshipStrength) { this.relationshipStrength = relationshipStrength; }

    public Boolean getIsMandatory() { return isMandatory; }
    public void setIsMandatory(Boolean isMandatory) { this.isMandatory = isMandatory; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Integer getLearningSequence() { return learningSequence; }
    public void setLearningSequence(Integer learningSequence) { this.learningSequence = learningSequence; }

    public String getConditionRulesJson() { return conditionRulesJson; }
    public void setConditionRulesJson(String conditionRulesJson) { this.conditionRulesJson = conditionRulesJson; }

    public String getValidationRulesJson() { return validationRulesJson; }
    public void setValidationRulesJson(String validationRulesJson) { this.validationRulesJson = validationRulesJson; }

    public BigDecimal getConfidenceScore() { return confidenceScore; }
    public void setConfidenceScore(BigDecimal confidenceScore) { this.confidenceScore = confidenceScore; }

    public Source getSource() { return source; }
    public void setSource(Source source) { this.source = source; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public Long getCreatedBy() { return createdBy; }
    public void setCreatedBy(Long createdBy) { this.createdBy = createdBy; }

    @Override
    public String toString() {
        return "SkillRelationship{" +
                "id=" + id +
                ", sourceSkillId=" + sourceSkillId +
                ", targetSkillId=" + targetSkillId +
                ", relationshipType=" + relationshipType +
                ", relationshipStrength=" + relationshipStrength +
                ", isMandatory=" + isMandatory +
                '}';
    }
}
