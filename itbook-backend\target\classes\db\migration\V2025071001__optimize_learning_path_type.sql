-- 优化学习路径类型设计
-- 移除冗余的is_recommended字段，使用path_type来明确标识路径类型
-- 
-- 作者: ITBook Team
-- 日期: 2025-07-10
-- 版本: V2025071001

-- 1. 更新现有数据：将is_recommended=true的路径设置为PERSONALIZED类型
UPDATE learning_path
SET path_type = 'PERSONALIZED'
WHERE is_recommended = true;

-- 2. 确保所有其他路径的path_type正确设置为STANDARD（如果还没有设置）
UPDATE learning_path
SET path_type = 'STANDARD'
WHERE is_recommended = false AND (path_type IS NULL OR path_type = '');

-- 3. 验证数据更新结果
SELECT
    path_type,
    COUNT(*) as count,
    GROUP_CONCAT(name SEPARATOR ', ') as path_names
FROM learning_path
GROUP BY path_type
ORDER BY path_type;

-- 4. 删除冗余的is_recommended字段
ALTER TABLE learning_path DROP COLUMN is_recommended;

-- 5. 添加注释说明新的设计
ALTER TABLE learning_path 
MODIFY COLUMN path_type ENUM('STANDARD', 'PERSONALIZED', 'TEMPLATE', 'CUSTOM') NOT NULL 
COMMENT '路径类型：STANDARD=标准路径(每个岗位的官方标准学习路径), PERSONALIZED=个性化推荐路径(基于用户画像生成), TEMPLATE=模板路径(可复用), CUSTOM=自定义路径(用户创建)';

-- 6. 创建索引优化查询性能
CREATE INDEX idx_learning_path_type_status ON learning_path(path_type, status);
CREATE INDEX idx_learning_path_target_job_type ON learning_path(target_job_id, path_type);

-- 7. 验证最终结果
SELECT 
    '优化完成' as status,
    COUNT(*) as total_paths,
    SUM(CASE WHEN path_type = 'STANDARD' THEN 1 ELSE 0 END) as standard_paths,
    SUM(CASE WHEN path_type = 'PERSONALIZED' THEN 1 ELSE 0 END) as personalized_paths,
    SUM(CASE WHEN path_type = 'TEMPLATE' THEN 1 ELSE 0 END) as template_paths,
    SUM(CASE WHEN path_type = 'CUSTOM' THEN 1 ELSE 0 END) as custom_paths
FROM learning_path;
