package com.itbook.dto;

import java.util.List;
import java.util.Map;

/**
 * 动态路径生成请求DTO
 * 用于请求生成个性化学习路径
 */
public class DynamicPathGenerationRequest {
    // 基本信息
    private Long userId;
    private String pathName;
    private String description;
    
    // 目标设定
    private List<Long> targetSkillIds;
    private Long targetCareerGoalId;
    private String targetProficiencyLevel; // BASIC, INTERMEDIATE, ADVANCED, EXPERT
    private String targetTimeframe; // 3_MONTHS, 6_MONTHS, 1_YEAR, 2_YEARS
    private Integer maxPathLength; // 最大路径步骤数
    
    // 个性化因子
    private PersonalizationFactors personalizationFactors;
    
    // 约束条件
    private Double maxHoursPerWeek;
    private List<String> excludedSkillIds; // 排除的技能ID
    private List<String> preferredResourceTypes; // VIDEO, ARTICLE, BOOK, COURSE, TUTORIAL
    private String budgetLevel; // FREE, LOW, MEDIUM, HIGH
    private String difficultyProgression; // LINEAR, ADAPTIVE, ACCELERATED
    
    // 路径偏好
    private String pathStyle; // STRUCTURED, FLEXIBLE, EXPLORATORY
    private Boolean includeProjects; // 是否包含项目实践
    private Boolean includeAssessments; // 是否包含技能评估
    private Boolean allowParallelLearning; // 是否允许并行学习
    private Double practiceToTheoryRatio; // 实践与理论的比例 0.0-1.0
    
    // 适应性设置
    private Boolean enableAdaptiveAdjustment; // 是否启用自适应调整
    private String adaptationFrequency; // DAILY, WEEKLY, MONTHLY
    private Double performanceThreshold; // 性能阈值，低于此值触发调整
    private Boolean allowPathBranching; // 是否允许路径分支
    
    // 社交学习
    private Boolean enablePeerLearning; // 是否启用同伴学习
    private Boolean enableMentorship; // 是否启用导师指导
    private Boolean enableGroupProjects; // 是否启用小组项目
    
    // 质量控制
    private String qualityLevel; // BASIC, STANDARD, HIGH, PREMIUM
    private Boolean requireValidatedContent; // 是否要求验证过的内容
    private Double minContentRating; // 最低内容评分
    private Boolean prioritizeRecentContent; // 是否优先最新内容
    
    // 输出格式
    private String outputFormat; // DETAILED, SUMMARY, MINIMAL
    private Boolean includeAlternatives; // 是否包含替代路径
    private Boolean includeMetadata; // 是否包含元数据
    private List<String> additionalFields; // 额外需要的字段
    
    // 扩展参数
    private Map<String, Object> customParameters;
    private String generationMode; // STANDARD, EXPERIMENTAL, CONSERVATIVE
    private String version; // API版本

    public DynamicPathGenerationRequest() {}

    // Getters and Setters
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getPathName() { return pathName; }
    public void setPathName(String pathName) { this.pathName = pathName; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public List<Long> getTargetSkillIds() { return targetSkillIds; }
    public void setTargetSkillIds(List<Long> targetSkillIds) { this.targetSkillIds = targetSkillIds; }

    public Long getTargetCareerGoalId() { return targetCareerGoalId; }
    public void setTargetCareerGoalId(Long targetCareerGoalId) { this.targetCareerGoalId = targetCareerGoalId; }

    public String getTargetProficiencyLevel() { return targetProficiencyLevel; }
    public void setTargetProficiencyLevel(String targetProficiencyLevel) { this.targetProficiencyLevel = targetProficiencyLevel; }

    public String getTargetTimeframe() { return targetTimeframe; }
    public void setTargetTimeframe(String targetTimeframe) { this.targetTimeframe = targetTimeframe; }

    public Integer getMaxPathLength() { return maxPathLength; }
    public void setMaxPathLength(Integer maxPathLength) { this.maxPathLength = maxPathLength; }

    public PersonalizationFactors getPersonalizationFactors() { return personalizationFactors; }
    public void setPersonalizationFactors(PersonalizationFactors personalizationFactors) { this.personalizationFactors = personalizationFactors; }

    public Double getMaxHoursPerWeek() { return maxHoursPerWeek; }
    public void setMaxHoursPerWeek(Double maxHoursPerWeek) { this.maxHoursPerWeek = maxHoursPerWeek; }

    public List<String> getExcludedSkillIds() { return excludedSkillIds; }
    public void setExcludedSkillIds(List<String> excludedSkillIds) { this.excludedSkillIds = excludedSkillIds; }

    public List<String> getPreferredResourceTypes() { return preferredResourceTypes; }
    public void setPreferredResourceTypes(List<String> preferredResourceTypes) { this.preferredResourceTypes = preferredResourceTypes; }

    public String getBudgetLevel() { return budgetLevel; }
    public void setBudgetLevel(String budgetLevel) { this.budgetLevel = budgetLevel; }

    public String getDifficultyProgression() { return difficultyProgression; }
    public void setDifficultyProgression(String difficultyProgression) { this.difficultyProgression = difficultyProgression; }

    public String getPathStyle() { return pathStyle; }
    public void setPathStyle(String pathStyle) { this.pathStyle = pathStyle; }

    public Boolean getIncludeProjects() { return includeProjects; }
    public void setIncludeProjects(Boolean includeProjects) { this.includeProjects = includeProjects; }

    public Boolean getIncludeAssessments() { return includeAssessments; }
    public void setIncludeAssessments(Boolean includeAssessments) { this.includeAssessments = includeAssessments; }

    public Boolean getAllowParallelLearning() { return allowParallelLearning; }
    public void setAllowParallelLearning(Boolean allowParallelLearning) { this.allowParallelLearning = allowParallelLearning; }

    public Double getPracticeToTheoryRatio() { return practiceToTheoryRatio; }
    public void setPracticeToTheoryRatio(Double practiceToTheoryRatio) { this.practiceToTheoryRatio = practiceToTheoryRatio; }

    public Boolean getEnableAdaptiveAdjustment() { return enableAdaptiveAdjustment; }
    public void setEnableAdaptiveAdjustment(Boolean enableAdaptiveAdjustment) { this.enableAdaptiveAdjustment = enableAdaptiveAdjustment; }

    public String getAdaptationFrequency() { return adaptationFrequency; }
    public void setAdaptationFrequency(String adaptationFrequency) { this.adaptationFrequency = adaptationFrequency; }

    public Double getPerformanceThreshold() { return performanceThreshold; }
    public void setPerformanceThreshold(Double performanceThreshold) { this.performanceThreshold = performanceThreshold; }

    public Boolean getAllowPathBranching() { return allowPathBranching; }
    public void setAllowPathBranching(Boolean allowPathBranching) { this.allowPathBranching = allowPathBranching; }

    public Boolean getEnablePeerLearning() { return enablePeerLearning; }
    public void setEnablePeerLearning(Boolean enablePeerLearning) { this.enablePeerLearning = enablePeerLearning; }

    public Boolean getEnableMentorship() { return enableMentorship; }
    public void setEnableMentorship(Boolean enableMentorship) { this.enableMentorship = enableMentorship; }

    public Boolean getEnableGroupProjects() { return enableGroupProjects; }
    public void setEnableGroupProjects(Boolean enableGroupProjects) { this.enableGroupProjects = enableGroupProjects; }

    public String getQualityLevel() { return qualityLevel; }
    public void setQualityLevel(String qualityLevel) { this.qualityLevel = qualityLevel; }

    public Boolean getRequireValidatedContent() { return requireValidatedContent; }
    public void setRequireValidatedContent(Boolean requireValidatedContent) { this.requireValidatedContent = requireValidatedContent; }

    public Double getMinContentRating() { return minContentRating; }
    public void setMinContentRating(Double minContentRating) { this.minContentRating = minContentRating; }

    public Boolean getPrioritizeRecentContent() { return prioritizeRecentContent; }
    public void setPrioritizeRecentContent(Boolean prioritizeRecentContent) { this.prioritizeRecentContent = prioritizeRecentContent; }

    public String getOutputFormat() { return outputFormat; }
    public void setOutputFormat(String outputFormat) { this.outputFormat = outputFormat; }

    public Boolean getIncludeAlternatives() { return includeAlternatives; }
    public void setIncludeAlternatives(Boolean includeAlternatives) { this.includeAlternatives = includeAlternatives; }

    public Boolean getIncludeMetadata() { return includeMetadata; }
    public void setIncludeMetadata(Boolean includeMetadata) { this.includeMetadata = includeMetadata; }

    public List<String> getAdditionalFields() { return additionalFields; }
    public void setAdditionalFields(List<String> additionalFields) { this.additionalFields = additionalFields; }

    public Map<String, Object> getCustomParameters() { return customParameters; }
    public void setCustomParameters(Map<String, Object> customParameters) { this.customParameters = customParameters; }

    public String getGenerationMode() { return generationMode; }
    public void setGenerationMode(String generationMode) { this.generationMode = generationMode; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }
}
