-- 手动更新job表中的responsibilities字段
-- 为现有的job记录添加工作职责数据

-- 更新job id=2 (Java后端开发工程师1)
UPDATE job SET responsibilities = '["负责后端系统架构设计和开发","编写高质量、可维护的代码","参与系统性能优化和问题排查","参与代码审查和技术分享","持续学习新技术，提升技术能力"]' WHERE id = 2;

-- 更新job id=3 (前端开发工程师2)
UPDATE job SET responsibilities = '["负责前端页面开发和用户体验优化","与设计师和后端工程师协作完成产品功能","参与前端技术选型和架构设计","参与代码审查和技术分享","持续学习新技术，提升技术能力"]' WHERE id = 3;

-- 更新job id=4 (全栈开发工程师3)
UPDATE job SET responsibilities = '["负责前后端全栈开发工作","参与产品需求分析和技术方案设计","协调前后端技术实现和接口对接","参与代码审查和技术分享","持续学习新技术，提升技术能力"]' WHERE id = 4;

-- 更新job id=5 (数据分析师4)
UPDATE job SET responsibilities = '["负责数据分析和挖掘工作","设计和维护数据处理流程","编写数据分析报告和可视化图表","参与数据模型设计和优化","持续学习新的数据分析技术"]' WHERE id = 5;

-- 更新job id=6 (产品经理5)
UPDATE job SET responsibilities = '["负责产品规划和需求分析","协调各部门完成产品功能开发","进行用户调研和市场分析","制定产品发展策略和路线图","持续关注行业趋势和用户反馈"]' WHERE id = 6;

-- 验证更新结果
SELECT id, title, responsibilities FROM job WHERE id IN (2,3,4,5,6);
