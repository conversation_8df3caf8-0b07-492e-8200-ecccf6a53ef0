/**
 * 跨平台确认对话框组件
 * 
 * 功能：
 * - 自动适配不同平台（Web、iOS、Android）
 * - Web环境使用自定义Modal，移动端使用原生Alert
 * - 统一的API接口，支持自定义样式
 * - 支持加载状态和禁用状态
 * 
 * <AUTHOR> Team
 * @since 2025-07-18
 */

import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';

export interface ConfirmDialogProps {
  /** 是否显示对话框 */
  visible: boolean;
  /** 对话框标题 */
  title: string;
  /** 对话框内容 */
  message: string;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮样式类型 */
  confirmType?: 'default' | 'destructive' | 'primary';
  /** 是否显示警告图标 */
  showWarningIcon?: boolean;
  /** 是否处于加载状态 */
  loading?: boolean;
  /** 确认回调 */
  onConfirm: () => void;
  /** 取消回调 */
  onCancel: () => void;
}

/**
 * 跨平台确认对话框组件
 */
export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  visible,
  title,
  message,
  confirmText = '确定',
  cancelText = '取消',
  confirmType = 'default',
  showWarningIcon = false,
  loading = false,
  onConfirm,
  onCancel,
}) => {
  const colors = useThemeColors();

  // 获取确认按钮颜色
  const getConfirmButtonColor = () => {
    switch (confirmType) {
      case 'destructive':
        return colors.error || '#FF3B30';
      case 'primary':
        return colors.primary;
      default:
        return colors.primary;
    }
  };

  // Web环境使用自定义Modal
  const renderWebDialog = () => (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: tokens.spacing('lg'),
      }}>
        <View style={{
          backgroundColor: colors.surface,
          borderRadius: tokens.radius('lg'),
          padding: tokens.spacing('xl'),
          width: '100%',
          maxWidth: 400,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 5,
        }}>
          {/* 标题 */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: tokens.spacing('lg'),
          }}>
            {showWarningIcon && (
              <Ionicons 
                name="warning-outline" 
                size={24} 
                color={confirmType === 'destructive' ? (colors.error || '#FF3B30') : colors.primary} 
              />
            )}
            <Text style={{
              marginLeft: showWarningIcon ? tokens.spacing('sm') : 0,
              fontSize: tokens.fontSize('title-sm'),
              fontWeight: tokens.fontWeight('bold') as any,
              color: colors.text,
            }}>
              {title}
            </Text>
          </View>

          {/* 内容 */}
          <Text style={{
            fontSize: tokens.fontSize('body'),
            color: colors.text,
            lineHeight: 22,
            marginBottom: tokens.spacing('xl'),
          }}>
            {message}
          </Text>

          {/* 按钮组 */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'flex-end',
            gap: tokens.spacing('md'),
          }}>
            {/* 取消按钮 */}
            <TouchableOpacity
              onPress={onCancel}
              activeOpacity={0.7}
              disabled={loading}
              style={{
                paddingHorizontal: tokens.spacing('lg'),
                paddingVertical: tokens.spacing('sm'),
                borderRadius: tokens.radius('md'),
                borderWidth: 1,
                borderColor: colors.border,
                backgroundColor: colors.background,
                opacity: loading ? 0.6 : 1,
              }}
            >
              <Text style={{
                fontSize: tokens.fontSize('body'),
                fontWeight: tokens.fontWeight('medium') as any,
                color: colors.text,
              }}>
                {cancelText}
              </Text>
            </TouchableOpacity>

            {/* 确认按钮 */}
            <TouchableOpacity
              onPress={onConfirm}
              activeOpacity={0.7}
              disabled={loading}
              style={{
                paddingHorizontal: tokens.spacing('lg'),
                paddingVertical: tokens.spacing('sm'),
                borderRadius: tokens.radius('md'),
                backgroundColor: getConfirmButtonColor(),
                opacity: loading ? 0.6 : 1,
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              {loading && (
                <ActivityIndicator 
                  size="small" 
                  color="white" 
                  style={{ marginRight: tokens.spacing('xs') }} 
                />
              )}
              <Text style={{
                fontSize: tokens.fontSize('body'),
                fontWeight: tokens.fontWeight('medium') as any,
                color: 'white',
              }}>
                {loading ? '处理中...' : confirmText}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  // 移动端使用原生Alert
  const showNativeAlert = () => {
    if (visible) {
      Alert.alert(
        title,
        message,
        [
          {
            text: cancelText,
            style: 'cancel',
            onPress: onCancel,
          },
          {
            text: confirmText,
            style: confirmType === 'destructive' ? 'destructive' : 'default',
            onPress: onConfirm,
          },
        ]
      );
    }
  };

  // 根据平台选择实现方式
  if (Platform.OS === 'web') {
    return renderWebDialog();
  } else {
    // 移动端使用原生Alert，但需要监听visible变化
    React.useEffect(() => {
      showNativeAlert();
    }, [visible]);

    return null; // 移动端不需要渲染任何组件
  }
};

export default ConfirmDialog;
