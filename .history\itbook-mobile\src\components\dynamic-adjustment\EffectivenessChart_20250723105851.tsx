/**
 * 学习效果图表组件
 * 
 * 展示学习效果评估结果的可视化图表
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';
import { DynamicAdjustmentService } from '../../services/DynamicAdjustmentService';
import {
  EffectivenessChartProps,
  EffectivenessEvaluation,
  EffectivenessLevel
} from '../../types/DynamicAdjustment';

/**
 * 学习效果图表组件
 */
export const EffectivenessChart: React.FC<EffectivenessChartProps> = ({
  evaluation,
  showDetails = false
}) => {
  const colors = useThemeColors();
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const renderProgressBar = (label: string, value: number, color: string) => {
    const percentage = Math.round(value * 100);
    
    return (
      <View style={{ marginBottom: tokens.spacing('md') }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: tokens.spacing('xs')
        }}>
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('sm'),
            fontWeight: tokens.fontWeight('medium')
          }}>
            {label}
          </Text>
          <Text style={{
            color: colors.textSecondary,
            fontSize: tokens.fontSize('sm')
          }}>
            {percentage}%
          </Text>
        </View>
        <View style={{
          height: 8,
          backgroundColor: colors.surfaceVariant,
          borderRadius: tokens.radius('xs'),
          overflow: 'hidden'
        }}>
          <View style={{
            height: '100%',
            width: `${percentage}%`,
            backgroundColor: color,
            borderRadius: tokens.radius('xs')
          }} />
        </View>
      </View>
    );
  };

  const renderEffectivenessLevel = () => {
    const levelColor = DynamicAdjustmentService.getEffectivenessLevelColor(evaluation.effectivenessLevel);
    const levelText = DynamicAdjustmentService.formatEffectivenessLevel(evaluation.effectivenessLevel);
    
    return (
      <View style={{
        alignItems: 'center',
        marginBottom: tokens.spacing('lg')
      }}>
        <View style={{
          width: 120,
          height: 120,
          borderRadius: 60,
          backgroundColor: `${levelColor}20`,
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: tokens.spacing('md')
        }}>
          <Text style={{
            color: levelColor,
            fontSize: tokens.fontSize('title-lg'),
            fontWeight: tokens.fontWeight('bold')
          }}>
            {Math.round(evaluation.overallEffectiveness * 100)}%
          </Text>
        </View>
        <Text style={{
          color: colors.text,
          fontSize: tokens.fontSize('title-sm'),
          fontWeight: tokens.fontWeight('bold'),
          marginBottom: tokens.spacing('xs')
        }}>
          {levelText}
        </Text>
        <Text style={{
          color: colors.textSecondary,
          fontSize: tokens.fontSize('sm'),
          textAlign: 'center',
          lineHeight: 20
        }}>
          {evaluation.evaluationSummary}
        </Text>
      </View>
    );
  };

  const renderDimensionScores = () => {
    const dimensions = [
      { key: 'learningOutcomeScore', label: '学习成果', value: evaluation.learningOutcomeScore, color: '#4CAF50' },
      { key: 'pathQualityScore', label: '路径质量', value: evaluation.pathQualityScore, color: '#2196F3' },
      { key: 'learningEfficiencyScore', label: '学习效率', value: evaluation.learningEfficiencyScore, color: '#FF9800' },
      { key: 'userSatisfactionScore', label: '用户满意度', value: evaluation.userSatisfactionScore, color: '#9C27B0' }
    ];

    return (
      <View style={{ marginBottom: tokens.spacing('lg') }}>
        <Text style={{
          color: colors.text,
          fontSize: tokens.fontSize('md'),
          fontWeight: tokens.fontWeight('bold'),
          marginBottom: tokens.spacing('md')
        }}>
          各维度评分
        </Text>
        {dimensions.map((dimension) => (
          <View key={dimension.key}>
            {renderProgressBar(dimension.label, dimension.value, dimension.color)}
          </View>
        ))}
      </View>
    );
  };

  const renderDetailSection = (title: string, items: string[], icon: string, sectionKey: string) => {
    if (!items || items.length === 0) return null;

    const isExpanded = expandedSection === sectionKey;

    return (
      <View style={{
        backgroundColor: colors.surfaceVariant,
        borderRadius: tokens.radius('sm'),
        marginBottom: tokens.spacing('md')
      }}>
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: tokens.spacing('md')
          }}
          onPress={() => setExpandedSection(isExpanded ? null : sectionKey)}
          activeOpacity={0.7}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
            <Ionicons name={icon as any} size={20} color={colors.primary} />
            <Text style={{
              color: colors.text,
              fontSize: tokens.fontSize('md'),
              fontWeight: tokens.fontWeight('medium'),
              marginLeft: tokens.spacing('sm')
            }}>
              {title}
            </Text>
            <View style={{
              backgroundColor: colors.primary,
              borderRadius: tokens.radius('xs'),
              paddingHorizontal: tokens.spacing('xs'),
              paddingVertical: 2,
              marginLeft: tokens.spacing('sm')
            }}>
              <Text style={{
                color: colors.onPrimary,
                fontSize: tokens.fontSize('xs'),
                fontWeight: tokens.fontWeight('medium')
              }}>
                {items.length}
              </Text>
            </View>
          </View>
          <Ionicons
            name={isExpanded ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={colors.textSecondary}
          />
        </TouchableOpacity>
        
        {isExpanded && (
          <View style={{
            paddingHorizontal: tokens.spacing('md'),
            paddingBottom: tokens.spacing('md')
          }}>
            {items.map((item, index) => (
              <View key={index} style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                marginBottom: tokens.spacing('sm')
              }}>
                <View style={{
                  width: 6,
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: colors.primary,
                  marginTop: 6,
                  marginRight: tokens.spacing('sm')
                }} />
                <Text style={{
                  color: colors.onSurfaceVariant,
                  fontSize: tokens.fontSize('sm'),
                  lineHeight: 18,
                  flex: 1
                }}>
                  {item}
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderSkillMastery = () => {
    const masteryPercentage = Math.round(evaluation.skillMasteryRate * 100);
    
    return (
      <View style={{
        backgroundColor: colors.primaryContainer,
        borderRadius: tokens.radius('sm'),
        padding: tokens.spacing('md'),
        marginBottom: tokens.spacing('lg')
      }}>
        <Text style={{
          color: colors.onPrimaryContainer,
          fontSize: tokens.fontSize('md'),
          fontWeight: tokens.fontWeight('bold'),
          marginBottom: tokens.spacing('sm')
        }}>
          技能掌握情况
        </Text>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{
              color: colors.onPrimaryContainer,
              fontSize: tokens.fontSize('sm'),
              marginBottom: tokens.spacing('xs')
            }}>
              已掌握技能: {evaluation.skillsAcquired} / {evaluation.totalSkills}
            </Text>
            <Text style={{
              color: colors.onPrimaryContainer,
              fontSize: tokens.fontSize('sm')
            }}>
              掌握率: {masteryPercentage}%
            </Text>
          </View>
          <View style={{
            width: 60,
            height: 60,
            borderRadius: 30,
            backgroundColor: colors.onPrimaryContainer,
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Text style={{
              color: colors.primaryContainer,
              fontSize: tokens.fontSize('md'),
              fontWeight: tokens.fontWeight('bold')
            }}>
              {masteryPercentage}%
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScrollView style={{
      backgroundColor: colors.surface,
      borderRadius: tokens.radius('md'),
      margin: tokens.spacing('md')
    }}>
      <View style={{ padding: tokens.spacing('lg') }}>
        {/* 标题 */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: tokens.spacing('lg')
        }}>
          <Ionicons name="analytics" size={24} color={colors.primary} />
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('title-sm'),
            fontWeight: tokens.fontWeight('bold'),
            marginLeft: tokens.spacing('sm')
          }}>
            学习效果分析
          </Text>
        </View>

        {/* 整体效果等级 */}
        {renderEffectivenessLevel()}

        {/* 技能掌握情况 */}
        {renderSkillMastery()}

        {/* 各维度评分 */}
        {renderDimensionScores()}

        {/* 详细信息 */}
        {showDetails && (
          <View>
            <Text style={{
              color: colors.text,
              fontSize: tokens.fontSize('md'),
              fontWeight: tokens.fontWeight('bold'),
              marginBottom: tokens.spacing('md')
            }}>
              详细分析
            </Text>

            {renderDetailSection('优势', evaluation.strengths, 'checkmark-circle', 'strengths')}
            {renderDetailSection('不足', evaluation.weaknesses, 'alert-circle', 'weaknesses')}
            {renderDetailSection('改进建议', evaluation.improvements, 'bulb', 'improvements')}
          </View>
        )}

        {/* 重新生成建议 */}
        <View style={{
          backgroundColor: evaluation.regenerationRecommendation === 'COMPLETE_REGENERATION' ? 
            colors.errorContainer : colors.surfaceVariant,
          borderRadius: tokens.radius('sm'),
          padding: tokens.spacing('md'),
          marginTop: tokens.spacing('md')
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: tokens.spacing('sm')
          }}>
            <Ionicons
              name={evaluation.regenerationRecommendation === 'COMPLETE_REGENERATION' ? 'warning' : 'information-circle'}
              size={20}
              color={evaluation.regenerationRecommendation === 'COMPLETE_REGENERATION' ? 
                colors.onErrorContainer : colors.onSurfaceVariant}
            />
            <Text style={{
              color: evaluation.regenerationRecommendation === 'COMPLETE_REGENERATION' ? 
                colors.onErrorContainer : colors.onSurfaceVariant,
              fontSize: tokens.fontSize('md'),
              fontWeight: tokens.fontWeight('medium'),
              marginLeft: tokens.spacing('sm')
            }}>
              路径调整建议
            </Text>
          </View>
          <Text style={{
            color: evaluation.regenerationRecommendation === 'COMPLETE_REGENERATION' ? 
              colors.onErrorContainer : colors.onSurfaceVariant,
            fontSize: tokens.fontSize('sm'),
            lineHeight: 18
          }}>
            {evaluation.regenerationReason}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};
