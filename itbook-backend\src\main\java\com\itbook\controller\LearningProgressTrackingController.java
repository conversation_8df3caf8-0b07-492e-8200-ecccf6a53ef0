package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.LearningProgressTracker;
import com.itbook.service.LearningProgressTracker.LearningEvent;
import com.itbook.service.LearningProgressTracker.LearningEventType;
import com.itbook.service.LearningProgressTracker.LearningBehaviorAnalysis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 学习进度跟踪控制器
 * 
 * 提供学习进度跟踪和行为分析的REST API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/learning-progress-tracking")
@RequiredArgsConstructor
@Tag(name = "学习进度跟踪", description = "学习进度跟踪和行为分析相关接口")
public class LearningProgressTrackingController {

    private final LearningProgressTracker learningProgressTracker;

    /**
     * 记录学习行为事件
     */
    @PostMapping("/events")
    @Operation(summary = "记录学习行为事件", description = "记录用户的学习行为事件，用于进度跟踪和行为分析")
    public ResponseEntity<ApiResponse<Map<String, Object>>> recordLearningEvent(
            @Parameter(description = "学习事件数据", required = true)
            @Valid @RequestBody LearningEventRequest request) {
        
        log.info("📝 记录学习行为事件: userId={}, eventType={}, stepId={}", 
                request.getUserId(), request.getEventType(), request.getStepId());
        
        try {
            // 构建学习事件
            LearningEvent event = new LearningEvent();
            event.setUserId(request.getUserId());
            event.setPathId(request.getPathId());
            event.setStepId(request.getStepId());
            event.setEventType(LearningEventType.valueOf(request.getEventType()));
            event.setTimestamp(LocalDateTime.now());
            event.setEventData(request.getEventData() != null ? request.getEventData() : new HashMap<>());

            // 记录事件
            learningProgressTracker.recordLearningEvent(event);

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("eventId", System.currentTimeMillis()); // 临时ID
            responseData.put("userId", request.getUserId());
            responseData.put("eventType", request.getEventType());
            responseData.put("timestamp", event.getTimestamp());
            responseData.put("status", "recorded");

            log.info("✅ 学习行为事件记录成功: userId={}, eventType={}", 
                    request.getUserId(), request.getEventType());

            return ResponseEntity.ok(ApiResponse.success("学习行为事件记录成功", responseData));

        } catch (Exception e) {
            log.error("❌ 学习行为事件记录失败: userId={}, eventType={}", 
                    request.getUserId(), request.getEventType(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习行为事件记录失败: " + e.getMessage()));
        }
    }

    /**
     * 分析用户学习行为
     */
    @GetMapping("/users/{userId}/paths/{pathId}/behavior-analysis")
    @Operation(summary = "分析用户学习行为", description = "分析用户在特定学习路径中的学习行为和模式")
    public ResponseEntity<ApiResponse<LearningBehaviorAnalysis>> analyzeLearningBehavior(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId,
            @Parameter(description = "路径ID", required = true) @PathVariable Long pathId) {
        
        log.info("🔍 分析用户学习行为: userId={}, pathId={}", userId, pathId);
        
        try {
            // 执行学习行为分析
            LearningBehaviorAnalysis analysis = learningProgressTracker.analyzeLearningBehavior(userId, pathId);

            log.info("✅ 学习行为分析完成: userId={}, pathId={}, riskLevel={}", 
                    userId, pathId, analysis.getRiskLevel());

            return ResponseEntity.ok(ApiResponse.success("学习行为分析完成", analysis));

        } catch (Exception e) {
            log.error("❌ 学习行为分析失败: userId={}, pathId={}", userId, pathId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("学习行为分析失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户学习统计信息
     */
    @GetMapping("/users/{userId}/statistics")
    @Operation(summary = "获取用户学习统计", description = "获取用户的整体学习统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserLearningStatistics(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        
        log.info("📊 获取用户学习统计: userId={}", userId);
        
        try {
            // 这里可以调用学习进度跟踪器获取统计信息
            // 暂时返回模拟数据
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("userId", userId);
            statistics.put("totalLearningTime", 1200); // 总学习时间（分钟）
            statistics.put("completedSteps", 15);      // 已完成步骤数
            statistics.put("totalSteps", 20);          // 总步骤数
            statistics.put("averageSessionDuration", 45); // 平均学习时长（分钟）
            statistics.put("learningStreak", 7);       // 连续学习天数
            statistics.put("lastLearningDate", LocalDateTime.now().minusDays(1));
            statistics.put("learningEfficiency", 0.85); // 学习效率
            statistics.put("preferredLearningTime", "晚上"); // 偏好学习时间

            log.info("✅ 用户学习统计获取成功: userId={}", userId);

            return ResponseEntity.ok(ApiResponse.success("用户学习统计获取成功", statistics));

        } catch (Exception e) {
            log.error("❌ 用户学习统计获取失败: userId={}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("用户学习统计获取失败: " + e.getMessage()));
        }
    }

    /**
     * 获取支持的学习事件类型
     */
    @GetMapping("/event-types")
    @Operation(summary = "获取学习事件类型", description = "获取所有支持的学习行为事件类型")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLearningEventTypes() {
        
        log.info("📋 获取学习事件类型列表");
        
        try {
            Map<String, Object> eventTypes = new HashMap<>();
            
            for (LearningEventType eventType : LearningEventType.values()) {
                Map<String, String> eventInfo = new HashMap<>();
                switch (eventType) {
                    case START_LEARNING:
                        eventInfo.put("name", "开始学习");
                        eventInfo.put("description", "用户开始学习某个步骤");
                        break;
                    case PAUSE_LEARNING:
                        eventInfo.put("name", "暂停学习");
                        eventInfo.put("description", "用户暂停当前学习");
                        break;
                    case RESUME_LEARNING:
                        eventInfo.put("name", "恢复学习");
                        eventInfo.put("description", "用户恢复学习");
                        break;
                    case COMPLETE_STEP:
                        eventInfo.put("name", "完成步骤");
                        eventInfo.put("description", "用户完成学习步骤");
                        break;
                    case SKIP_STEP:
                        eventInfo.put("name", "跳过步骤");
                        eventInfo.put("description", "用户跳过某个步骤");
                        break;
                    case RATE_CONTENT:
                        eventInfo.put("name", "评价内容");
                        eventInfo.put("description", "用户对学习内容进行评价");
                        break;
                    case ADD_NOTE:
                        eventInfo.put("name", "添加笔记");
                        eventInfo.put("description", "用户添加学习笔记");
                        break;
                    case SEEK_HELP:
                        eventInfo.put("name", "寻求帮助");
                        eventInfo.put("description", "用户寻求学习帮助");
                        break;
                    case REPEAT_CONTENT:
                        eventInfo.put("name", "重复学习");
                        eventInfo.put("description", "用户重复学习内容");
                        break;
                }
                eventTypes.put(eventType.name(), eventInfo);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("eventTypes", eventTypes);
            responseData.put("totalCount", eventTypes.size());

            return ResponseEntity.ok(ApiResponse.success("获取学习事件类型成功", responseData));

        } catch (Exception e) {
            log.error("❌ 获取学习事件类型失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取学习事件类型失败: " + e.getMessage()));
        }
    }

    /**
     * 学习事件请求参数
     */
    public static class LearningEventRequest {
        private Long userId;
        private Long pathId;
        private Long stepId;
        private String eventType;
        private Map<String, Object> eventData;

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public Long getStepId() { return stepId; }
        public void setStepId(Long stepId) { this.stepId = stepId; }
        
        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }
        
        public Map<String, Object> getEventData() { return eventData; }
        public void setEventData(Map<String, Object> eventData) { this.eventData = eventData; }
    }
}
