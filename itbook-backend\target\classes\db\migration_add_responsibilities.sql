-- ===================================================================
-- 数据库迁移脚本：添加responsibilities列，删除description列
-- 创建时间：2025-07-16
-- 目的：将职位描述从单一description字段改为结构化的responsibilities列表
-- ===================================================================

-- 第一步：添加responsibilities列（JSON类型）
ALTER TABLE `job` ADD COLUMN `responsibilities` JSON NULL COMMENT '工作职责列表，JSON格式存储职责描述数组';

-- 第二步：将现有description数据迁移到responsibilities
-- 为每个现有记录生成基于职位标题的工作职责
UPDATE `job` SET `responsibilities` = JSON_ARRAY(
    CASE 
        WHEN `title` LIKE '%后端%' OR `title` LIKE '%Backend%' OR `title` LIKE '%Java%' THEN 
            JSON_ARRAY(
                '负责后端系统架构设计和开发',
                '编写高质量、可维护的代码',
                '参与系统性能优化和问题排查',
                '参与代码审查和技术分享',
                '持续学习新技术，提升技术能力'
            )
        WHEN `title` LIKE '%前端%' OR `title` LIKE '%Frontend%' OR `title` LIKE '%React%' OR `title` LIKE '%Vue%' THEN
            JSON_ARRAY(
                '负责前端页面开发和用户体验优化',
                '与设计师和后端工程师协作完成产品功能',
                '参与前端技术选型和架构设计',
                '参与代码审查和技术分享',
                '持续学习新技术，提升技术能力'
            )
        WHEN `title` LIKE '%全栈%' OR `title` LIKE '%Full Stack%' THEN
            JSON_ARRAY(
                '负责前后端全栈开发工作',
                '参与产品需求分析和技术方案设计',
                '协调前后端技术实现和接口对接',
                '参与代码审查和技术分享',
                '持续学习新技术，提升技术能力'
            )
        WHEN `title` LIKE '%数据%' OR `title` LIKE '%分析%' OR `title` LIKE '%Data%' THEN
            JSON_ARRAY(
                '负责数据分析和挖掘工作',
                '设计和维护数据处理流程',
                '编写数据分析报告和可视化图表',
                '参与数据模型设计和优化',
                '持续学习新的数据分析技术'
            )
        WHEN `title` LIKE '%产品%' OR `title` LIKE '%Product%' THEN
            JSON_ARRAY(
                '负责产品规划和需求分析',
                '协调各部门完成产品功能开发',
                '进行用户调研和市场分析',
                '制定产品发展策略和路线图',
                '持续关注行业趋势和用户反馈'
            )
        ELSE
            JSON_ARRAY(
                '负责相关技术领域的开发工作',
                '参与项目需求分析和技术方案制定',
                '协作完成产品功能开发和优化',
                '参与代码审查和技术分享',
                '持续学习新技术，提升技术能力'
            )
    END
) WHERE `responsibilities` IS NULL;

-- 第三步：验证数据迁移结果
-- 检查是否所有记录都有responsibilities数据
SELECT 
    id, 
    title, 
    JSON_LENGTH(responsibilities) as responsibilities_count,
    JSON_EXTRACT(responsibilities, '$[0]') as first_responsibility
FROM `job` 
WHERE `responsibilities` IS NOT NULL
LIMIT 10;

-- 第四步：删除description列（注意：这是不可逆操作）
-- 在生产环境中，建议先备份数据
ALTER TABLE `job` DROP COLUMN `description`;

-- 第五步：更新表结构注释
ALTER TABLE `job` COMMENT = '职位信息表 - 已将description字段迁移为responsibilities JSON数组';

-- ===================================================================
-- 迁移完成确认
-- ===================================================================
-- 执行以下查询确认迁移成功：
-- SELECT id, title, responsibilities FROM job LIMIT 5;
-- 
-- 预期结果：
-- - 所有记录都应该有responsibilities字段
-- - responsibilities字段应该是JSON数组格式
-- - description字段应该已被删除
-- ===================================================================
