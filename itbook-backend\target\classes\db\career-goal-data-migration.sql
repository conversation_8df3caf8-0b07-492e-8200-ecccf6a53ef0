-- ===================================================================
-- ITBook 职业目标数据迁移脚本
-- 将前端careerGoals.ts中的数据迁移到数据库中
-- ===================================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 清空现有数据（如果需要重新导入）
-- DELETE FROM career_market_data;
-- DELETE FROM career_interview_topic;
-- DELETE FROM career_project;
-- DELETE FROM career_path;
-- DELETE FROM career_skill;
-- DELETE FROM career_goal;
-- DELETE FROM career_category;

-- ----------------------------
-- 插入职业分类数据
-- ----------------------------
INSERT INTO career_category (category_id, name, icon, description, sort_order, is_active) VALUES
('backend', '后端开发', 'server-outline', '服务端应用开发', 1, 1),
('frontend', '前端开发', 'desktop-outline', 'Web前端应用开发', 2, 1),
('mobile', '移动开发', 'phone-portrait-outline', '移动应用开发', 3, 1),
('testing', '测试开发', 'checkmark-circle-outline', '软件测试和质量保证', 4, 1),
('devops', '运维开发', 'settings-outline', '系统运维和DevOps', 5, 1),
('data', '数据开发', 'analytics-outline', '数据分析和大数据开发', 6, 1);

-- ----------------------------
-- 插入职业目标主数据
-- ----------------------------
INSERT INTO career_goal (id, name, category, description, icon, is_popular, is_active, created_at, updated_at) VALUES
(2, 'Java后端工程师', '后端开发', '负责服务端应用开发，使用Java技术栈构建高性能、可扩展的后端系统', 'server-outline', 1, 1, '2025-01-08 00:00:00', '2025-01-08 00:00:00'),
(3, 'React前端工程师', '前端开发', '负责Web前端应用开发，使用React技术栈构建用户界面', 'desktop-outline', 1, 1, '2025-01-08 00:00:00', '2025-01-08 00:00:00');

-- ----------------------------
-- 插入Java后端工程师技能数据
-- ----------------------------
INSERT INTO career_skill (career_goal_id, skill_id, skill_name, skill_category, importance, target_level, skill_type, description, learning_resources, assessment_criteria) VALUES
-- 核心技能
(2, 'java-basics', 'Java基础', '编程语言', 'critical', 'advanced', 'core', 'Java语法、面向对象编程、集合框架、多线程等', '["java-course-1", "java-book-1"]', '["语法熟练度", "代码质量", "问题解决能力"]'),
(2, 'spring-framework', 'Spring框架', '开发框架', 'critical', 'advanced', 'core', 'Spring Boot、Spring MVC、Spring Security等', '["spring-course-1", "spring-docs"]', '["框架理解", "实际应用", "最佳实践"]'),
(2, 'database-mysql', 'MySQL数据库', '数据库', 'critical', 'intermediate', 'core', 'SQL语法、数据库设计、性能优化等', '["mysql-course-1", "mysql-docs"]', '["SQL熟练度", "设计能力", "优化技能"]'),
(2, 'web-development', 'Web开发', 'Web技术', 'important', 'intermediate', 'core', 'HTTP协议、RESTful API、Web安全等', '["web-course-1"]', '["协议理解", "API设计", "安全意识"]'),
-- 加分技能
(2, 'microservices', '微服务架构', '架构设计', 'important', 'intermediate', 'bonus', '微服务设计、服务治理、分布式系统等', '["microservices-course-1"]', '["架构理解", "实践经验"]'),
(2, 'redis-cache', 'Redis缓存', '缓存技术', 'important', 'intermediate', 'bonus', 'Redis数据结构、缓存策略、集群配置等', '["redis-course-1"]', '["基础操作", "实际应用"]');

-- ----------------------------
-- 插入React前端工程师技能数据
-- ----------------------------
INSERT INTO career_skill (career_goal_id, skill_id, skill_name, skill_category, importance, target_level, skill_type, description, learning_resources, assessment_criteria) VALUES
-- 核心技能
(3, 'javascript-es6', 'JavaScript ES6+', '编程语言', 'critical', 'advanced', 'core', 'ES6+语法、异步编程、模块化等', '["js-course-1"]', '["语法熟练度", "异步处理", "代码质量"]'),
(3, 'react-framework', 'React框架', '前端框架', 'critical', 'advanced', 'core', 'React组件、Hooks、状态管理等', '["react-course-1"]', '["组件设计", "Hooks使用", "性能优化"]'),
-- 加分技能
(3, 'typescript', 'TypeScript', '编程语言', 'nice-to-have', 'intermediate', 'bonus', 'TypeScript类型系统、泛型、装饰器等', '["ts-course-1"]', '["类型定义", "代码质量"]');

-- ----------------------------
-- 插入职业发展路径数据
-- ----------------------------
INSERT INTO career_path (career_goal_id, level, duration, skills, projects, salary_min, salary_max) VALUES
-- Java后端工程师发展路径
(2, 'junior', '6-12个月', '["Java基础", "Spring Boot", "MySQL"]', '["个人博客系统", "TODO应用"]', 8, 15),
(2, 'mid', '1-2年', '["微服务架构", "Redis", "MQ"]', '["电商API系统", "内容管理系统"]', 15, 25),
(2, 'senior', '2-5年', '["系统架构", "性能优化", "团队管理"]', '["分布式系统", "高并发平台"]', 25, 40),
-- React前端工程师发展路径
(3, 'junior', '6-12个月', '["JavaScript", "React", "CSS"]', '["Todo应用", "个人网站"]', 8, 15),
(3, 'mid', '1-2年', '["TypeScript", "状态管理", "工程化"]', '["管理后台", "SPA应用"]', 15, 25),
(3, 'senior', '2-5年', '["架构设计", "性能优化", "团队协作"]', '["大型前端项目", "组件库"]', 25, 40);

-- ----------------------------
-- 插入项目模板数据
-- ----------------------------
INSERT INTO career_project (career_goal_id, project_id, title, description, difficulty, tech_stack, estimated_time, business_scenario, learning_objectives, deliverables) VALUES
-- Java后端项目
(2, 'blog-system', '个人博客系统', '基于Spring Boot的个人博客系统，包含文章管理、用户认证、评论功能', 'junior', '["Spring Boot", "MySQL", "MyBatis", "Thymeleaf"]', '2-3周', '个人博客网站', '["Spring Boot应用开发", "数据库设计", "用户认证"]', '["完整项目代码", "数据库设计文档", "部署说明"]'),
(2, 'ecommerce-api', '电商API系统', '电商平台的后端API系统，包含商品管理、订单处理、支付集成', 'mid', '["Spring Boot", "MySQL", "Redis", "RabbitMQ"]', '4-6周', '电商平台后端', '["微服务架构", "缓存应用", "消息队列"]', '["API文档", "系统架构图", "性能测试报告"]'),
-- React前端项目
(3, 'todo-app', 'Todo应用', '基于React的待办事项管理应用', 'junior', '["React", "TypeScript", "CSS Modules"]', '1-2周', '个人任务管理工具', '["React组件开发", "状态管理", "事件处理"]', '["完整应用代码", "部署链接", "功能说明"]');

-- ----------------------------
-- 插入面试题数据
-- ----------------------------
INSERT INTO career_interview_topic (career_goal_id, topic_id, category, topic, questions, difficulty, importance, preparation_tips) VALUES
-- Java后端面试题
(2, 'java-fundamentals', 'technical', 'Java基础', '["什么是面向对象编程？", "Java中的多态是什么？", "解释Java中的垃圾回收机制"]', 'intermediate', 'critical', '["复习Java核心概念", "准备代码示例", "理解JVM原理"]'),
(2, 'spring-framework-interview', 'technical', 'Spring框架', '["什么是依赖注入？", "Spring Boot的自动配置原理？", "如何处理事务管理？"]', 'intermediate', 'critical', '["深入理解Spring原理", "准备实际项目经验", "了解最新特性"]'),
-- React前端面试题
(3, 'react-basics', 'technical', 'React基础', '["React的虚拟DOM是什么？", "useState和useEffect的使用场景？", "React组件的生命周期？"]', 'intermediate', 'critical', '["复习React核心概念", "准备代码示例"]');

-- ----------------------------
-- 插入市场数据
-- ----------------------------
INSERT INTO career_market_data (career_goal_id, junior_salary_min, junior_salary_max, mid_salary_min, mid_salary_max, senior_salary_min, senior_salary_max, demand_trend, popular_cities, top_companies, skill_demand, last_updated) VALUES
-- Java后端工程师市场数据
(2, 8000, 15000, 15000, 25000, 25000, 40000, 'increasing', '["北京", "上海", "深圳", "杭州"]', '["阿里巴巴", "腾讯", "字节跳动", "美团"]', '{"Java": 95, "Spring Boot": 90, "MySQL": 85, "Redis": 70}', '2025-01-08'),
-- React前端工程师市场数据
(3, 8000, 15000, 15000, 25000, 25000, 40000, 'increasing', '["北京", "上海", "深圳", "杭州"]', '["阿里巴巴", "腾讯", "字节跳动", "美团"]', '{"React": 90, "JavaScript": 95, "TypeScript": 80, "Vue": 75}', '2025-01-08');

SET FOREIGN_KEY_CHECKS = 1;

-- 验证数据插入
SELECT 'Career Goals' as table_name, COUNT(*) as count FROM career_goal
UNION ALL
SELECT 'Career Skills', COUNT(*) FROM career_skill
UNION ALL
SELECT 'Career Paths', COUNT(*) FROM career_path
UNION ALL
SELECT 'Career Projects', COUNT(*) FROM career_project
UNION ALL
SELECT 'Interview Topics', COUNT(*) FROM career_interview_topic
UNION ALL
SELECT 'Market Data', COUNT(*) FROM career_market_data
UNION ALL
SELECT 'Categories', COUNT(*) FROM career_category;
