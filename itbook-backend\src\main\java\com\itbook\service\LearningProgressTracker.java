package com.itbook.service;

import com.itbook.entity.*;
import com.itbook.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习进度跟踪器
 * 
 * 提供实时学习进度监控和行为分析功能，为动态路径调整提供数据支持
 * 
 * 核心功能：
 * 1. 实时跟踪用户学习行为
 * 2. 分析学习模式和效率
 * 3. 识别学习瓶颈和问题
 * 4. 提供学习建议和预警
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LearningProgressTracker {

    private final UserStepProgressRepository stepProgressRepository;
    private final UserLearningPathProgressRepository pathProgressRepository;
    private final DynamicLearningPathRepository dynamicPathRepository;
    private final DynamicPathStepRepository dynamicStepRepository;
    private final UserRepository userRepository;

    /**
     * 学习行为事件类型
     */
    public enum LearningEventType {
        START_LEARNING,     // 开始学习
        PAUSE_LEARNING,     // 暂停学习
        RESUME_LEARNING,    // 恢复学习
        COMPLETE_STEP,      // 完成步骤
        SKIP_STEP,          // 跳过步骤
        RATE_CONTENT,       // 评价内容
        ADD_NOTE,           // 添加笔记
        SEEK_HELP,          // 寻求帮助
        REPEAT_CONTENT      // 重复学习
    }

    /**
     * 学习行为事件
     */
    public static class LearningEvent {
        private Long userId;
        private Long pathId;
        private Long stepId;
        private LearningEventType eventType;
        private LocalDateTime timestamp;
        private Map<String, Object> eventData;

        // Constructors
        public LearningEvent() {}

        public LearningEvent(Long userId, Long pathId, Long stepId, LearningEventType eventType) {
            this.userId = userId;
            this.pathId = pathId;
            this.stepId = stepId;
            this.eventType = eventType;
            this.timestamp = LocalDateTime.now();
            this.eventData = new HashMap<>();
        }

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public Long getStepId() { return stepId; }
        public void setStepId(Long stepId) { this.stepId = stepId; }
        
        public LearningEventType getEventType() { return eventType; }
        public void setEventType(LearningEventType eventType) { this.eventType = eventType; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        
        public Map<String, Object> getEventData() { return eventData; }
        public void setEventData(Map<String, Object> eventData) { this.eventData = eventData; }
    }

    /**
     * 学习行为分析结果
     */
    public static class LearningBehaviorAnalysis {
        private Long userId;
        private Long pathId;
        private LocalDateTime analysisTime;
        
        // 学习效率指标
        private Double averageLearningSpeed;        // 平均学习速度（分钟/步骤）
        private Double learningConsistency;         // 学习一致性（0-1）
        private Double focusLevel;                  // 专注度（0-1）
        
        // 学习模式分析
        private String learningPattern;             // 学习模式（BURST/STEADY/IRREGULAR）
        private List<String> preferredTimeSlots;   // 偏好学习时段
        private Integer averageSessionDuration;    // 平均学习时长（分钟）
        
        // 困难识别
        private List<String> strugglingAreas;      // 困难领域
        private List<Long> problematicSteps;       // 问题步骤
        private Double overallDifficulty;          // 整体难度感知
        
        // 预测指标
        private Double completionProbability;      // 完成概率
        private Integer estimatedRemainingDays;   // 预计剩余天数
        private String riskLevel;                  // 风险等级（LOW/MEDIUM/HIGH）
        
        // 建议和警告
        private List<String> recommendations;      // 学习建议
        private List<String> warnings;             // 预警信息

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public Long getPathId() { return pathId; }
        public void setPathId(Long pathId) { this.pathId = pathId; }
        
        public LocalDateTime getAnalysisTime() { return analysisTime; }
        public void setAnalysisTime(LocalDateTime analysisTime) { this.analysisTime = analysisTime; }
        
        public Double getAverageLearningSpeed() { return averageLearningSpeed; }
        public void setAverageLearningSpeed(Double averageLearningSpeed) { this.averageLearningSpeed = averageLearningSpeed; }
        
        public Double getLearningConsistency() { return learningConsistency; }
        public void setLearningConsistency(Double learningConsistency) { this.learningConsistency = learningConsistency; }
        
        public Double getFocusLevel() { return focusLevel; }
        public void setFocusLevel(Double focusLevel) { this.focusLevel = focusLevel; }
        
        public String getLearningPattern() { return learningPattern; }
        public void setLearningPattern(String learningPattern) { this.learningPattern = learningPattern; }
        
        public List<String> getPreferredTimeSlots() { return preferredTimeSlots; }
        public void setPreferredTimeSlots(List<String> preferredTimeSlots) { this.preferredTimeSlots = preferredTimeSlots; }
        
        public Integer getAverageSessionDuration() { return averageSessionDuration; }
        public void setAverageSessionDuration(Integer averageSessionDuration) { this.averageSessionDuration = averageSessionDuration; }
        
        public List<String> getStrugglingAreas() { return strugglingAreas; }
        public void setStrugglingAreas(List<String> strugglingAreas) { this.strugglingAreas = strugglingAreas; }
        
        public List<Long> getProblematicSteps() { return problematicSteps; }
        public void setProblematicSteps(List<Long> problematicSteps) { this.problematicSteps = problematicSteps; }
        
        public Double getOverallDifficulty() { return overallDifficulty; }
        public void setOverallDifficulty(Double overallDifficulty) { this.overallDifficulty = overallDifficulty; }
        
        public Double getCompletionProbability() { return completionProbability; }
        public void setCompletionProbability(Double completionProbability) { this.completionProbability = completionProbability; }
        
        public Integer getEstimatedRemainingDays() { return estimatedRemainingDays; }
        public void setEstimatedRemainingDays(Integer estimatedRemainingDays) { this.estimatedRemainingDays = estimatedRemainingDays; }
        
        public String getRiskLevel() { return riskLevel; }
        public void setRiskLevel(String riskLevel) { this.riskLevel = riskLevel; }
        
        public List<String> getRecommendations() { return recommendations; }
        public void setRecommendations(List<String> recommendations) { this.recommendations = recommendations; }
        
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings; }
    }

    /**
     * 记录学习行为事件
     * 
     * @param event 学习事件
     */
    public void recordLearningEvent(LearningEvent event) {
        log.debug("📝 记录学习事件: userId={}, eventType={}, stepId={}", 
                event.getUserId(), event.getEventType(), event.getStepId());

        try {
            // 这里可以将事件存储到数据库或消息队列
            // 暂时使用日志记录，后续可以扩展为持久化存储
            
            // 根据事件类型更新相关进度
            updateProgressBasedOnEvent(event);
            
            log.info("✅ 学习事件记录成功: userId={}, eventType={}", 
                    event.getUserId(), event.getEventType());
                    
        } catch (Exception e) {
            log.error("❌ 学习事件记录失败: userId={}, eventType={}", 
                    event.getUserId(), event.getEventType(), e);
        }
    }

    /**
     * 分析用户学习行为
     * 
     * @param userId 用户ID
     * @param pathId 路径ID
     * @return 学习行为分析结果
     */
    public LearningBehaviorAnalysis analyzeLearningBehavior(Long userId, Long pathId) {
        log.info("🔍 分析用户学习行为: userId={}, pathId={}", userId, pathId);

        try {
            LearningBehaviorAnalysis analysis = new LearningBehaviorAnalysis();
            analysis.setUserId(userId);
            analysis.setPathId(pathId);
            analysis.setAnalysisTime(LocalDateTime.now());

            // 获取用户的学习进度数据
            List<UserStepProgress> stepProgresses = getStepProgressesForPath(userId, pathId);
            
            if (stepProgresses.isEmpty()) {
                log.warn("⚠️ 用户暂无学习进度数据: userId={}, pathId={}", userId, pathId);
                return createDefaultAnalysis(analysis);
            }

            // 分析学习效率
            analyzeEfficiencyMetrics(analysis, stepProgresses);
            
            // 分析学习模式
            analyzeLearningPatterns(analysis, stepProgresses);
            
            // 识别困难和问题
            identifyStrugglingAreas(analysis, stepProgresses);
            
            // 预测和建议
            generatePredictionsAndRecommendations(analysis, stepProgresses);

            log.info("✅ 学习行为分析完成: userId={}, pathId={}, riskLevel={}", 
                    userId, pathId, analysis.getRiskLevel());

            return analysis;

        } catch (Exception e) {
            log.error("❌ 学习行为分析失败: userId={}, pathId={}", userId, pathId, e);
            throw new RuntimeException("学习行为分析失败", e);
        }
    }

    /**
     * 根据事件更新进度
     */
    private void updateProgressBasedOnEvent(LearningEvent event) {
        switch (event.getEventType()) {
            case START_LEARNING:
                handleStartLearningEvent(event);
                break;
            case COMPLETE_STEP:
                handleCompleteStepEvent(event);
                break;
            case PAUSE_LEARNING:
                handlePauseLearningEvent(event);
                break;
            case RATE_CONTENT:
                handleRateContentEvent(event);
                break;
            default:
                log.debug("事件类型暂不需要特殊处理: {}", event.getEventType());
        }
    }

    /**
     * 处理开始学习事件
     */
    private void handleStartLearningEvent(LearningEvent event) {
        if (event.getStepId() != null) {
            // 查找对应的步骤进度记录
            Optional<UserStepProgress> progressOpt = stepProgressRepository
                    .findByUserIdAndStepId(event.getUserId(), event.getStepId());

            if (progressOpt.isPresent()) {
                UserStepProgress progress = progressOpt.get();
                progress.startLearning();
                stepProgressRepository.save(progress);
                log.debug("更新步骤开始学习: stepId={}", event.getStepId());
            }
        }
    }

    /**
     * 处理完成步骤事件
     */
    private void handleCompleteStepEvent(LearningEvent event) {
        if (event.getStepId() != null) {
            Optional<UserStepProgress> progressOpt = stepProgressRepository
                    .findByUserIdAndStepId(event.getUserId(), event.getStepId());

            if (progressOpt.isPresent()) {
                UserStepProgress progress = progressOpt.get();
                progress.completeLearning();

                // 从事件数据中获取评分信息
                if (event.getEventData().containsKey("difficultyRating")) {
                    Integer rating = (Integer) event.getEventData().get("difficultyRating");
                    progress.setDifficultyRating(rating);
                }

                stepProgressRepository.save(progress);
                log.debug("更新步骤完成状态: stepId={}", event.getStepId());
            }
        }
    }

    /**
     * 处理暂停学习事件
     */
    private void handlePauseLearningEvent(LearningEvent event) {
        if (event.getStepId() != null) {
            Optional<UserStepProgress> progressOpt = stepProgressRepository
                    .findByUserIdAndStepId(event.getUserId(), event.getStepId());

            if (progressOpt.isPresent()) {
                UserStepProgress progress = progressOpt.get();
                progress.pauseLearning();
                stepProgressRepository.save(progress);
                log.debug("更新步骤暂停状态: stepId={}", event.getStepId());
            }
        }
    }

    /**
     * 处理内容评价事件
     */
    private void handleRateContentEvent(LearningEvent event) {
        if (event.getStepId() != null && event.getEventData().containsKey("rating")) {
            Optional<UserStepProgress> progressOpt = stepProgressRepository
                    .findByUserIdAndStepId(event.getUserId(), event.getStepId());

            if (progressOpt.isPresent()) {
                UserStepProgress progress = progressOpt.get();
                Integer rating = (Integer) event.getEventData().get("rating");
                progress.setQualityRating(rating);
                stepProgressRepository.save(progress);
                log.debug("更新内容评价: stepId={}, rating={}", event.getStepId(), rating);
            }
        }
    }

    /**
     * 获取路径的步骤进度列表
     */
    private List<UserStepProgress> getStepProgressesForPath(Long userId, Long pathId) {
        // 这里需要根据pathId查找对应的步骤，然后获取用户的进度
        // 暂时返回用户的所有步骤进度，后续可以优化为按路径过滤
        return stepProgressRepository.findByUserId(userId);
    }

    /**
     * 创建默认分析结果
     */
    private LearningBehaviorAnalysis createDefaultAnalysis(LearningBehaviorAnalysis analysis) {
        analysis.setAverageLearningSpeed(0.0);
        analysis.setLearningConsistency(0.0);
        analysis.setFocusLevel(0.0);
        analysis.setLearningPattern("UNKNOWN");
        analysis.setPreferredTimeSlots(new ArrayList<>());
        analysis.setAverageSessionDuration(0);
        analysis.setStrugglingAreas(new ArrayList<>());
        analysis.setProblematicSteps(new ArrayList<>());
        analysis.setOverallDifficulty(0.0);
        analysis.setCompletionProbability(0.0);
        analysis.setEstimatedRemainingDays(0);
        analysis.setRiskLevel("UNKNOWN");
        analysis.setRecommendations(Arrays.asList("开始学习以获得个性化分析"));
        analysis.setWarnings(new ArrayList<>());

        return analysis;
    }

    /**
     * 分析学习效率指标
     */
    private void analyzeEfficiencyMetrics(LearningBehaviorAnalysis analysis, List<UserStepProgress> stepProgresses) {
        // 计算平均学习速度
        double totalMinutes = stepProgresses.stream()
                .mapToInt(p -> p.getStudiedMinutes() != null ? p.getStudiedMinutes() : 0)
                .sum();

        long completedSteps = stepProgresses.stream()
                .filter(UserStepProgress::isCompleted)
                .count();

        double averageSpeed = completedSteps > 0 ? totalMinutes / completedSteps : 0.0;
        analysis.setAverageLearningSpeed(averageSpeed);

        // 计算学习一致性（基于学习时间的标准差）
        if (completedSteps > 1) {
            double[] studyTimes = stepProgresses.stream()
                    .filter(UserStepProgress::isCompleted)
                    .mapToDouble(p -> p.getStudiedMinutes() != null ? p.getStudiedMinutes() : 0)
                    .toArray();

            double consistency = calculateConsistency(studyTimes);
            analysis.setLearningConsistency(consistency);
        } else {
            analysis.setLearningConsistency(0.0);
        }

        // 计算专注度（基于完成率和重复学习次数）
        double completionRate = stepProgresses.isEmpty() ? 0.0 :
                (double) completedSteps / stepProgresses.size();
        analysis.setFocusLevel(Math.min(1.0, completionRate * 1.2)); // 简化计算
    }

    /**
     * 分析学习模式
     */
    private void analyzeLearningPatterns(LearningBehaviorAnalysis analysis, List<UserStepProgress> stepProgresses) {
        // 分析学习模式（基于学习时间分布）
        List<Integer> studyTimes = stepProgresses.stream()
                .filter(p -> p.getStudiedMinutes() != null && p.getStudiedMinutes() > 0)
                .map(UserStepProgress::getStudiedMinutes)
                .collect(Collectors.toList());

        if (studyTimes.isEmpty()) {
            analysis.setLearningPattern("UNKNOWN");
            analysis.setAverageSessionDuration(0);
        } else {
            // 计算平均学习时长
            int averageDuration = (int) studyTimes.stream().mapToInt(Integer::intValue).average().orElse(0);
            analysis.setAverageSessionDuration(averageDuration);

            // 判断学习模式
            double variance = calculateVariance(studyTimes);
            if (variance < 100) {
                analysis.setLearningPattern("STEADY");      // 稳定型
            } else if (variance > 500) {
                analysis.setLearningPattern("BURST");       // 爆发型
            } else {
                analysis.setLearningPattern("IRREGULAR");   // 不规律型
            }
        }

        // 分析偏好学习时段（简化版本）
        analysis.setPreferredTimeSlots(Arrays.asList("上午", "下午", "晚上"));
    }

    /**
     * 识别困难领域和问题步骤
     */
    private void identifyStrugglingAreas(LearningBehaviorAnalysis analysis, List<UserStepProgress> stepProgresses) {
        List<String> strugglingAreas = new ArrayList<>();
        List<Long> problematicSteps = new ArrayList<>();

        // 识别困难步骤（学习时间过长或评分较低）
        for (UserStepProgress progress : stepProgresses) {
            boolean isProblematic = false;

            // 学习时间过长
            if (progress.getStudiedMinutes() != null && progress.getStudiedMinutes() > 180) { // 超过3小时
                isProblematic = true;
            }

            // 难度评分较高
            if (progress.getDifficultyRating() != null && progress.getDifficultyRating() >= 4) {
                isProblematic = true;
            }

            // 质量评分较低
            if (progress.getQualityRating() != null && progress.getQualityRating() <= 2) {
                isProblematic = true;
            }

            if (isProblematic) {
                problematicSteps.add(progress.getStepId());
            }
        }

        analysis.setProblematicSteps(problematicSteps);
        analysis.setStrugglingAreas(strugglingAreas);

        // 计算整体难度感知
        double averageDifficulty = stepProgresses.stream()
                .filter(p -> p.getDifficultyRating() != null)
                .mapToInt(UserStepProgress::getDifficultyRating)
                .average()
                .orElse(3.0);
        analysis.setOverallDifficulty(averageDifficulty);
    }

    /**
     * 生成预测和建议
     */
    private void generatePredictionsAndRecommendations(LearningBehaviorAnalysis analysis, List<UserStepProgress> stepProgresses) {
        List<String> recommendations = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 计算完成概率
        long completedSteps = stepProgresses.stream().filter(UserStepProgress::isCompleted).count();
        long totalSteps = stepProgresses.size();
        double completionRate = totalSteps > 0 ? (double) completedSteps / totalSteps : 0.0;

        // 基于完成率和学习一致性预测完成概率
        double completionProbability = Math.min(1.0,
                completionRate * 0.7 + analysis.getLearningConsistency() * 0.3);
        analysis.setCompletionProbability(completionProbability);

        // 预计剩余天数（简化计算）
        int remainingSteps = (int) (totalSteps - completedSteps);
        int estimatedDays = (int) (remainingSteps * analysis.getAverageLearningSpeed() / 60 / 8); // 假设每天8小时
        analysis.setEstimatedRemainingDays(Math.max(1, estimatedDays));

        // 风险等级评估
        String riskLevel;
        if (completionProbability >= 0.8 && analysis.getLearningConsistency() >= 0.7) {
            riskLevel = "LOW";
        } else if (completionProbability >= 0.5 && analysis.getLearningConsistency() >= 0.4) {
            riskLevel = "MEDIUM";
        } else {
            riskLevel = "HIGH";
        }
        analysis.setRiskLevel(riskLevel);

        // 生成建议
        if ("HIGH".equals(riskLevel)) {
            recommendations.add("建议调整学习计划，降低学习难度");
            recommendations.add("考虑寻求额外的学习支持");
            warnings.add("当前学习进度存在较高风险，需要及时调整");
        } else if ("MEDIUM".equals(riskLevel)) {
            recommendations.add("保持当前学习节奏，注意学习一致性");
            recommendations.add("可以适当增加学习时间");
        } else {
            recommendations.add("学习状态良好，继续保持");
            recommendations.add("可以考虑挑战更高难度的内容");
        }

        // 基于学习模式的建议
        switch (analysis.getLearningPattern()) {
            case "BURST":
                recommendations.add("建议将爆发式学习分散到更多天数");
                break;
            case "IRREGULAR":
                recommendations.add("建议建立更规律的学习习惯");
                break;
            case "STEADY":
                recommendations.add("学习节奏很好，继续保持");
                break;
        }

        analysis.setRecommendations(recommendations);
        analysis.setWarnings(warnings);
    }

    /**
     * 计算一致性（基于标准差）
     */
    private double calculateConsistency(double[] values) {
        if (values.length <= 1) return 0.0;

        double mean = Arrays.stream(values).average().orElse(0.0);
        double variance = Arrays.stream(values)
                .map(x -> Math.pow(x - mean, 2))
                .average()
                .orElse(0.0);

        double stdDev = Math.sqrt(variance);

        // 将标准差转换为一致性分数（0-1），标准差越小一致性越高
        return Math.max(0.0, 1.0 - (stdDev / mean));
    }

    /**
     * 计算方差
     */
    private double calculateVariance(List<Integer> values) {
        if (values.isEmpty()) return 0.0;

        double mean = values.stream().mapToInt(Integer::intValue).average().orElse(0.0);
        return values.stream()
                .mapToDouble(x -> Math.pow(x - mean, 2))
                .average()
                .orElse(0.0);
    }
}
