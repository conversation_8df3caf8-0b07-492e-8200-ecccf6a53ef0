import React, { useState, useEffect } from 'react';
import { ScrollView, TouchableOpacity, FlatList, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import {
  View,
  Text,
  Card,
  ProgressBar,
  Button,
  Image
} from 'react-native-ui-lib';

import { MainTabScreenProps } from '../navigation/types';
import { useAuth } from '../hooks/useAuth';
import { useCourse } from '../hooks/useCourse';
import { LearningStatsCard } from '../components/learning/LearningStatsCard';
import { useThemeColors } from '../contexts/ThemeContext';
// import { useOffline } from '../hooks/useOffline'; // 已删除，离线功能已移除
import { useCareer, useCareerRecommendations } from '../hooks/useCareer';
import { PersonalizedRecommendationScreen } from './learning/PersonalizedRecommendationScreen';
import learningPathService from '../services/LearningPathService';
import { LearningPath, DifficultyLevel } from '../types';

const { width: screenWidth } = Dimensions.get('window');

type Props = MainTabScreenProps<'Study'>;

export const StudyScreen: React.FC<Props> = ({ navigation }) => {
  // 使用Redux状态
  const { isAuthenticated, user } = useAuth();
  const { myCourses, getLearningStats, fetchCourses } = useCourse();
  // 使用主题颜色
  const colors = useThemeColors();
  // 离线功能已移除
  // const { isOfflineMode, cacheContent, isContentCached } = useOffline();

  // 当前视图状态
  const [currentView, setCurrentView] = useState<'my-learning' | 'browse' | 'personalized-recommendations' | 'career-recommendations' | 'standard-paths' | 'project-workshop' | 'atomic-skills'>('my-learning');

  // 学习路径状态
  const [learningPaths, setLearningPaths] = useState<LearningPath[]>([]);
  const [isLoadingPaths, setIsLoadingPaths] = useState(false);
  const [pathsError, setPathsError] = useState<string | null>(null);

  // 使用职业相关Hook
  const { hasCareerSelected, careerPreference } = useCareer();
  const {
    recommendations,
    isLoading: isRecommendationsLoading,
    refresh: refreshRecommendations
  } = useCareerRecommendations(user?.id);

  // 获取学习统计数据
  const stats = getLearningStats();

  // 加载学习路径数据
  const loadLearningPaths = async () => {
    setIsLoadingPaths(true);
    setPathsError(null);
    try {
      const response = await learningPathService.getAllLearningPaths();
      if (response.code === 20000) {
        // 转换数据格式以匹配前端类型
        const pathsArray = response.data.content || response.data; // 兼容分页和非分页格式
        const paths = (pathsArray as any[]).map((path: any) => ({
          id: path.id.toString(),
          title: path.name,
          description: path.description,
          shortDescription: path.description,
          coverImage: path.coverImage || 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=250&fit=crop',
          techStack: path.techStack || 'FRONTEND',
          difficulty: (path.difficultyLevel?.toLowerCase() || 'beginner') as DifficultyLevel,
          estimatedHours: path.estimatedHours || 0,
          lessons: [],
          tags: path.tags || [],
          rating: path.rating || 0,
          reviewCount: path.reviewCount || 0,
          enrollmentCount: path.learnerCount || 0,
          price: 0,
          isPopular: path.isPopular || false,
          isNew: path.isNew || false,
          createdAt: path.createdAt || new Date().toISOString(),
          updatedAt: path.updatedAt || new Date().toISOString(),
          category: path.category || '通用',
          level: path.difficultyLevel || '入门'
        }));
        setLearningPaths(paths);
      } else {
        setPathsError(response.message || '获取学习路径失败');
      }
    } catch (error) {
      console.error('加载学习路径失败:', error);
      setPathsError('网络错误，请检查网络连接');
    } finally {
      setIsLoadingPaths(false);
    }
  };

  useEffect(() => {
    // 加载课程数据和学习路径数据
    fetchCourses();
    loadLearningPaths();
  }, []);

  const handleStartLearning = (pathId: string) => {
    // 导航到学习路径详情页面
    console.log('开始学习路径:', pathId);
    navigation.navigate('LearningPathDetail', {
      pathId: pathId,
      pathType: 'recommended'
    });
  };

  const handleViewProgress = () => {
    // TODO: 查看学习进度详情
    console.log('查看学习进度');
  };

  const handleOpenCodeEditor = () => {
    navigation.navigate('CodeEditor', {
      title: '在线代码编辑器',
      initialCode: '// 欢迎使用ITBook在线代码编辑器\nconsole.log("Hello, ITBook!");',
      language: 'javascript'
    });
  };

  const handleNavigateToStudyCheckIn = () => {
    navigation.navigate('StudyCheckIn');
  };

  // 缓存功能已移除
  const handleCacheContent = async (item: any) => {
    console.log('缓存功能已移除，课程将通过Redux状态管理');
    // 这里可以添加其他逻辑，比如添加到收藏夹
  };

  // 渲染学习路径卡片
  const renderLearningPathCard = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={{
        backgroundColor: colors.card,
        borderRadius: 12,
        padding: 16,
        marginHorizontal: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}
      onPress={() => handleStartLearning(item.id)}
    >
      <Image
        source={{ uri: item.coverImage }}
        style={{
          width: '100%',
          height: 120,
          borderRadius: 8,
          marginBottom: 12,
        }}
        resizeMode="cover"
      />

      <Text
        style={{
          fontSize: 16,
          fontWeight: '600',
          color: colors.text,
          marginBottom: 8,
        }}
        numberOfLines={2}
      >
        {item.title}
      </Text>

      <Text
        style={{
          fontSize: 14,
          color: colors.textSecondary,
          marginBottom: 12,
        }}
        numberOfLines={2}
      >
        {item.shortDescription}
      </Text>

      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Ionicons name="time-outline" size={14} color={colors.textSecondary} />
          <Text style={{ fontSize: 12, color: colors.textSecondary, marginLeft: 4 }}>
            {item.estimatedHours}小时
          </Text>
        </View>

        <Text style={{
          fontSize: 14,
          fontWeight: '600',
          color: item.price === 0 ? colors.success : colors.text,
        }}>
          {item.price === 0 ? '免费' : `¥${item.price}`}
        </Text>
      </View>

      {/* 缓存按钮 */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: colors.primary,
            paddingVertical: 8,
            paddingHorizontal: 16,
            borderRadius: 6,
            marginRight: 8,
          }}
          onPress={() => handleStartLearning(item.id)}
        >
          <Text style={{ color: 'white', fontSize: 14, fontWeight: '500', textAlign: 'center' }}>
            开始学习
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            backgroundColor: colors.primary + '20',
            paddingVertical: 8,
            paddingHorizontal: 12,
            borderRadius: 6,
          }}
          onPress={() => handleCacheContent(item)}
        >
          <Ionicons
            name="bookmark-outline"
            size={16}
            color={colors.primary}
          />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  // 渲染我的学习页面
  const renderMyLearning = () => (
    <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
      <View style={{ padding: 16 }}>
        {/* 当前学习路径 */}
        {isAuthenticated && myCourses.length > 0 ? (
          <View style={{
            backgroundColor: colors.card,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <Text style={{ fontSize: 18, fontWeight: '600', color: colors.text }}>
                当前学习路径
              </Text>
              <TouchableOpacity onPress={handleViewProgress}>
                <Text style={{ fontSize: 14, color: colors.primary }}>
                  查看详情
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
              <View style={{
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: colors.surface,
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 16,
              }}>
                <Ionicons name="code-slash" size={24} color={colors.primary} />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: '500', color: colors.text, marginBottom: 4 }}>
                  {myCourses[0]?.title || 'React从入门到精通'}
                </Text>
                <Text style={{ fontSize: 14, color: colors.textSecondary, marginBottom: 8 }}>
                  系统学习React开发，包含基础到高级的完整知识体系
                </Text>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{
                    flex: 1,
                    height: 6,
                    backgroundColor: colors.surface,
                    borderRadius: 3,
                    marginRight: 8,
                  }}>
                    <View style={{
                      width: '65%',
                      height: '100%',
                      backgroundColor: colors.primary,
                      borderRadius: 3,
                    }} />
                  </View>
                  <Text style={{ fontSize: 14, fontWeight: '500', color: colors.primary }}>
                    65%
                  </Text>
                </View>
              </View>
            </View>

            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: colors.primary,
                borderRadius: 8,
                paddingVertical: 12,
                paddingHorizontal: 16,
              }}
              onPress={() => handleStartLearning('path-1')}
            >
              <Text style={{ fontSize: 16, fontWeight: '500', color: 'white', marginRight: 8 }}>
                继续学习
              </Text>
              <Ionicons name="arrow-forward" size={16} color="white" />
            </TouchableOpacity>
          </View>
        ) : (
          <View style={{
            backgroundColor: colors.card,
            borderRadius: 12,
            padding: 24,
            marginBottom: 16,
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}>
            <Ionicons name="book-outline" size={48} color={colors.textSecondary} />
            <Text style={{ fontSize: 16, color: colors.textSecondary, marginTop: 12, textAlign: 'center' }}>
              {isAuthenticated ? '还没有开始学习课程' : '登录后开始你的学习之旅'}
            </Text>
            <TouchableOpacity
              style={{
                backgroundColor: colors.primary,
                borderRadius: 8,
                paddingVertical: 12,
                paddingHorizontal: 24,
                marginTop: 16,
              }}
              onPress={() => {
                if (isAuthenticated) {
                  setCurrentView('browse');
                } else {
                  navigation.navigate('Profile');
                }
              }}
            >
              <Text style={{ fontSize: 16, fontWeight: '500', color: 'white' }}>
                {isAuthenticated ? '浏览课程' : '立即登录'}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* 学习统计 - 使用高级统计组件 */}
        {isAuthenticated && user && (
          <LearningStatsCard userId={user.id.toString()} />
        )}

        {/* 动态调整功能入口 */}
        {isAuthenticated && user && (
          <View style={{
            backgroundColor: colors.card,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: colors.primary + '20',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="analytics" size={20} color={colors.primary} />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: '600', color: colors.text, marginBottom: 4 }}>
                  学习路径分析
                </Text>
                <Text style={{ fontSize: 14, color: colors.textSecondary }}>
                  智能分析学习效果，优化学习路径
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: colors.primary + '10',
                borderRadius: 8,
                paddingVertical: 12,
                paddingHorizontal: 16,
                borderWidth: 1,
                borderColor: colors.primary + '30',
              }}
              onPress={() => navigation.navigate('AnalysisReport', {
                userId: user.id,
                pathId: 1, // 这里应该是当前学习路径的ID
                pathName: myCourses[0]?.title || '当前学习路径'
              })}
            >
              <Ionicons name="trending-up" size={16} color={colors.primary} />
              <Text style={{ fontSize: 14, fontWeight: '500', color: colors.primary, marginLeft: 8 }}>
                查看学习分析
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* 个性化路径入口 */}
        {isAuthenticated && (
          <View style={{
            backgroundColor: colors.card,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: colors.primary + '20',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="bulb" size={20} color={colors.primary} />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: '600', color: colors.text, marginBottom: 4 }}>
                  个性化学习路径
                </Text>
                <Text style={{ fontSize: 14, color: colors.textSecondary }}>
                  基于你的技能水平，为你定制最适合的学习路径
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: colors.primary,
                borderRadius: 8,
                paddingVertical: 10,
                paddingHorizontal: 16,
              }}
              onPress={() => navigation.navigate('PersonalizedPath')}
            >
              <Text style={{ fontSize: 14, fontWeight: '500', color: 'white', marginRight: 6 }}>
                获取路径
              </Text>
              <Ionicons name="arrow-forward" size={14} color="white" />
            </TouchableOpacity>
          </View>
        )}

        {/* 路径生成向导入口 */}
        {isAuthenticated && (
          <View style={{
            backgroundColor: colors.card,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <View style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: colors.secondary + '20',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
                <Ionicons name="construct" size={20} color={colors.secondary} />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={{ fontSize: 16, fontWeight: '600', color: colors.text, marginBottom: 4 }}>
                  创建学习路径
                </Text>
                <Text style={{ fontSize: 14, color: colors.textSecondary }}>
                  通过向导一步步创建专属于你的个性化学习路径
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: colors.secondary,
                borderRadius: 8,
                paddingVertical: 10,
                paddingHorizontal: 16,
              }}
              onPress={() => navigation.navigate('PathGenerationWizard')}
            >
              <Text style={{ fontSize: 14, fontWeight: '500', color: 'white', marginRight: 6 }}>
                开始创建
              </Text>
              <Ionicons name="arrow-forward" size={14} color="white" />
            </TouchableOpacity>
          </View>
        )}

        {/* 快速访问按钮 */}
        {isAuthenticated && user && (
          <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
            {/* 第一行按钮 */}
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginBottom: 12,
            }}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: colors.card,
                  borderRadius: 12,
                  padding: 16,
                  marginRight: 6,
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                }}
                onPress={() => navigation.navigate('LearningPlan', { userId: user.id })}
              >
                <Ionicons name="calendar-outline" size={24} color={colors.primary} />
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: colors.text,
                  marginTop: 8,
                  textAlign: 'center'
                }}>
                  学习计划
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: colors.card,
                  borderRadius: 12,
                  padding: 16,
                  marginLeft: 6,
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                }}
                onPress={() => navigation.navigate('LearningPathDetail', {
                  userId: user?.id || 3,
                  pathType: 'personalized',
                  careerGoalId: careerPreference?.careerGoal?.id || 1
                })}
              >
                <Ionicons name="git-network-outline" size={24} color={colors.primary} />
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: colors.text,
                  marginTop: 8,
                  textAlign: 'center'
                }}>
                  学习路径
                </Text>
              </TouchableOpacity>
            </View>

            {/* 第二行按钮 */}
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: colors.card,
                  borderRadius: 12,
                  padding: 16,
                  marginRight: 6,
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                }}
                onPress={handleNavigateToStudyCheckIn}
              >
                <Ionicons name="checkmark-circle" size={24} color={colors.success} />
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: colors.text,
                  marginTop: 8,
                  textAlign: 'center'
                }}>
                  学习打卡
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flex: 1,
                  backgroundColor: colors.card,
                  borderRadius: 12,
                  padding: 16,
                  marginLeft: 6,
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                }}
                onPress={() => navigation.navigate('LearningDashboard', { userId: user.id })}
              >
                <Ionicons name="analytics-outline" size={24} color={colors.accent} />
                <Text style={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: colors.text,
                  marginTop: 8,
                  textAlign: 'center'
                }}>
                  学习数据
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </ScrollView>
  );

  // 渲染标准路径页面
  const renderStandardPaths = () => (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <View style={{
        padding: 16,
        backgroundColor: colors.card,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: colors.text,
          marginBottom: 8,
        }}>
          岗位标准学习路径
        </Text>
        <Text style={{
          fontSize: 14,
          color: colors.textSecondary,
          lineHeight: 20,
        }}>
          每个岗位的官方标准学习路径，经过市场验证，涵盖核心技能和知识点
        </Text>
      </View>

      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
      }}>
        <Ionicons name="map-outline" size={64} color={colors.primary} style={{ marginBottom: 16 }} />
        <Text style={{
          fontSize: 18,
          fontWeight: '600',
          color: colors.text,
          textAlign: 'center',
          marginBottom: 8,
        }}>
          探索标准学习路径
        </Text>
        <Text style={{
          fontSize: 14,
          color: colors.textSecondary,
          textAlign: 'center',
          lineHeight: 20,
          marginBottom: 24,
        }}>
          查看所有岗位的标准学习路径，找到最适合你的学习方向
        </Text>
        <Button
          label="查看所有标准路径"
          backgroundColor={colors.primary}
          color="#FFFFFF"
          borderRadius={8}
          paddingH-24
          paddingV-12
          onPress={() => navigation.navigate('StandardPath')}
        />
      </View>
    </View>
  );

  // 渲染职业相关页面
  const renderCareerRecommendations = () => {
    if (!hasCareerSelected) {
      return (
        <View style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.background,
          paddingHorizontal: 32,
        }}>
          <Ionicons name="briefcase-outline" size={64} color={colors.textSecondary} />
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: colors.text,
            marginTop: 16,
            marginBottom: 8,
            textAlign: 'center',
          }}>
            设置职业目标
          </Text>
          <Text style={{
            fontSize: 14,
            color: colors.textSecondary,
            textAlign: 'center',
            lineHeight: 20,
            marginBottom: 24,
          }}>
            设置你的目标岗位后，我们将为你定制最相关的学习内容和技能路径
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary,
              paddingVertical: 12,
              paddingHorizontal: 24,
              borderRadius: 8,
            }}
            onPress={() => navigation.navigate('CareerGoalSelection')}
          >
            <Text style={{
              color: 'white',
              fontSize: 16,
              fontWeight: '600',
            }}>
              选择目标岗位
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <ScrollView style={{ flex: 1, backgroundColor: colors.background }}>
        {/* 岗位信息卡片 */}
        <View style={{
          backgroundColor: colors.surface,
          margin: 16,
          borderRadius: 12,
          padding: 16,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
            <Ionicons name={careerPreference?.careerGoal?.icon as any} size={24} color={colors.primary} />
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: colors.text,
              marginLeft: 12,
            }}>
              {careerPreference?.careerGoal?.name}
            </Text>
          </View>
          <Text style={{
            fontSize: 14,
            color: colors.textSecondary,
            marginBottom: 16,
          }}>
            基于你的目标岗位，为你定制最相关的学习内容
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary + '20',
              paddingVertical: 8,
              paddingHorizontal: 12,
              borderRadius: 6,
              alignSelf: 'flex-start',
            }}
            onPress={refreshRecommendations}
          >
            <Text style={{
              color: colors.primary,
              fontSize: 12,
              fontWeight: '500',
            }}>
              刷新内容
            </Text>
          </TouchableOpacity>
        </View>

        {/* 个性化内容列表 */}
        {isRecommendationsLoading ? (
          <View style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            paddingVertical: 48,
          }}>
            <Text style={{
              fontSize: 14,
              color: colors.textSecondary,
            }}>
              正在生成个性化路径...
            </Text>
          </View>
        ) : recommendations.length === 0 ? (
          <View style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            paddingVertical: 48,
          }}>
            <Ionicons name="telescope-outline" size={48} color={colors.textSecondary} />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: colors.text,
              marginTop: 16,
              marginBottom: 8,
            }}>
              暂无个性化内容
            </Text>
            <Text style={{
              fontSize: 14,
              color: colors.textSecondary,
              textAlign: 'center',
            }}>
              我们正在为你准备个性化的学习路径
            </Text>
          </View>
        ) : (
          <FlatList
            data={recommendations.filter(item => item.type === 'course')}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={{
                  backgroundColor: colors.surface,
                  marginHorizontal: 16,
                  marginBottom: 12,
                  borderRadius: 12,
                  padding: 16,
                }}
                onPress={() => {
                  // TODO: 导航到课程详情
                  console.log('打开个性化课程:', item.title);
                }}
              >
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 8 }}>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: colors.text,
                    flex: 1,
                    marginRight: 12,
                  }} numberOfLines={2}>
                    {item.title}
                  </Text>
                  <View style={{
                    backgroundColor: colors.primary + '20',
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    borderRadius: 8,
                  }}>
                    <Text style={{
                      fontSize: 12,
                      fontWeight: '600',
                      color: colors.primary,
                    }}>
                      {Math.round(item.relevanceScore)}%
                    </Text>
                  </View>
                </View>

                <Text style={{
                  fontSize: 14,
                  color: colors.textSecondary,
                  marginBottom: 12,
                  lineHeight: 20,
                }} numberOfLines={3}>
                  {item.description}
                </Text>

                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Ionicons name="time-outline" size={14} color={colors.textSecondary} />
                    <Text style={{
                      fontSize: 12,
                      color: colors.textSecondary,
                      marginLeft: 4,
                    }}>
                      {item.estimatedTime}
                    </Text>
                  </View>
                  <View style={{
                    backgroundColor: colors.success + '20',
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    borderRadius: 8,
                  }}>
                    <Text style={{
                      fontSize: 10,
                      fontWeight: '500',
                      color: colors.success,
                    }}>
                      {item.difficulty === 'beginner' ? '入门' :
                       item.difficulty === 'intermediate' ? '中级' :
                       item.difficulty === 'advanced' ? '高级' : '专家'}
                    </Text>
                  </View>
                </View>

                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingTop: 8,
                  borderTopWidth: 1,
                  borderTopColor: colors.border,
                }}>
                  <Ionicons name="information-circle-outline" size={12} color={colors.textSecondary} />
                  <Text style={{
                    fontSize: 11,
                    color: colors.textSecondary,
                    marginLeft: 4,
                    flex: 1,
                  }} numberOfLines={1}>
                    {item.reason}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        )}
      </ScrollView>
    );
  };

  /**
   * 渲染原子技能内容
   */
  const renderAtomicSkills = () => {
    return (
      <View style={{ flex: 1, backgroundColor: colors.surface }}>
        <View style={{ padding: 16, alignItems: 'center', justifyContent: 'center', flex: 1 }}>
          <Ionicons name="library" size={64} color={colors.primary} />
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: colors.text,
            marginTop: 16,
            marginBottom: 8,
          }}>
            原子技能管理
          </Text>
          <Text style={{
            fontSize: 14,
            color: colors.textSecondary,
            textAlign: 'center',
            marginBottom: 24,
            lineHeight: 20,
          }}>
            精细化的技能管理系统，帮助你掌握每一个核心技能点
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary,
              paddingHorizontal: 24,
              paddingVertical: 12,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
            }}
            onPress={() => navigation.navigate('AtomicSkillList')}
          >
            <Ionicons name="library-outline" size={20} color="white" style={{ marginRight: 8 }} />
            <Text style={{
              color: 'white',
              fontSize: 16,
              fontWeight: '600',
            }}>
              浏览技能库
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  /**
   * 渲染项目实战内容
   */
  const renderProjectWorkshop = () => {
    return (
      <View style={{ flex: 1, backgroundColor: colors.surface }}>
        <View style={{ padding: 16, alignItems: 'center', justifyContent: 'center', flex: 1 }}>
          <Ionicons name="hammer" size={64} color={colors.primary} />
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: colors.text,
            marginTop: 16,
            marginBottom: 8,
          }}>
            项目实战平台
          </Text>
          <Text style={{
            fontSize: 14,
            color: colors.textSecondary,
            textAlign: 'center',
            marginBottom: 24,
            lineHeight: 20,
          }}>
            通过真实项目提升编程技能{'\n'}培养实际工作能力
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: colors.primary,
              paddingHorizontal: 24,
              paddingVertical: 12,
              borderRadius: 8,
            }}
            onPress={() => navigation.navigate('ProjectWorkshop')}
          >
            <Text style={{
              color: 'white',
              fontSize: 16,
              fontWeight: '600',
            }}>
              进入项目实战
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>

      {/* Tab Buttons */}
      <View style={{
        flexDirection: 'row',
        backgroundColor: colors.background,
        paddingHorizontal: 16,
        paddingBottom: 12,
      }}>
        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: 12,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: currentView === 'my-learning' ? colors.primary : 'transparent',
          }}
          onPress={() => setCurrentView('my-learning')}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: currentView === 'my-learning' ? colors.primary : colors.textSecondary,
          }}>
            我的学习
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: 12,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: currentView === 'browse' ? colors.primary : 'transparent',
          }}
          onPress={() => setCurrentView('browse')}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: currentView === 'browse' ? colors.primary : colors.textSecondary,
          }}>
            浏览课程
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: 12,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: currentView === 'personalized-recommendations' ? colors.primary : 'transparent',
          }}
          onPress={() => setCurrentView('personalized-recommendations')}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: currentView === 'personalized-recommendations' ? colors.primary : colors.textSecondary,
          }}>
            个性化路径
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: 12,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: currentView === 'standard-paths' ? colors.primary : 'transparent',
          }}
          onPress={() => setCurrentView('standard-paths')}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: currentView === 'standard-paths' ? colors.primary : colors.textSecondary,
          }}>
            标准路径
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: 12,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: currentView === 'career-recommendations' ? colors.primary : 'transparent',
          }}
          onPress={() => setCurrentView('career-recommendations')}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: currentView === 'career-recommendations' ? colors.primary : colors.textSecondary,
          }}>
            职业相关
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: 12,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: currentView === 'project-workshop' ? colors.primary : 'transparent',
          }}
          onPress={() => setCurrentView('project-workshop')}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: currentView === 'project-workshop' ? colors.primary : colors.textSecondary,
          }}>
            项目实战
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            flex: 1,
            paddingVertical: 12,
            alignItems: 'center',
            borderBottomWidth: 2,
            borderBottomColor: currentView === 'atomic-skills' ? colors.primary : 'transparent',
          }}
          onPress={() => setCurrentView('atomic-skills')}
        >
          <Text style={{
            fontSize: 14,
            fontWeight: '600',
            color: currentView === 'atomic-skills' ? colors.primary : colors.textSecondary,
          }}>
            原子技能
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={{ flex: 1 }}>
        {currentView === 'my-learning' ? (
          renderMyLearning()
        ) : currentView === 'browse' ? (
          <FlatList
            data={learningPaths}
            renderItem={renderLearningPathCard}
            keyExtractor={(item) => item.id}
            style={{ flex: 1, backgroundColor: colors.surface }}
            contentContainerStyle={{ paddingTop: 16, paddingBottom: 20 }}
            showsVerticalScrollIndicator={false}
            refreshing={isLoadingPaths}
            onRefresh={loadLearningPaths}
          />
        ) : currentView === 'personalized-recommendations' ? (
          <PersonalizedRecommendationScreen />
        ) : currentView === 'standard-paths' ? (
          renderStandardPaths()
        ) : currentView === 'career-recommendations' ? (
          renderCareerRecommendations()
        ) : currentView === 'atomic-skills' ? (
          renderAtomicSkills()
        ) : (
          renderProjectWorkshop()
        )}
      </View>
    </SafeAreaView>
  );
};


