/**
 * 证书资质编辑表单组件（简化版）
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../contexts/ThemeContext';
import { tokens } from '../../design-tokens';

interface CertificationFormData {
  name: string;
  issuer: string;
  issueDate: string;
  expiryDate: string;
  credentialId: string;
  credentialUrl: string;
  description: string;
  hasExpiry: boolean;
}

interface CertificationEditFormProps {
  resumeId: number;
  certification?: any;
  onSave: (certification: any) => void;
  onCancel: () => void;
  onDelete?: () => void;
}

export const CertificationEditForm: React.FC<CertificationEditFormProps> = ({
  resumeId,
  certification,
  onSave,
  onCancel,
  onDelete,
}) => {
  const colors = useThemeColors();
  const isEditing = !!certification;

  // 表单数据状态
  const [formData, setFormData] = useState<CertificationFormData>({
    name: certification?.name || '',
    issuer: certification?.issuer || '',
    issueDate: certification?.issueDate || '',
    expiryDate: certification?.expiryDate || '',
    credentialId: certification?.credentialId || '',
    credentialUrl: certification?.credentialUrl || '',
    description: certification?.description || '',
    hasExpiry: !!certification?.expiryDate,
  });

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = '请输入证书名称';
    }

    if (!formData.issuer.trim()) {
      newErrors.issuer = '请输入颁发机构';
    }

    if (!formData.issueDate.trim()) {
      newErrors.issueDate = '请输入颁发日期';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理字段变更
  const handleFieldChange = (field: keyof CertificationFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }

    if (field === 'hasExpiry' && !value) {
      setFormData(prev => ({
        ...prev,
        expiryDate: '',
      }));
    }
  };

  // 处理保存
  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('提示', '请检查表单中的错误信息');
      return;
    }

    try {
      setLoading(true);

      const saveData = {
        id: certification?.id || 0,
        name: formData.name.trim(),
        issuer: formData.issuer.trim(),
        issueDate: formData.issueDate,
        expiryDate: formData.hasExpiry ? formData.expiryDate || undefined : undefined,
        credentialId: formData.credentialId.trim() || undefined,
        credentialUrl: formData.credentialUrl.trim() || undefined,
        description: formData.description.trim() || undefined,
      };

      onSave(saveData);
    } catch (error) {
      console.error('保存证书资质失败:', error);
      Alert.alert('错误', '保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理删除
  const handleDelete = () => {
    Alert.alert(
      '确认删除',
      '确定要删除这个证书资质吗？此操作无法撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => onDelete?.(),
        },
      ]
    );
  };

  // 渲染输入框
  const renderInput = (
    label: string,
    value: string,
    onChangeText: (text: string) => void,
    placeholder: string,
    error?: string,
    multiline = false,
    required = false
  ) => (
    <View style={{ marginBottom: tokens.spacing('lg') }}>
      <Text style={{
        fontSize: tokens.fontSize('body'),
        fontWeight: tokens.fontWeight('medium') as any,
        color: colors.text,
        marginBottom: tokens.spacing('sm'),
      }}>
        {label}{required && <Text style={{ color: colors.error }}>*</Text>}
      </Text>
      <TextInput
        style={{
          borderWidth: 1,
          borderColor: error ? colors.error : colors.border,
          borderRadius: tokens.radius('sm'),
          padding: tokens.spacing('md'),
          fontSize: tokens.fontSize('body'),
          color: colors.text,
          backgroundColor: colors.surface,
          minHeight: multiline ? 80 : 44,
          textAlignVertical: multiline ? 'top' : 'center',
        }}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={colors.textSecondary}
        multiline={multiline}
        numberOfLines={multiline ? 4 : 1}
      />
      {error && (
        <Text style={{
          fontSize: tokens.fontSize('caption'),
          color: colors.error,
          marginTop: tokens.spacing('xs'),
        }}>
          {error}
        </Text>
      )}
    </View>
  );

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background,
    }}>
      {/* 头部 */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: tokens.spacing('lg'),
        paddingVertical: tokens.spacing('md'),
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
        backgroundColor: colors.surface,
      }}>
        <TouchableOpacity
          onPress={onCancel}
          activeOpacity={0.7}
        >
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>

        <Text style={{
          fontSize: tokens.fontSize('title-sm'),
          fontWeight: tokens.fontWeight('bold') as any,
          color: colors.text,
        }}>
          {isEditing ? '编辑证书资质' : '添加证书资质'}
        </Text>

        <TouchableOpacity
          onPress={handleSave}
          activeOpacity={0.7}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Text style={{
              fontSize: tokens.fontSize('body'),
              color: colors.primary,
              fontWeight: tokens.fontWeight('medium') as any,
            }}>
              保存
            </Text>
          )}
        </TouchableOpacity>
      </View>

      {/* 表单内容 */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          padding: tokens.spacing('lg'),
        }}
        showsVerticalScrollIndicator={false}
      >
        {/* 基本信息 */}
        {renderInput(
          '证书名称',
          formData.name,
          (text) => handleFieldChange('name', text),
          '如：AWS认证开发者、PMP项目管理',
          errors.name,
          false,
          true
        )}

        {renderInput(
          '颁发机构',
          formData.issuer,
          (text) => handleFieldChange('issuer', text),
          '如：Amazon Web Services、PMI',
          errors.issuer,
          false,
          true
        )}

        {renderInput(
          '颁发日期',
          formData.issueDate,
          (text) => handleFieldChange('issueDate', text),
          'YYYY-MM-DD',
          errors.issueDate,
          false,
          true
        )}

        {/* 过期日期开关 */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: tokens.spacing('lg'),
        }}>
          <Text style={{
            fontSize: tokens.fontSize('body'),
            fontWeight: tokens.fontWeight('medium') as any,
            color: colors.text,
          }}>
            设置过期日期
          </Text>
          <Switch
            value={formData.hasExpiry}
            onValueChange={(value) => handleFieldChange('hasExpiry', value)}
            trackColor={{ false: colors.border, true: colors.primary }}
            thumbColor={colors.surface}
          />
        </View>

        {/* 过期日期（如果开启） */}
        {formData.hasExpiry && renderInput(
          '过期日期',
          formData.expiryDate,
          (text) => handleFieldChange('expiryDate', text),
          'YYYY-MM-DD',
          errors.expiryDate
        )}

        {/* 证书详情 */}
        {renderInput(
          '证书编号',
          formData.credentialId,
          (text) => handleFieldChange('credentialId', text),
          '证书编号或ID（可选）',
          errors.credentialId
        )}

        {renderInput(
          '证书链接',
          formData.credentialUrl,
          (text) => handleFieldChange('credentialUrl', text),
          '证书验证链接（可选）',
          errors.credentialUrl
        )}

        {renderInput(
          '证书描述',
          formData.description,
          (text) => handleFieldChange('description', text),
          '证书相关的技能、成就或说明（可选）',
          errors.description,
          true
        )}

        {/* 删除按钮（编辑模式下显示） */}
        {isEditing && onDelete && (
          <TouchableOpacity
            onPress={handleDelete}
            activeOpacity={0.7}
            style={{
              backgroundColor: colors.error + '20',
              borderRadius: tokens.radius('sm'),
              padding: tokens.spacing('md'),
              alignItems: 'center',
              marginTop: tokens.spacing('lg'),
              marginBottom: tokens.spacing('xl'),
            }}
          >
            <Text style={{
              fontSize: tokens.fontSize('body'),
              color: colors.error,
              fontWeight: tokens.fontWeight('medium') as any,
            }}>
              删除这个证书资质
            </Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </View>
  );
};
