-- ===================================================================
-- 修复用户职业目标关联关系迁移脚本
-- 将 user_career_goal 表的 target_job_id 字段改为关联 career_goal 表
-- ===================================================================

-- 1. 删除现有的外键约束
ALTER TABLE `user_career_goal` DROP FOREIGN KEY `fk_user_career_goal_job`;

-- 2. 删除现有的索引
ALTER TABLE `user_career_goal` DROP INDEX `idx_target_job_id`;

-- 3. 重命名字段以更好地反映业务逻辑
ALTER TABLE `user_career_goal` CHANGE COLUMN `target_job_id` `career_goal_id` bigint(0) NULL DEFAULT NULL COMMENT '职业目标ID';

-- 4. 创建新的索引
ALTER TABLE `user_career_goal` ADD INDEX `idx_career_goal_id`(`career_goal_id`) USING BTREE;

-- 5. 添加新的外键约束，关联到 career_goal 表
ALTER TABLE `user_career_goal` ADD CONSTRAINT `fk_user_career_goal_career_goal` 
    FOREIGN KEY (`career_goal_id`) REFERENCES `career_goal` (`id`) 
    ON DELETE SET NULL ON UPDATE RESTRICT;

-- 6. 更新表注释
ALTER TABLE `user_career_goal` COMMENT = '用户职业目标表 - 关联职业目标模板而非具体职位';

-- ===================================================================
-- 数据迁移说明：
-- 由于原来的 target_job_id 关联的是 job 表，而现在要关联 career_goal 表，
-- 这两个表的ID不兼容，因此需要清空现有数据或进行数据映射。
-- 
-- 选项1：清空现有数据（推荐用于开发环境）
-- DELETE FROM user_career_goal;
-- 
-- 选项2：数据映射（生产环境需要根据具体业务逻辑进行映射）
-- 需要根据 job 表中的职位信息映射到对应的 career_goal
-- ===================================================================

-- 开发环境：清空现有数据（取消注释以执行）
-- DELETE FROM user_career_goal WHERE career_goal_id IS NOT NULL;
-- UPDATE user_career_goal SET career_goal_id = NULL;

COMMIT;
