/**
 * 个人信息服务
 * 
 * <AUTHOR> Team
 * @since 2025-07-17
 */

import ApiService from './ApiService';

export interface PersonalInfoUpdateData {
  fullName: string;
  email: string;
  phone: string;
  location?: string;
  website?: string;
  linkedin?: string;
  github?: string;
}

export interface PersonalInfoResponse {
  id: number;
  resumeId: number;
  fullName: string;
  email: string;
  phone: string;
  location?: string;
  website?: string;
  linkedin?: string;
  github?: string;
  createdAt: string;
  updatedAt: string;
}

class PersonalInfoService {
  /**
   * 更新个人信息
   */
  static async updatePersonalInfo(resumeId: number, data: PersonalInfoUpdateData): Promise<PersonalInfoResponse> {
    console.log('🔄 更新个人信息:', { resumeId, data });
    
    try {
      const response = await ApiService.put(`/resumes/${resumeId}/personal-info`, data);
      console.log('✅ 个人信息更新成功:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ 个人信息更新失败:', error);
      throw error;
    }
  }
}

export default PersonalInfoService;
