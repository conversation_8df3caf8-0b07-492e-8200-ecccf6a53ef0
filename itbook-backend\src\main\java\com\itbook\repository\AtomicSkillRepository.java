package com.itbook.repository;

import com.itbook.entity.AtomicSkill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 原子技能数据访问接口
 * 提供原子技能的数据库操作方法
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Repository
public interface AtomicSkillRepository extends JpaRepository<AtomicSkill, Long> {

    /**
     * 根据技能编码查找技能
     */
    AtomicSkill findBySkillCode(String skillCode);

    /**
     * 检查技能编码是否存在
     */
    boolean existsBySkillCode(String skillCode);

    /**
     * 根据技能名称查找技能
     */
    List<AtomicSkill> findByNameContainingIgnoreCase(String name);

    /**
     * 检查技能名称是否存在（仅活跃状态）
     */
    boolean existsByNameAndIsActiveTrue(String name);

    /**
     * 分页查询活跃状态的技能（按创建时间倒序）
     */
    Page<AtomicSkill> findByIsActiveTrueOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 根据分类查找技能
     */
    List<AtomicSkill> findByCategory(String category);

    /**
     * 根据分类分页查询活跃状态的技能（按创建时间倒序）
     */
    Page<AtomicSkill> findByCategoryAndIsActiveTrueOrderByCreatedAtDesc(String category, Pageable pageable);

    /**
     * 根据名称搜索活跃状态的技能（按创建时间倒序）
     */
    Page<AtomicSkill> findByNameContainingIgnoreCaseAndIsActiveTrueOrderByCreatedAtDesc(String name, Pageable pageable);

    /**
     * 根据分类和子分类查找技能
     */
    List<AtomicSkill> findByCategoryAndSubcategory(String category, String subcategory);

    /**
     * 根据难度级别查找技能
     */
    List<AtomicSkill> findByDifficultyLevel(AtomicSkill.DifficultyLevel difficultyLevel);

    /**
     * 根据难度级别分页查询活跃状态的技能（按创建时间倒序）
     */
    Page<AtomicSkill> findByDifficultyLevelAndIsActiveTrueOrderByCreatedAtDesc(AtomicSkill.DifficultyLevel difficultyLevel, Pageable pageable);

    /**
     * 根据技能类型分页查询活跃状态的技能（按创建时间倒序）
     */
    Page<AtomicSkill> findBySkillTypeAndIsActiveTrueOrderByCreatedAtDesc(AtomicSkill.SkillType skillType, Pageable pageable);

    /**
     * 获取热门技能（按平均评分和创建时间排序）
     */
    Page<AtomicSkill> findByIsActiveTrueOrderByAverageRatingDescCreatedAtDesc(Pageable pageable);

    /**
     * 获取所有活跃状态的技能分类
     */
    @Query("SELECT DISTINCT a.category FROM AtomicSkill a WHERE a.isActive = true ORDER BY a.category")
    List<String> findDistinctCategoriesByIsActiveTrue();

    /**
     * 根据技能类型查找技能
     */
    List<AtomicSkill> findBySkillType(AtomicSkill.SkillType skillType);

    /**
     * 根据状态查找技能
     */
    List<AtomicSkill> findByStatus(AtomicSkill.Status status);

    /**
     * 根据多个条件分页查询技能
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "(:category IS NULL OR a.category = :category) AND " +
           "(:subcategory IS NULL OR a.subcategory = :subcategory) AND " +
           "(:difficultyLevel IS NULL OR a.difficultyLevel = :difficultyLevel) AND " +
           "(:status IS NULL OR a.status = :status) AND " +
           "a.isActive = true " +
           "ORDER BY a.createdAt DESC")
    Page<AtomicSkill> findByFilters(@Param("category") String category,
                                   @Param("subcategory") String subcategory,
                                   @Param("difficultyLevel") AtomicSkill.DifficultyLevel difficultyLevel,
                                   @Param("status") AtomicSkill.Status status,
                                   Pageable pageable);

    /**
     * 根据关键词搜索技能
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "(LOWER(a.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.keywords) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "ORDER BY a.learnerCount DESC, a.averageRating DESC")
    List<AtomicSkill> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 根据技能ID集合查找技能
     */
    List<AtomicSkill> findByIdIn(Set<Long> skillIds);

    /**
     * 获取热门技能（按学习人数排序）
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "ORDER BY a.learnerCount DESC, a.averageRating DESC")
    List<AtomicSkill> findPopularSkills(Pageable pageable);

    /**
     * 获取推荐技能（按评分和完成率排序）
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' AND " +
           "a.averageRating >= 4.0 AND a.completionRate >= 0.7 " +
           "ORDER BY a.averageRating DESC, a.completionRate DESC")
    List<AtomicSkill> findRecommendedSkills(Pageable pageable);

    /**
     * 根据职业目标推荐技能
     * 通过职业技能映射关系查找推荐的原子技能
     */
    @Query("SELECT DISTINCT a FROM AtomicSkill a " +
           "JOIN CareerSkillMapping csm ON a.id = csm.atomicSkillId " +
           "JOIN CareerSkill cs ON csm.careerSkillId = cs.id " +
           "WHERE cs.careerGoal.id = :careerGoalId AND " +
           "a.isActive = true AND a.status = 'PUBLISHED' AND " +
           "a.id NOT IN :excludeSkillIds " +
           "ORDER BY csm.weight DESC, a.averageRating DESC")
    List<AtomicSkill> findRecommendedSkillsForCareer(@Param("careerGoalId") Long careerGoalId,
                                                    @Param("excludeSkillIds") Set<Long> excludeSkillIds);

    /**
     * 获取技能分类统计
     */
    @Query("SELECT a.category, COUNT(a) FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "GROUP BY a.category ORDER BY COUNT(a) DESC")
    List<Object[]> getCategoryStatistics();

    /**
     * 获取难度级别统计
     */
    @Query("SELECT a.difficultyLevel, COUNT(a) FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "GROUP BY a.difficultyLevel ORDER BY a.difficultyLevel")
    List<Object[]> getDifficultyStatistics();

    /**
     * 获取技能类型统计
     */
    @Query("SELECT a.skillType, COUNT(a) FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "GROUP BY a.skillType ORDER BY COUNT(a) DESC")
    List<Object[]> getSkillTypeStatistics();

    /**
     * 根据预计学习时长范围查找技能
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "a.estimatedHours BETWEEN :minHours AND :maxHours AND " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "ORDER BY a.estimatedHours ASC")
    List<AtomicSkill> findByEstimatedHoursRange(@Param("minHours") Integer minHours,
                                               @Param("maxHours") Integer maxHours);

    /**
     * 根据评分范围查找技能
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "a.averageRating BETWEEN :minRating AND :maxRating AND " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "ORDER BY a.averageRating DESC")
    List<AtomicSkill> findByRatingRange(@Param("minRating") Double minRating,
                                       @Param("maxRating") Double maxRating);

    /**
     * 获取最近创建的技能
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "ORDER BY a.createdAt DESC")
    List<AtomicSkill> findRecentSkills(Pageable pageable);

    /**
     * 获取最近更新的技能
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "ORDER BY a.updatedAt DESC")
    List<AtomicSkill> findRecentlyUpdatedSkills(Pageable pageable);

    /**
     * 根据标签查找技能
     */
    @Query("SELECT a FROM AtomicSkill a WHERE " +
           "LOWER(a.keywords) LIKE LOWER(CONCAT('%', :tag, '%')) AND " +
           "a.isActive = true AND a.status = 'PUBLISHED' " +
           "ORDER BY a.averageRating DESC")
    List<AtomicSkill> findByTag(@Param("tag") String tag);

    /**
     * 获取技能总数统计
     */
    @Query("SELECT COUNT(a) FROM AtomicSkill a WHERE a.isActive = true AND a.status = 'PUBLISHED'")
    Long countActiveSkills();

    /**
     * 获取平均评分
     */
    @Query("SELECT AVG(a.averageRating) FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' AND a.averageRating > 0")
    Double getAverageRating();

    /**
     * 获取平均完成率
     */
    @Query("SELECT AVG(a.completionRate) FROM AtomicSkill a WHERE " +
           "a.isActive = true AND a.status = 'PUBLISHED' AND a.completionRate > 0")
    Double getAverageCompletionRate();

    /**
     * 批量更新技能状态
     */
    @Query("UPDATE AtomicSkill a SET a.status = :status, a.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE a.id IN :skillIds")
    int updateStatusBatch(@Param("skillIds") Set<Long> skillIds, @Param("status") AtomicSkill.Status status);

    /**
     * 批量更新技能活跃状态
     */
    @Query("UPDATE AtomicSkill a SET a.isActive = :isActive, a.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE a.id IN :skillIds")
    int updateActiveStatusBatch(@Param("skillIds") Set<Long> skillIds, @Param("isActive") Boolean isActive);

}
