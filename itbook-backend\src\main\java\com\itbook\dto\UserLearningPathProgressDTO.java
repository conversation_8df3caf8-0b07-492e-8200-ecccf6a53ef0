package com.itbook.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户学习路径进度DTO
 * 用于解决Hibernate懒加载问题
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Data
public class UserLearningPathProgressDTO {

    /**
     * 学习状态枚举
     */
    public enum Status {
        NOT_STARTED,   // 未开始
        IN_PROGRESS,   // 进行中
        COMPLETED,     // 已完成
        PAUSED,        // 已暂停
        ABANDONED      // 已放弃
    }

    /**
     * 进度记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 学习路径ID
     */
    private Long learningPathId;

    /**
     * 学习路径基本信息
     */
    private LearningPathDTO learningPath;

    /**
     * 关联的职业目标ID
     */
    private Long careerGoalId;

    /**
     * 学习状态
     */
    private Status status;

    /**
     * 完成百分比
     */
    private BigDecimal completionPercentage;

    /**
     * 已完成步骤数
     */
    private Integer completedSteps;

    /**
     * 总步骤数
     */
    private Integer totalSteps;

    /**
     * 开始学习时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startedAt;

    /**
     * 最后学习时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastStudiedAt;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedAt;

    /**
     * 预计完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime estimatedCompletionAt;

    /**
     * 学习时长（分钟）
     */
    private Integer studyTimeMinutes;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
