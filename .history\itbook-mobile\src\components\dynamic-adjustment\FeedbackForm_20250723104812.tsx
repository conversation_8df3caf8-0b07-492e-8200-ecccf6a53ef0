/**
 * 用户反馈表单组件
 * 
 * 收集用户对学习内容的反馈和评价
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */

import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '../../hooks/useThemeColors';
import { tokens } from '../../design-tokens';
import { DynamicAdjustmentService } from '../../services/DynamicAdjustmentService';
import {
  FeedbackFormProps,
  FeedbackType,
  FeedbackSentiment,
  FeedbackRequest
} from '../../types/DynamicAdjustment';

/**
 * 用户反馈表单组件
 */
export const FeedbackForm: React.FC<FeedbackFormProps> = ({
  userId,
  pathId,
  stepId,
  onFeedbackSubmit
}) => {
  const colors = useThemeColors();
  const [feedbackType, setFeedbackType] = useState<FeedbackType>(FeedbackType.GENERAL_COMMENT);
  const [rating, setRating] = useState<number>(0);
  const [comment, setComment] = useState<string>('');
  const [sentiment, setSentiment] = useState<FeedbackSentiment>(FeedbackSentiment.NEUTRAL);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const feedbackTypes = [
    { value: FeedbackType.DIFFICULTY_RATING, label: '难度评分', icon: 'speedometer-outline', requiresRating: true },
    { value: FeedbackType.QUALITY_RATING, label: '质量评分', icon: 'star-outline', requiresRating: true },
    { value: FeedbackType.CONTENT_FEEDBACK, label: '内容反馈', icon: 'document-text-outline', requiresRating: false },
    { value: FeedbackType.LEARNING_EXPERIENCE, label: '学习体验', icon: 'happy-outline', requiresRating: false },
    { value: FeedbackType.SUGGESTION, label: '改进建议', icon: 'bulb-outline', requiresRating: false },
    { value: FeedbackType.BUG_REPORT, label: '问题报告', icon: 'bug-outline', requiresRating: false },
    { value: FeedbackType.GENERAL_COMMENT, label: '一般评论', icon: 'chatbubble-outline', requiresRating: false }
  ];

  const sentiments = [
    { value: FeedbackSentiment.POSITIVE, label: '积极', icon: 'happy', color: '#4CAF50' },
    { value: FeedbackSentiment.NEUTRAL, label: '中性', icon: 'remove-circle', color: '#FFC107' },
    { value: FeedbackSentiment.NEGATIVE, label: '消极', icon: 'sad', color: '#F44336' }
  ];

  const selectedFeedbackType = feedbackTypes.find(type => type.value === feedbackType);
  const requiresRating = selectedFeedbackType?.requiresRating || false;

  const handleSubmit = async () => {
    // 验证表单
    if (requiresRating && rating === 0) {
      Alert.alert('提示', '请选择评分');
      return;
    }

    if (!comment.trim()) {
      Alert.alert('提示', '请输入反馈内容');
      return;
    }

    try {
      setIsSubmitting(true);

      const feedbackRequest: FeedbackRequest = {
        userId,
        pathId,
        stepId,
        feedbackType,
        rating: requiresRating ? rating : undefined,
        comment: comment.trim(),
        sentiment,
        metadata: {
          deviceType: 'mobile',
          timestamp: new Date().toISOString(),
          hasRating: requiresRating,
          commentLength: comment.trim().length
        }
      };

      const result = await DynamicAdjustmentService.submitFeedback(feedbackRequest);

      Alert.alert(
        '提交成功',
        result.adjustmentTriggered ? '感谢您的反馈！系统已根据您的反馈进行了路径调整。' : '感谢您的反馈！',
        [{ 
          text: '确定', 
          onPress: () => {
            // 重置表单
            setRating(0);
            setComment('');
            setSentiment(FeedbackSentiment.NEUTRAL);
            onFeedbackSubmit?.(true);
          }
        }]
      );

    } catch (error) {
      console.error('提交反馈失败:', error);
      Alert.alert('错误', '提交反馈失败，请稍后重试');
      onFeedbackSubmit?.(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderRatingStars = () => {
    if (!requiresRating) return null;

    return (
      <View style={{ marginBottom: tokens.spacing('lg') }}>
        <Text style={{
          color: colors.text,
          fontSize: tokens.fontSize('md'),
          fontWeight: tokens.fontWeight('medium'),
          marginBottom: tokens.spacing('sm')
        }}>
          评分 *
        </Text>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          {[1, 2, 3, 4, 5].map((star) => (
            <TouchableOpacity
              key={star}
              onPress={() => setRating(star)}
              style={{ padding: tokens.spacing('sm') }}
              activeOpacity={0.7}
            >
              <Ionicons
                name={star <= rating ? 'star' : 'star-outline'}
                size={32}
                color={star <= rating ? '#FFC107' : colors.outline}
              />
            </TouchableOpacity>
          ))}
        </View>
        <Text style={{
          color: colors.textSecondary,
          fontSize: tokens.fontSize('sm'),
          textAlign: 'center',
          marginTop: tokens.spacing('xs')
        }}>
          {rating === 0 ? '请选择评分' : 
           rating === 1 ? '很差' :
           rating === 2 ? '较差' :
           rating === 3 ? '一般' :
           rating === 4 ? '良好' : '优秀'}
        </Text>
      </View>
    );
  };

  return (
    <ScrollView style={{
      backgroundColor: colors.surface,
      borderRadius: tokens.radius('md'),
      margin: tokens.spacing('md'),
      maxHeight: 600
    }}>
      <View style={{ padding: tokens.spacing('lg') }}>
        {/* 标题 */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: tokens.spacing('lg')
        }}>
          <Ionicons name="chatbubble-ellipses-outline" size={24} color={colors.primary} />
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('title-sm'),
            fontWeight: tokens.fontWeight('bold'),
            marginLeft: tokens.spacing('sm')
          }}>
            提交反馈
          </Text>
        </View>

        {/* 反馈类型选择 */}
        <View style={{ marginBottom: tokens.spacing('lg') }}>
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('md'),
            fontWeight: tokens.fontWeight('medium'),
            marginBottom: tokens.spacing('sm')
          }}>
            反馈类型 *
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={{ flexDirection: 'row' }}>
              {feedbackTypes.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={{
                    backgroundColor: feedbackType === type.value ? colors.primaryContainer : colors.surfaceVariant,
                    paddingHorizontal: tokens.spacing('md'),
                    paddingVertical: tokens.spacing('sm'),
                    borderRadius: tokens.radius('sm'),
                    marginRight: tokens.spacing('sm'),
                    alignItems: 'center',
                    minWidth: 80
                  }}
                  onPress={() => setFeedbackType(type.value)}
                  activeOpacity={0.7}
                >
                  <Ionicons
                    name={type.icon as any}
                    size={20}
                    color={feedbackType === type.value ? colors.onPrimaryContainer : colors.onSurfaceVariant}
                  />
                  <Text style={{
                    color: feedbackType === type.value ? colors.onPrimaryContainer : colors.onSurfaceVariant,
                    fontSize: tokens.fontSize('xs'),
                    fontWeight: tokens.fontWeight('medium'),
                    marginTop: tokens.spacing('xs'),
                    textAlign: 'center'
                  }}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* 评分 */}
        {renderRatingStars()}

        {/* 反馈内容 */}
        <View style={{ marginBottom: tokens.spacing('lg') }}>
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('md'),
            fontWeight: tokens.fontWeight('medium'),
            marginBottom: tokens.spacing('sm')
          }}>
            反馈内容 *
          </Text>
          <TextInput
            style={{
              backgroundColor: colors.surfaceVariant,
              borderRadius: tokens.radius('sm'),
              padding: tokens.spacing('md'),
              color: colors.onSurfaceVariant,
              fontSize: tokens.fontSize('md'),
              minHeight: 100,
              textAlignVertical: 'top'
            }}
            placeholder="请详细描述您的反馈..."
            placeholderTextColor={colors.outline}
            value={comment}
            onChangeText={setComment}
            multiline
            numberOfLines={4}
          />
          <Text style={{
            color: colors.textSecondary,
            fontSize: tokens.fontSize('xs'),
            textAlign: 'right',
            marginTop: tokens.spacing('xs')
          }}>
            {comment.length}/500
          </Text>
        </View>

        {/* 情感倾向 */}
        <View style={{ marginBottom: tokens.spacing('xl') }}>
          <Text style={{
            color: colors.text,
            fontSize: tokens.fontSize('md'),
            fontWeight: tokens.fontWeight('medium'),
            marginBottom: tokens.spacing('sm')
          }}>
            情感倾向
          </Text>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-around'
          }}>
            {sentiments.map((sentimentOption) => (
              <TouchableOpacity
                key={sentimentOption.value}
                style={{
                  alignItems: 'center',
                  padding: tokens.spacing('md'),
                  borderRadius: tokens.radius('sm'),
                  backgroundColor: sentiment === sentimentOption.value ? 
                    `${sentimentOption.color}20` : 'transparent',
                  borderWidth: sentiment === sentimentOption.value ? 2 : 1,
                  borderColor: sentiment === sentimentOption.value ? 
                    sentimentOption.color : colors.outline
                }}
                onPress={() => setSentiment(sentimentOption.value)}
                activeOpacity={0.7}
              >
                <Ionicons
                  name={sentimentOption.icon as any}
                  size={24}
                  color={sentiment === sentimentOption.value ? 
                    sentimentOption.color : colors.textSecondary}
                />
                <Text style={{
                  color: sentiment === sentimentOption.value ? 
                    sentimentOption.color : colors.textSecondary,
                  fontSize: tokens.fontSize('sm'),
                  fontWeight: tokens.fontWeight('medium'),
                  marginTop: tokens.spacing('xs')
                }}>
                  {sentimentOption.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 提交按钮 */}
        <TouchableOpacity
          style={{
            backgroundColor: colors.primary,
            paddingVertical: tokens.spacing('md'),
            borderRadius: tokens.radius('sm'),
            alignItems: 'center',
            opacity: isSubmitting ? 0.6 : 1
          }}
          onPress={handleSubmit}
          disabled={isSubmitting}
          activeOpacity={0.7}
        >
          {isSubmitting ? (
            <ActivityIndicator size="small" color={colors.onPrimary} />
          ) : (
            <>
              <Ionicons name="send" size={20} color={colors.onPrimary} />
              <Text style={{
                color: colors.onPrimary,
                fontSize: tokens.fontSize('md'),
                fontWeight: tokens.fontWeight('medium'),
                marginTop: tokens.spacing('xs')
              }}>
                提交反馈
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};
