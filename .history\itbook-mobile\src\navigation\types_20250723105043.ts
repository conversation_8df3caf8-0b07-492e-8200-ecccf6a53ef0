import { NavigatorScreenParams } from '@react-navigation/native';

// Root Stack Navigator
export type RootStackParamList = {
  Main: NavigatorScreenParams<MainTabParamList>;
  Modal: { title: string; content?: string };
  Settings: undefined;
  Auth: NavigatorScreenParams<AuthStackParamList>;
  ComponentDemo: undefined;
  ArticleDetail: { articleId: string };
  QuestionDetail: { questionId: number };
  NewsDetail: { newsId: string };
  JobDetail: { jobId: string };
  CompanyDetail: { companyId?: string; companyName?: string };
  ResumeCreate: undefined;
  ResumeDetail: { resumeId: string };

  // 学习相关页面
  LearningPlan: { userId: string };
  Notes: { userId: string; courseId?: string; lessonId?: string };
  Achievements: { userId: string };
  UserProfileSetup: undefined;
  UserSkillAssessment: undefined;
  UserProfileManagement: undefined;
  PersonalizedAssessment: undefined;
  UserProfileAnalysis: undefined;
  LearningDashboard: { userId: string };
  LearningPathDetail: {
    pathId?: string;
    jobId?: number;
    userId?: number;
    pathType?: 'personalized' | 'standard' | 'recommended';
  };
  StandardPath: undefined;
  StandardPathDetail: {
    jobId: number;
    pathId?: number;
  };
  Recommendation: undefined;
  // 代码编辑器页面
  CodeEditor: {
    initialCode?: string;
    language?: string;
    title?: string;
    lessonId?: string;
    courseId?: string;
  };

  // 原子技能相关页面
  AtomicSkillList: undefined;
  AtomicSkillDetail: { skillId: number };

  // 前置技能分析页面
  PrerequisiteAnalysis: { skillId: number; skillName?: string };

  // 技能图谱可视化页面
  SkillGraph: undefined;

  // 动态学习路径生成页面
  DynamicLearningPath: undefined;

  // 面试题练习页面
  InterviewPractice: undefined;
  QuestionList: {
    type?: string;
    company?: string;
    difficulty?: string;
  };
  InterviewQuestionDetail: {
    questionId: string;
    isDaily?: boolean;
  };
  InterviewStats: undefined;

  // 学习打卡页面
  StudyCheckIn: undefined;
  StudyTimer: undefined;
  StudyCalendar: undefined;
  StudyStats: undefined;
  GoalManagement: undefined;
  HabitManagement: undefined;
  StudyReport: undefined;

  // 技能评估页面
  SkillAssessment: undefined;
  TestConfiguration: { testId: string };
  TestExecution: { config: any };
  AssessmentResult: { result?: any; assessmentId?: string };
  AssessmentHistory: undefined;
  AssessmentStats: undefined;

  // 项目作品展示页面
  ProjectShowcase: undefined;
  ProjectDetail: { projectId: string };
  CreateProject: undefined;
  EditProject: { projectId: string };
  MyPortfolio: undefined;

  // 成就系统页面
  AchievementCenter: undefined;

  // 用户资料编辑页面
  EditProfile: { userId?: number };

  // 设置页面
  ThemeSettings: undefined;

  // 离线管理页面
  OfflineManager: undefined;

  // 测试页面
  LearningPathTest: undefined;

  // 职业目标相关页面
  JobSelection: { onJobSelected?: (job: any, level: any) => Promise<void> };
  CareerManagement: undefined;
  JobProfile: { job: any; onSelectJob?: (job: any, level: any) => void };

  // 第三阶段新增页面 - 职业目标专属功能
  ProjectWorkshop: undefined;                    // 项目实战平台
  ProjectWorkshopDetail: { projectId: string }; // 项目实战详情
  InterviewPreparation: { jobId?: string };     // 面试准备
  InterviewQuestionPractice: { questionId: string }; // 面试题练习
  PortfolioGeneration: undefined;               // 作品集生成
  PortfolioPreview: { portfolioId: string };    // 作品集预览
  JobRecommendations: undefined;                // 岗位推荐
  JobMatchAnalysis: { jobId: string };          // 岗位匹配分析
  CollaborativeCoding: { sessionId: string };   // 协作编程
  MarketAnalysis: { jobId: number; jobTitle?: string }; // 市场分析

  // 动态调整相关页面
  AnalysisReport: { userId: number; pathId: number; pathName?: string }; // 综合分析报告
};

// Main Tab Navigator - ITBook核心模块
export type MainTabParamList = {
  Home: {                 // 首页 - 学习概览和推荐
    jobSelected?: string;
    selectedJobId?: string;
    selectedJobName?: string;
    selectedLevel?: string;
  } | undefined;
  Study: undefined;       // 学习 - 学习路径和课程
  Discover: undefined;    // 发现 - 问答和技术文章
  Jobs: {                 // 求职 - 职位推荐和简历
    initialTab?: 'home' | 'resume' | 'applied' | 'analysis' | 'interview';
  } | undefined;
  Profile: undefined;     // 我的 - 个人中心
};

// Auth Stack Navigator
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

// Screen props helper types
export type RootStackScreenProps<T extends keyof RootStackParamList> = {
  navigation: any;
  route: { params: RootStackParamList[T] };
};

export type MainTabScreenProps<T extends keyof MainTabParamList> = {
  navigation: any;
  route: { params: MainTabParamList[T] };
};

export type AuthStackScreenProps<T extends keyof AuthStackParamList> = {
  navigation: any;
  route: { params: AuthStackParamList[T] };
};
