/**
 * 动态调整相关类型定义
 * 
 * 定义动态学习路径调整功能的所有TypeScript类型
 * 包括API请求/响应、UI组件props、状态管理等
 * 
 * <AUTHOR> Team
 * @since 2025-07-23
 */

// ==================== 基础枚举类型 ====================

export enum LearningEventType {
  START_LEARNING = 'START_LEARNING',
  PAUSE_LEARNING = 'PAUSE_LEARNING',
  RESUME_LEARNING = 'RESUME_LEARNING',
  COMPLETE_STEP = 'COMPLETE_STEP',
  SKIP_STEP = 'SKIP_STEP',
  RATE_CONTENT = 'RATE_CONTENT',
  ADD_NOTE = 'ADD_NOTE',
  SEEK_HELP = 'SEEK_HELP',
  REPEAT_CONTENT = 'REPEAT_CONTENT'
}

export enum FeedbackType {
  DIFFICULTY_RATING = 'DIFFICULTY_RATING',
  QUALITY_RATING = 'QUALITY_RATING',
  CONTENT_FEEDBACK = 'CONTENT_FEEDBACK',
  LEARNING_EXPERIENCE = 'LEARNING_EXPERIENCE',
  SUGGESTION = 'SUGGESTION',
  BUG_REPORT = 'BUG_REPORT',
  GENERAL_COMMENT = 'GENERAL_COMMENT'
}

export enum FeedbackSentiment {
  POSITIVE = 'POSITIVE',
  NEUTRAL = 'NEUTRAL',
  NEGATIVE = 'NEGATIVE'
}

export enum AdjustmentTrigger {
  POOR_PERFORMANCE = 'POOR_PERFORMANCE',
  HIGH_DIFFICULTY_RATING = 'HIGH_DIFFICULTY_RATING',
  LOW_QUALITY_RATING = 'LOW_QUALITY_RATING',
  SLOW_PROGRESS = 'SLOW_PROGRESS',
  USER_FEEDBACK = 'USER_FEEDBACK',
  SCHEDULED_OPTIMIZATION = 'SCHEDULED_OPTIMIZATION'
}

export enum AdjustmentStrategy {
  DIFFICULTY_ADJUSTMENT = 'DIFFICULTY_ADJUSTMENT',
  CONTENT_REPLACEMENT = 'CONTENT_REPLACEMENT',
  STEP_REORDERING = 'STEP_REORDERING',
  PATH_EXTENSION = 'PATH_EXTENSION',
  PATH_SIMPLIFICATION = 'PATH_SIMPLIFICATION',
  COMPLETE_REGENERATION = 'COMPLETE_REGENERATION'
}

export enum EffectivenessLevel {
  EXCELLENT = 'EXCELLENT',
  GOOD = 'GOOD',
  AVERAGE = 'AVERAGE',
  POOR = 'POOR',
  VERY_POOR = 'VERY_POOR'
}

export enum RegenerationRecommendation {
  NOT_NEEDED = 'NOT_NEEDED',
  MINOR_ADJUSTMENT = 'MINOR_ADJUSTMENT',
  MAJOR_ADJUSTMENT = 'MAJOR_ADJUSTMENT',
  COMPLETE_REGENERATION = 'COMPLETE_REGENERATION'
}

// ==================== API请求类型 ====================

export interface LearningEventRequest {
  userId: number;
  pathId: number;
  stepId?: number;
  eventType: LearningEventType;
  eventData?: Record<string, any>;
}

export interface FeedbackRequest {
  userId: number;
  pathId: number;
  stepId?: number;
  feedbackType: FeedbackType;
  rating?: number;
  comment?: string;
  sentiment?: FeedbackSentiment;
  metadata?: Record<string, any>;
}

export interface PathAdjustmentRequest {
  userId: number;
  pathId: number;
  trigger: AdjustmentTrigger;
  preferredStrategy?: AdjustmentStrategy;
  adjustmentData?: Record<string, any>;
  reason?: string;
}

export interface SmartAdjustmentRequest {
  autoExecute?: boolean;
  preferredStrategy?: string;
  options?: Record<string, any>;
}

// ==================== API响应类型 ====================

export interface LearningBehaviorAnalysis {
  userId: number;
  pathId: number;
  analysisTime: string;
  
  // 学习效率指标
  averageLearningSpeed: number;
  learningConsistency: number;
  focusLevel: number;
  
  // 学习模式分析
  learningPattern: string;
  preferredTimeSlots: string[];
  averageSessionDuration: number;
  
  // 困难识别
  strugglingAreas: string[];
  problematicSteps: number[];
  overallDifficulty: number;
  
  // 预测指标
  completionProbability: number;
  estimatedRemainingDays: number;
  riskLevel: string;
  
  // 建议和警告
  recommendations: string[];
  warnings: string[];
}

export interface PathAdjustmentResult {
  pathId: number;
  appliedStrategy: AdjustmentStrategy;
  adjustmentActions: string[];
  adjustmentDetails: Record<string, any>;
  qualityImprovement: number;
  adjustmentSummary: string;
  adjustmentTime: string;
  requiresUserConfirmation: boolean;
}

export interface FeedbackAnalysis {
  pathId: number;
  analysisTime: string;
  
  // 整体统计
  totalFeedbackCount: number;
  averageRating: number;
  feedbackTypeDistribution: Record<FeedbackType, number>;
  sentimentDistribution: Record<FeedbackSentiment, number>;
  
  // 问题识别
  identifiedIssues: string[];
  problematicSteps: number[];
  overallSatisfaction: number;
  
  // 改进建议
  improvementSuggestions: string[];
  userSuggestions: string[];
  priorityAction: string;
  
  // 趋势分析
  feedbackTrend: string;
  satisfactionTrend: number;
}

export interface EffectivenessEvaluation {
  userId: number;
  pathId: number;
  evaluationTime: string;
  
  // 整体评估
  overallEffectiveness: number;
  effectivenessLevel: EffectivenessLevel;
  evaluationSummary: string;
  
  // 各维度评估
  dimensionScores: Record<string, number>;
  dimensionAnalysis?: Record<string, string>;
  
  // 学习成果分析
  learningOutcomeScore: number;
  skillsAcquired: number;
  totalSkills: number;
  skillMasteryRate: number;
  
  // 路径质量分析
  pathQualityScore: number;
  contentRelevance: number;
  difficultyAppropriate: number;
  structureLogic: number;
  
  // 学习效率分析
  learningEfficiencyScore: number;
  timeUtilization: number;
  progressRate: number;
  averageTimePerSkill: number;
  
  // 用户满意度分析
  userSatisfactionScore: number;
  feedbackRating: number;
  positiveComments: number;
  negativeComments: number;
  
  // 改进建议
  strengths: string[];
  weaknesses: string[];
  improvements: string[];
  regenerationRecommendation: RegenerationRecommendation;
  regenerationReason: string;
}

export interface ComprehensiveAnalysis {
  analysisTime: string;
  userId: number;
  pathId: number;
  
  behaviorAnalysis: LearningBehaviorAnalysis;
  feedbackAnalysis: FeedbackAnalysis;
  effectivenessEvaluation: EffectivenessEvaluation;
  adjustmentEvaluation: {
    adjustmentScore: number;
    adjustmentLevel: string;
    recommendedStrategies: AdjustmentStrategy[];
    identifiedIssues: string[];
    expectedImpact: {
      expectedImprovement: number;
      expectedBenefits: string[];
      estimatedTimeToComplete: number;
    };
  };
  recommendations: {
    priority: string;
    primaryAction: string;
    effectivenessAction: string;
  };
}

export interface SmartAdjustmentResult {
  analysisTime: string;
  userId: number;
  pathId: number;
  
  intelligentSuggestions: {
    learningStyleOptimization: string;
    difficultyAdjustment: string;
    timeManagement: string;
  };
  
  autoExecuted: boolean;
  adjustmentResults?: PathAdjustmentResult[];
  
  expectedOutcomes: {
    predictedEffectiveness: number;
    estimatedCompletionTime: number;
    confidenceLevel: number;
  };
}

export interface SystemStatus {
  systemName: string;
  version: string;
  status: string;
  uptime: string;
  modules: Record<string, string>;
  statistics: {
    totalUsers: number;
    activePaths: number;
    adjustmentsToday: number;
    feedbacksToday: number;
    averageEffectiveness: number;
  };
}

// ==================== UI组件Props类型 ====================

export interface DynamicAdjustmentCardProps {
  userId: number;
  pathId: number;
  onAdjustmentComplete?: (result: PathAdjustmentResult) => void;
}

export interface FeedbackFormProps {
  userId: number;
  pathId: number;
  stepId?: number;
  onFeedbackSubmit?: (success: boolean) => void;
}

export interface AnalysisReportProps {
  analysis: ComprehensiveAnalysis;
  onActionTaken?: (action: string) => void;
}

export interface EffectivenessChartProps {
  evaluation: EffectivenessEvaluation;
  showDetails?: boolean;
}

// ==================== 状态管理类型 ====================

export interface DynamicAdjustmentState {
  currentAnalysis?: ComprehensiveAnalysis;
  adjustmentHistory: PathAdjustmentResult[];
  feedbackHistory: FeedbackRequest[];
  isLoading: boolean;
  error?: string;
}

export interface DynamicAdjustmentActions {
  loadAnalysis: (userId: number, pathId: number) => Promise<void>;
  submitFeedback: (feedback: FeedbackRequest) => Promise<boolean>;
  executeAdjustment: (request: PathAdjustmentRequest) => Promise<PathAdjustmentResult>;
  recordLearningEvent: (event: LearningEventRequest) => Promise<void>;
  clearError: () => void;
}
