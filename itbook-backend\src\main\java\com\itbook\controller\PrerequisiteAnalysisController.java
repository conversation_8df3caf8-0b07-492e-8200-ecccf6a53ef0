package com.itbook.controller;

import com.itbook.common.ApiResponse;
import com.itbook.service.PrerequisiteAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 前置技能分析控制器
 * 提供技能依赖分析、学习路径生成等API接口
 * 
 * <AUTHOR> Team
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/prerequisite-analysis")
@Tag(name = "前置技能分析", description = "技能依赖分析和学习路径生成相关API")
public class PrerequisiteAnalysisController {

    @Autowired
    private PrerequisiteAnalysisService prerequisiteAnalysisService;

    /**
     * 分析指定技能的前置技能
     */
    @GetMapping("/skills/{skillId}")
    @Operation(summary = "分析技能前置依赖", description = "分析指定技能的所有前置技能和依赖关系")
    public ResponseEntity<ApiResponse<PrerequisiteAnalysisService.PrerequisiteAnalysisResult>> analyzePrerequisites(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.info("分析技能前置依赖: skillId={}", skillId);

        try {
            PrerequisiteAnalysisService.PrerequisiteAnalysisResult result = 
                    prerequisiteAnalysisService.analyzePrerequisites(skillId);
            
            log.info("前置技能分析成功: skillId={}, 总前置技能数={}", skillId, result.getTotalPrerequisites());
            return ResponseEntity.ok(ApiResponse.success(result));
            
        } catch (Exception e) {
            log.error("分析技能前置依赖失败: skillId={}", skillId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("分析前置技能失败: " + e.getMessage()));
        }
    }

    /**
     * 获取技能的直接前置技能
     */
    @GetMapping("/skills/{skillId}/direct")
    @Operation(summary = "获取直接前置技能", description = "获取指定技能的直接前置技能列表")
    public ResponseEntity<ApiResponse<List<PrerequisiteAnalysisService.SkillDependencyNode>>> getDirectPrerequisites(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.info("获取直接前置技能: skillId={}", skillId);

        try {
            PrerequisiteAnalysisService.PrerequisiteAnalysisResult result = 
                    prerequisiteAnalysisService.analyzePrerequisites(skillId);
            
            List<PrerequisiteAnalysisService.SkillDependencyNode> directPrerequisites = 
                    result.getDirectPrerequisites();
            
            log.info("获取直接前置技能成功: skillId={}, 数量={}", skillId, directPrerequisites.size());
            return ResponseEntity.ok(ApiResponse.success(directPrerequisites));
            
        } catch (Exception e) {
            log.error("获取直接前置技能失败: skillId={}", skillId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取直接前置技能失败: " + e.getMessage()));
        }
    }

    /**
     * 生成学习路径
     */
    @GetMapping("/skills/{skillId}/learning-path")
    @Operation(summary = "生成学习路径", description = "为指定技能生成推荐的学习路径")
    public ResponseEntity<ApiResponse<List<PrerequisiteAnalysisService.LearningPathStep>>> generateLearningPath(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.info("生成学习路径: skillId={}", skillId);

        try {
            PrerequisiteAnalysisService.PrerequisiteAnalysisResult result = 
                    prerequisiteAnalysisService.analyzePrerequisites(skillId);
            
            List<PrerequisiteAnalysisService.LearningPathStep> learningPath = 
                    result.getRecommendedPath();
            
            log.info("生成学习路径成功: skillId={}, 步骤数={}", skillId, learningPath.size());
            return ResponseEntity.ok(ApiResponse.success(learningPath));
            
        } catch (Exception e) {
            log.error("生成学习路径失败: skillId={}", skillId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成学习路径失败: " + e.getMessage()));
        }
    }

    /**
     * 获取技能依赖统计信息
     */
    @GetMapping("/skills/{skillId}/statistics")
    @Operation(summary = "获取技能依赖统计", description = "获取指定技能的依赖关系统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPrerequisiteStatistics(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.info("获取技能依赖统计: skillId={}", skillId);

        try {
            PrerequisiteAnalysisService.PrerequisiteAnalysisResult result = 
                    prerequisiteAnalysisService.analyzePrerequisites(skillId);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("skillId", result.getTargetSkillId());
            statistics.put("skillName", result.getTargetSkillName());
            statistics.put("totalPrerequisites", result.getTotalPrerequisites());
            statistics.put("directPrerequisites", result.getDirectPrerequisites().size());
            statistics.put("maxDepth", result.getMaxDepth());
            statistics.put("complexity", result.getComplexity());
            statistics.put("hasCycles", result.isHasCycles());
            statistics.put("learningSteps", result.getRecommendedPath().size());
            
            // 计算总学习时间
            int totalHours = result.getRecommendedPath().stream()
                    .mapToInt(PrerequisiteAnalysisService.LearningPathStep::getEstimatedHours)
                    .sum();
            statistics.put("totalEstimatedHours", totalHours);
            
            // 按难度级别统计
            Map<String, Long> difficultyStats = result.getAllPrerequisites().stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            PrerequisiteAnalysisService.SkillDependencyNode::getDifficultyLevel,
                            java.util.stream.Collectors.counting()
                    ));
            statistics.put("difficultyDistribution", difficultyStats);
            
            // 按类别统计
            Map<String, Long> categoryStats = result.getAllPrerequisites().stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            PrerequisiteAnalysisService.SkillDependencyNode::getCategory,
                            java.util.stream.Collectors.counting()
                    ));
            statistics.put("categoryDistribution", categoryStats);
            
            log.info("获取技能依赖统计成功: skillId={}, 总前置技能={}, 复杂度={}", 
                    skillId, result.getTotalPrerequisites(), result.getComplexity());
            return ResponseEntity.ok(ApiResponse.success(statistics));
            
        } catch (Exception e) {
            log.error("获取技能依赖统计失败: skillId={}", skillId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取技能依赖统计失败: " + e.getMessage()));
        }
    }

    /**
     * 批量分析多个技能的前置依赖
     */
    @PostMapping("/skills/batch-analyze")
    @Operation(summary = "批量分析前置依赖", description = "批量分析多个技能的前置依赖关系")
    public ResponseEntity<ApiResponse<Map<Long, PrerequisiteAnalysisService.PrerequisiteAnalysisResult>>> batchAnalyzePrerequisites(
            @Parameter(description = "技能ID列表") @RequestBody List<Long> skillIds) {
        
        log.info("批量分析前置依赖: skillIds={}", skillIds);

        try {
            Map<Long, PrerequisiteAnalysisService.PrerequisiteAnalysisResult> results = new HashMap<>();
            
            for (Long skillId : skillIds) {
                try {
                    PrerequisiteAnalysisService.PrerequisiteAnalysisResult result = 
                            prerequisiteAnalysisService.analyzePrerequisites(skillId);
                    results.put(skillId, result);
                } catch (Exception e) {
                    log.warn("分析技能前置依赖失败: skillId={}, error={}", skillId, e.getMessage());
                    // 继续处理其他技能，不中断整个批量操作
                }
            }
            
            log.info("批量分析前置依赖完成: 请求数={}, 成功数={}", skillIds.size(), results.size());
            return ResponseEntity.ok(ApiResponse.success(results));
            
        } catch (Exception e) {
            log.error("批量分析前置依赖失败: skillIds={}", skillIds, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("批量分析前置依赖失败: " + e.getMessage()));
        }
    }

    /**
     * 检测技能依赖中的循环引用
     */
    @GetMapping("/skills/{skillId}/cycle-detection")
    @Operation(summary = "检测循环依赖", description = "检测指定技能的依赖关系中是否存在循环引用")
    public ResponseEntity<ApiResponse<Map<String, Object>>> detectCycles(
            @Parameter(description = "技能ID") @PathVariable Long skillId) {
        
        log.info("检测循环依赖: skillId={}", skillId);

        try {
            PrerequisiteAnalysisService.PrerequisiteAnalysisResult result = 
                    prerequisiteAnalysisService.analyzePrerequisites(skillId);
            
            Map<String, Object> cycleInfo = new HashMap<>();
            cycleInfo.put("skillId", skillId);
            cycleInfo.put("skillName", result.getTargetSkillName());
            cycleInfo.put("hasCycles", result.isHasCycles());
            cycleInfo.put("totalPrerequisites", result.getTotalPrerequisites());
            cycleInfo.put("maxDepth", result.getMaxDepth());
            
            if (result.isHasCycles()) {
                cycleInfo.put("warning", "检测到循环依赖，可能影响学习路径生成");
                cycleInfo.put("recommendation", "建议检查技能关系配置，移除循环依赖");
            } else {
                cycleInfo.put("status", "依赖关系正常，无循环引用");
            }
            
            log.info("循环依赖检测完成: skillId={}, hasCycles={}", skillId, result.isHasCycles());
            return ResponseEntity.ok(ApiResponse.success(cycleInfo));
            
        } catch (Exception e) {
            log.error("检测循环依赖失败: skillId={}", skillId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("检测循环依赖失败: " + e.getMessage()));
        }
    }

    /**
     * 获取技能依赖树结构
     */
    @GetMapping("/skills/{skillId}/dependency-tree")
    @Operation(summary = "获取依赖树结构", description = "获取指定技能的完整依赖树结构")
    public ResponseEntity<ApiResponse<PrerequisiteAnalysisService.SkillDependencyNode>> getDependencyTree(
            @Parameter(description = "技能ID") @PathVariable Long skillId,
            @Parameter(description = "最大深度限制") @RequestParam(defaultValue = "5") int maxDepth) {
        
        log.info("获取依赖树结构: skillId={}, maxDepth={}", skillId, maxDepth);

        try {
            PrerequisiteAnalysisService.PrerequisiteAnalysisResult result = 
                    prerequisiteAnalysisService.analyzePrerequisites(skillId);
            
            // 构建根节点
            PrerequisiteAnalysisService.SkillDependencyNode rootNode = 
                    new PrerequisiteAnalysisService.SkillDependencyNode();
            rootNode.setSkillId(result.getTargetSkillId());
            rootNode.setSkillName(result.getTargetSkillName());
            rootNode.setDepth(0);
            
            // 添加直接前置技能作为子节点
            List<PrerequisiteAnalysisService.SkillDependencyNode> directPrerequisites = 
                    result.getDirectPrerequisites();
            
            // 限制深度
            List<PrerequisiteAnalysisService.SkillDependencyNode> filteredChildren = 
                    directPrerequisites.stream()
                            .filter(node -> node.getDepth() <= maxDepth)
                            .collect(java.util.stream.Collectors.toList());
            
            rootNode.setChildren(filteredChildren);
            
            log.info("获取依赖树结构成功: skillId={}, 子节点数={}", skillId, filteredChildren.size());
            return ResponseEntity.ok(ApiResponse.success(rootNode));
            
        } catch (Exception e) {
            log.error("获取依赖树结构失败: skillId={}", skillId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取依赖树结构失败: " + e.getMessage()));
        }
    }
}
