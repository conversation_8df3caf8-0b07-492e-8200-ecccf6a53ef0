package com.itbook.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 动态路径步骤实体
 * 存储动态学习路径的具体学习步骤
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Entity
@Table(name = "dynamic_path_step")
public class DynamicPathStep {

    /**
     * 步骤类型枚举
     */
    public enum StepType {
        LEARN("学习"),
        PRACTICE("练习"),
        ASSESS("评估"),
        PROJECT("项目"),
        REVIEW("复习");
        
        private final String description;
        
        StepType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 步骤状态枚举
     */
    public enum Status {
        NOT_STARTED("未开始"),
        IN_PROGRESS("进行中"),
        COMPLETED("已完成"),
        SKIPPED("已跳过");
        
        private final String description;
        
        Status(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 动态路径ID
     */
    @NotNull(message = "动态路径ID不能为空")
    @Column(name = "path_id", nullable = false)
    private Long pathId;

    /**
     * 动态学习路径（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "path_id", insertable = false, updatable = false)
    @JsonIgnore
    private DynamicLearningPath dynamicPath;

    /**
     * 原子技能ID
     */
    @NotNull(message = "原子技能ID不能为空")
    @Column(name = "atomic_skill_id", nullable = false)
    private Long atomicSkillId;

    /**
     * 原子技能（关联查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "atomic_skill_id", insertable = false, updatable = false)
    @JsonIgnore
    private AtomicSkill atomicSkill;

    /**
     * 步骤顺序
     */
    @NotNull(message = "步骤顺序不能为空")
    @Min(value = 1, message = "步骤顺序不能小于1")
    @Column(name = "step_order", nullable = false)
    private Integer stepOrder;

    /**
     * 步骤类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "step_type", nullable = false)
    private StepType stepType = StepType.LEARN;

    /**
     * 预计时长（小时）
     */
    @Min(value = 0, message = "预计时长不能为负数")
    @Column(name = "estimated_hours")
    private Integer estimatedHours = 0;

    /**
     * 难度调整系数
     */
    @DecimalMin(value = "0.1", message = "难度调整系数不能小于0.1")
    @DecimalMax(value = "3.0", message = "难度调整系数不能大于3.0")
    @Column(name = "difficulty_adjustment", precision = 3, scale = 2)
    private BigDecimal difficultyAdjustment = BigDecimal.ONE;

    /**
     * 优先级权重
     */
    @DecimalMin(value = "0.0", message = "优先级权重不能小于0")
    @DecimalMax(value = "10.0", message = "优先级权重不能大于10")
    @Column(name = "priority_weight", precision = 3, scale = 2)
    private BigDecimal priorityWeight = BigDecimal.ONE;

    /**
     * 个性化原因
     */
    @Column(name = "personalization_reason", columnDefinition = "TEXT")
    private String personalizationReason;

    /**
     * 推荐学习资源（JSON格式）
     */
    @Column(name = "recommended_resources", columnDefinition = "JSON")
    private String recommendedResourcesJson;

    /**
     * 备选资源（JSON格式）
     */
    @Column(name = "alternative_resources", columnDefinition = "JSON")
    private String alternativeResourcesJson;

    /**
     * 步骤状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.NOT_STARTED;

    /**
     * 进度百分比
     */
    @DecimalMin(value = "0.00", message = "进度百分比不能小于0")
    @DecimalMax(value = "100.00", message = "进度百分比不能大于100")
    @Column(name = "progress_percentage", precision = 5, scale = 2)
    private BigDecimal progressPercentage = BigDecimal.ZERO;

    /**
     * 完成分数
     */
    @DecimalMin(value = "0.00", message = "完成分数不能小于0")
    @DecimalMax(value = "100.00", message = "完成分数不能大于100")
    @Column(name = "completion_score", precision = 3, scale = 2)
    private BigDecimal completionScore;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "started_at")
    private LocalDateTime startedAt;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    /**
     * 实际用时（小时）
     */
    @DecimalMin(value = "0.00", message = "实际用时不能为负数")
    @Column(name = "actual_hours", precision = 5, scale = 2)
    private BigDecimal actualHours;

    /**
     * 用户反馈（JSON格式）
     */
    @Column(name = "user_feedback", columnDefinition = "JSON")
    private String userFeedbackJson;

    /**
     * 系统反馈（JSON格式）
     */
    @Column(name = "system_feedback", columnDefinition = "JSON")
    private String systemFeedbackJson;

    /**
     * 调整历史（JSON格式）
     */
    @Column(name = "adjustment_history", columnDefinition = "JSON")
    private String adjustmentHistoryJson;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 构造函数
    public DynamicPathStep() {}

    public DynamicPathStep(Long pathId, Long atomicSkillId, Integer stepOrder) {
        this.pathId = pathId;
        this.atomicSkillId = atomicSkillId;
        this.stepOrder = stepOrder;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getPathId() { return pathId; }
    public void setPathId(Long pathId) { this.pathId = pathId; }

    public DynamicLearningPath getDynamicPath() { return dynamicPath; }
    public void setDynamicPath(DynamicLearningPath dynamicPath) { this.dynamicPath = dynamicPath; }

    public Long getAtomicSkillId() { return atomicSkillId; }
    public void setAtomicSkillId(Long atomicSkillId) { this.atomicSkillId = atomicSkillId; }

    public AtomicSkill getAtomicSkill() { return atomicSkill; }
    public void setAtomicSkill(AtomicSkill atomicSkill) { this.atomicSkill = atomicSkill; }

    public Integer getStepOrder() { return stepOrder; }
    public void setStepOrder(Integer stepOrder) { this.stepOrder = stepOrder; }

    public StepType getStepType() { return stepType; }
    public void setStepType(StepType stepType) { this.stepType = stepType; }

    public Integer getEstimatedHours() { return estimatedHours; }
    public void setEstimatedHours(Integer estimatedHours) { this.estimatedHours = estimatedHours; }

    public BigDecimal getDifficultyAdjustment() { return difficultyAdjustment; }
    public void setDifficultyAdjustment(BigDecimal difficultyAdjustment) { this.difficultyAdjustment = difficultyAdjustment; }

    public BigDecimal getPriorityWeight() { return priorityWeight; }
    public void setPriorityWeight(BigDecimal priorityWeight) { this.priorityWeight = priorityWeight; }

    public String getPersonalizationReason() { return personalizationReason; }
    public void setPersonalizationReason(String personalizationReason) { this.personalizationReason = personalizationReason; }

    public String getRecommendedResourcesJson() { return recommendedResourcesJson; }
    public void setRecommendedResourcesJson(String recommendedResourcesJson) { this.recommendedResourcesJson = recommendedResourcesJson; }

    public String getAlternativeResourcesJson() { return alternativeResourcesJson; }
    public void setAlternativeResourcesJson(String alternativeResourcesJson) { this.alternativeResourcesJson = alternativeResourcesJson; }

    public Status getStatus() { return status; }
    public void setStatus(Status status) { this.status = status; }

    public BigDecimal getProgressPercentage() { return progressPercentage; }
    public void setProgressPercentage(BigDecimal progressPercentage) { this.progressPercentage = progressPercentage; }

    public BigDecimal getCompletionScore() { return completionScore; }
    public void setCompletionScore(BigDecimal completionScore) { this.completionScore = completionScore; }

    public LocalDateTime getStartedAt() { return startedAt; }
    public void setStartedAt(LocalDateTime startedAt) { this.startedAt = startedAt; }

    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }

    public BigDecimal getActualHours() { return actualHours; }
    public void setActualHours(BigDecimal actualHours) { this.actualHours = actualHours; }

    public String getUserFeedbackJson() { return userFeedbackJson; }
    public void setUserFeedbackJson(String userFeedbackJson) { this.userFeedbackJson = userFeedbackJson; }

    public String getSystemFeedbackJson() { return systemFeedbackJson; }
    public void setSystemFeedbackJson(String systemFeedbackJson) { this.systemFeedbackJson = systemFeedbackJson; }

    public String getAdjustmentHistoryJson() { return adjustmentHistoryJson; }
    public void setAdjustmentHistoryJson(String adjustmentHistoryJson) { this.adjustmentHistoryJson = adjustmentHistoryJson; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    @Override
    public String toString() {
        return "DynamicPathStep{" +
                "id=" + id +
                ", pathId=" + pathId +
                ", atomicSkillId=" + atomicSkillId +
                ", stepOrder=" + stepOrder +
                ", stepType=" + stepType +
                ", status=" + status +
                ", progressPercentage=" + progressPercentage +
                '}';
    }
}
