import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Resume } from '../../types/jobs';
import { ResumeStats } from '../../services/ResumeService';

// 简历状态接口
export interface ResumeState {
  resumes: Resume[];
  stats: ResumeStats | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number;
}

// 初始状态
const initialState: ResumeState = {
  resumes: [],
  stats: null,
  isLoading: false,
  error: null,
  lastUpdated: 0,
};

// 简历Redux slice
const resumeSlice = createSlice({
  name: 'resume',
  initialState,
  reducers: {
    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    // 设置错误信息
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },

    // 设置简历列表
    setResumes: (state, action: PayloadAction<Resume[]>) => {
      state.resumes = action.payload;
      state.lastUpdated = Date.now();
      state.isLoading = false;
      state.error = null;
      console.log('📄 Redux: 更新简历列表', { count: action.payload.length });
    },

    // 设置简历统计数据
    setStats: (state, action: PayloadAction<ResumeStats>) => {
      state.stats = action.payload;
      state.lastUpdated = Date.now();
      console.log('📊 Redux: 更新简历统计', action.payload);
    },

    // 添加新简历
    addResume: (state, action: PayloadAction<Resume>) => {
      state.resumes.unshift(action.payload); // 添加到列表开头
      
      // 更新统计数据
      if (state.stats) {
        state.stats.totalResumes += 1;
        if (action.payload.status === 'PUBLISHED') {
          state.stats.publishedResumes += 1;
        }
      }
      
      state.lastUpdated = Date.now();
      console.log('➕ Redux: 添加新简历', { 
        id: action.payload.id, 
        title: action.payload.title,
        newTotal: state.resumes.length 
      });
    },

    // 删除简历
    removeResume: (state, action: PayloadAction<number>) => {
      const resumeId = action.payload;
      const resumeIndex = state.resumes.findIndex(r => r.id === resumeId);
      
      if (resumeIndex !== -1) {
        const removedResume = state.resumes[resumeIndex];
        state.resumes.splice(resumeIndex, 1);
        
        // 更新统计数据
        if (state.stats) {
          state.stats.totalResumes -= 1;
          if (removedResume.status === 'PUBLISHED') {
            state.stats.publishedResumes -= 1;
          }
          state.stats.totalViews -= removedResume.viewCount || 0;
          state.stats.totalDownloads -= removedResume.downloadCount || 0;
        }
        
        state.lastUpdated = Date.now();
        console.log('🗑️ Redux: 删除简历', { 
          id: resumeId, 
          title: removedResume.title,
          newTotal: state.resumes.length 
        });
      }
    },

    // 更新简历
    updateResume: (state, action: PayloadAction<Resume>) => {
      const updatedResume = action.payload;
      const index = state.resumes.findIndex(r => r.id === updatedResume.id);
      
      if (index !== -1) {
        const oldResume = state.resumes[index];
        state.resumes[index] = updatedResume;
        
        // 更新统计数据（如果状态发生变化）
        if (state.stats && oldResume.status !== updatedResume.status) {
          if (oldResume.status === 'PUBLISHED' && updatedResume.status !== 'PUBLISHED') {
            state.stats.publishedResumes -= 1;
          } else if (oldResume.status !== 'PUBLISHED' && updatedResume.status === 'PUBLISHED') {
            state.stats.publishedResumes += 1;
          }
        }
        
        state.lastUpdated = Date.now();
        console.log('📝 Redux: 更新简历', { 
          id: updatedResume.id, 
          title: updatedResume.title 
        });
      }
    },

    // 清空所有数据
    clearAll: (state) => {
      state.resumes = [];
      state.stats = null;
      state.error = null;
      state.lastUpdated = 0;
      console.log('🧹 Redux: 清空简历数据');
    },

    // 刷新数据（重置加载状态）
    refreshData: (state) => {
      state.isLoading = true;
      state.error = null;
      console.log('🔄 Redux: 开始刷新简历数据');
    },
  },
});

// 导出actions
export const {
  setLoading,
  setError,
  setResumes,
  setStats,
  addResume,
  removeResume,
  updateResume,
  clearAll,
  refreshData,
} = resumeSlice.actions;

// 导出reducer
export default resumeSlice.reducer;

// 选择器
export const selectResumes = (state: { resume: ResumeState }) => state.resume.resumes;
export const selectResumeStats = (state: { resume: ResumeState }) => state.resume.stats;
export const selectResumeLoading = (state: { resume: ResumeState }) => state.resume.isLoading;
export const selectResumeError = (state: { resume: ResumeState }) => state.resume.error;
export const selectResumeLastUpdated = (state: { resume: ResumeState }) => state.resume.lastUpdated;
