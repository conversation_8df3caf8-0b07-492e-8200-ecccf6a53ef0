/**
 * 个人简介服务
 * 
 * <AUTHOR> Team
 * @since 2025-07-17
 */

import ApiService from './ApiService';

export interface SummaryUpdateData {
  summary: string;
}

export interface SummaryResponse {
  id: number;
  userId: number;
  title: string;
  summary: string;
  isDefault: boolean;
  isPublic: boolean;
  downloadCount: number;
  viewCount: number;
  templateId?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

class SummaryService {
  /**
   * 更新个人简介
   */
  static async updateSummary(resumeId: number, data: SummaryUpdateData): Promise<SummaryResponse> {
    console.log('🔄 更新个人简介:', { resumeId, data });
    
    try {
      const response = await ApiService.put(`/resumes/${resumeId}/summary`, data);
      console.log('✅ 个人简介更新成功:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ 个人简介更新失败:', error);
      throw error;
    }
  }
}

export default SummaryService;
