import { apiService } from './ApiService';
import { AtomicSkill, AtomicSkillApiResponse, SkillSearchFilter } from '../types/atomicSkill';
import { AtomicSkillUtils } from '../utils/atomicSkillUtils';

/**
 * 原子技能服务类
 * 提供原子技能相关的API调用功能
 */
export class AtomicSkillService {
  private static readonly BASE_URL = '/atomic-skills';

  /**
   * 获取原子技能列表（分页）
   */
  static async getAtomicSkills(params: {
    page?: number;
    size?: number;
    category?: string;
    difficulty?: string;
    status?: string;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
  } = {}): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    // 构建查询参数，确保分页参数正确
    const queryParams: Record<string, any> = {};

    // 添加分页参数
    if (params.page !== undefined) {
      queryParams.page = params.page;
    }
    if (params.size !== undefined) {
      queryParams.size = params.size;
    }

    // 添加筛选参数
    if (params.category) {
      queryParams.category = params.category;
    }
    if (params.difficulty) {
      queryParams.difficulty = params.difficulty;
    }
    if (params.status) {
      queryParams.status = params.status;
    }
    if (params.sortBy) {
      queryParams.sort = params.sortDirection === 'desc'
        ? `${params.sortBy},desc`
        : `${params.sortBy},asc`;
    }

    const response = await apiService.get(this.BASE_URL, queryParams);
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 获取原子技能详情
   */
  static async getAtomicSkill(skillId: number): Promise<AtomicSkill> {
    const response = await apiService.get(`${this.BASE_URL}/${skillId}`);
    const apiResponse: AtomicSkillApiResponse = response.data;
    return AtomicSkillUtils.fromApiResponse(apiResponse);
  }

  /**
   * 创建原子技能
   */
  static async createAtomicSkill(skill: Partial<AtomicSkill>): Promise<AtomicSkill> {
    const apiRequest = AtomicSkillUtils.toApiRequest(skill);
    const response = await apiService.post(this.BASE_URL, apiRequest);
    const apiResponse: AtomicSkillApiResponse = response.data;
    return AtomicSkillUtils.fromApiResponse(apiResponse);
  }

  /**
   * 更新原子技能
   */
  static async updateAtomicSkill(skillId: number, skill: Partial<AtomicSkill>): Promise<AtomicSkill> {
    const apiRequest = AtomicSkillUtils.toApiRequest(skill);
    const response = await apiService.put(`${this.BASE_URL}/${skillId}`, apiRequest);
    const apiResponse: AtomicSkillApiResponse = response.data;
    return AtomicSkillUtils.fromApiResponse(apiResponse);
  }

  /**
   * 删除原子技能（软删除）
   */
  static async deleteAtomicSkill(skillId: number): Promise<string> {
    const response = await apiService.delete(`${this.BASE_URL}/${skillId}`);
    return response.data;
  }

  /**
   * 搜索原子技能
   */
  static async searchAtomicSkills(
    keyword: string,
    params: {
      page?: number;
      size?: number;
      category?: string;
      difficulty?: string;
      sortBy?: string;
      sortDirection?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    // 构建查询参数，确保分页参数正确
    const queryParams: Record<string, any> = { keyword };

    // 添加分页参数
    if (params.page !== undefined) {
      queryParams.page = params.page;
    }
    if (params.size !== undefined) {
      queryParams.size = params.size;
    }

    // 添加其他筛选参数
    if (params.category) {
      queryParams.category = params.category;
    }
    if (params.difficulty) {
      queryParams.difficulty = params.difficulty;
    }
    if (params.sortBy) {
      queryParams.sort = params.sortDirection === 'desc'
        ? `${params.sortBy},desc`
        : `${params.sortBy},asc`;
    }

    // 确保参数正确传递给后端搜索接口
    console.log('🔍 搜索参数:', queryParams);
    const response = await apiService.get(`${this.BASE_URL}/search`, queryParams);
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 按分类获取原子技能
   */
  static async getSkillsByCategory(
    category: string,
    params: {
      page?: number;
      size?: number;
      difficulty?: string;
      sortBy?: string;
      sortDirection?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    const response = await apiService.get(`${this.BASE_URL}/category/${category}`, params);
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 按难度获取原子技能
   */
  static async getSkillsByDifficulty(
    difficultyLevel: string,
    params: {
      page?: number;
      size?: number;
      category?: string;
      sortBy?: string;
      sortDirection?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    const response = await apiService.get(`${this.BASE_URL}/difficulty/${difficultyLevel}`, params);
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 按类型获取原子技能
   */
  static async getSkillsByType(
    skillType: string,
    params: {
      page?: number;
      size?: number;
      category?: string;
      sortBy?: string;
      sortDirection?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    const response = await apiService.get(`${this.BASE_URL}/type/${skillType}`, params);
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 发布技能
   */
  static async publishAtomicSkill(skillId: number): Promise<AtomicSkill> {
    const response = await apiService.post(`${this.BASE_URL}/${skillId}/publish`);
    const apiResponse: AtomicSkillApiResponse = response.data;
    return AtomicSkillUtils.fromApiResponse(apiResponse);
  }

  /**
   * 高级搜索原子技能
   */
  static async advancedSearchSkills(filter: SkillSearchFilter): Promise<AtomicSkill[]> {
    const response = await apiService.post(`${this.BASE_URL}/advanced-search`, filter);
    const apiSkills: AtomicSkillApiResponse[] = response.data;
    return apiSkills.map(skill => AtomicSkillUtils.fromApiResponse(skill));
  }

  /**
   * 获取技能的前置技能
   */
  static async getPrerequisiteSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await apiService.get(`${this.BASE_URL}/${skillId}/prerequisites`);
    return response.data;
  }

  /**
   * 获取技能的后续技能
   */
  static async getSuccessorSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await apiService.get(`${this.BASE_URL}/${skillId}/successors`);
    return response.data;
  }

  /**
   * 获取相关技能
   */
  static async getRelatedSkills(skillId: number): Promise<AtomicSkill[]> {
    const response = await apiService.get(`${this.BASE_URL}/${skillId}/related`);
    return response.data;
  }

  /**
   * 获取技能分类列表
   */
  static async getSkillCategories(): Promise<string[]> {
    const response = await apiService.get(`${this.BASE_URL}/categories`);
    return response.data;
  }

  /**
   * 获取技能难度级别列表
   */
  static async getDifficultyLevels(): Promise<string[]> {
    const response = await apiService.get(`${this.BASE_URL}/difficulty-levels`);
    return response.data;
  }

  /**
   * 获取技能标签列表
   */
  static async getSkillTags(): Promise<string[]> {
    const response = await apiService.get(`${this.BASE_URL}/tags`);
    return response.data;
  }

  /**
   * 批量获取原子技能
   */
  static async getAtomicSkillsBatch(skillIds: number[]): Promise<AtomicSkill[]> {
    const response = await apiService.post(`${this.BASE_URL}/batch`, skillIds);
    const apiSkills: AtomicSkillApiResponse[] = response.data;
    return apiSkills.map(skill => AtomicSkillUtils.fromApiResponse(skill));
  }

  /**
   * 批量删除原子技能
   */
  static async deleteAtomicSkillsBatch(skillIds: number[]): Promise<number> {
    const response = await apiService.delete(`${this.BASE_URL}/batch`, { data: skillIds });
    return response.data;
  }

  /**
   * 批量发布技能
   */
  static async publishAtomicSkillsBatch(skillIds: number[]): Promise<number> {
    const response = await apiService.post(`${this.BASE_URL}/batch/publish`, skillIds);
    return response.data;
  }

  /**
   * 获取技能统计信息
   */
  static async getSkillStatistics(): Promise<{
    totalSkills: number;
    averageRating: number;
    averageCompletionRate: number;
  }> {
    const response = await apiService.get(`${this.BASE_URL}/statistics`);
    return response.data;
  }

  /**
   * 获取热门原子技能
   */
  static async getPopularSkills(params: {
    page?: number;
    size?: number;
    category?: string;
    timeRange?: string;
  } = {}): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    const response = await apiService.get(`${this.BASE_URL}/popular`, params);
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 获取推荐的原子技能
   */
  static async getRecommendedSkills(userId: number, limit: number = 10): Promise<AtomicSkill[]> {
    const response = await apiService.get(`${this.BASE_URL}/recommended`, { userId, limit });
    const apiSkills: AtomicSkillApiResponse[] = response.data;
    return apiSkills.map(skill => AtomicSkillUtils.fromApiResponse(skill));
  }

  /**
   * 评价原子技能
   */
  static async rateSkill(skillId: number, rating: number, comment?: string): Promise<void> {
    await apiService.post(`${this.BASE_URL}/${skillId}/rate`, {
      rating,
      comment
    });
  }

  /**
   * 获取技能评价
   */
  static async getSkillRatings(skillId: number, params: {
    page?: number;
    size?: number;
  } = {}): Promise<{
    content: any[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    const response = await apiService.get(`${this.BASE_URL}/${skillId}/ratings`, params);
    return response.data;
  }

  /**
   * 收藏原子技能
   */
  static async favoriteSkill(skillId: number): Promise<void> {
    await apiService.post(`${this.BASE_URL}/${skillId}/favorite`);
  }

  /**
   * 取消收藏原子技能
   */
  static async unfavoriteSkill(skillId: number): Promise<void> {
    await apiService.delete(`${this.BASE_URL}/${skillId}/favorite`);
  }

  /**
   * 获取用户收藏的技能
   */
  static async getFavoriteSkills(userId: number, params: {
    page?: number;
    size?: number;
  } = {}): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    const response = await apiService.get(`${this.BASE_URL}/favorites`, { userId, ...params });
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }

  /**
   * 标记技能为已掌握
   */
  static async markSkillAsMastered(skillId: number): Promise<void> {
    await apiService.post(`${this.BASE_URL}/${skillId}/master`);
  }

  /**
   * 取消技能掌握状态
   */
  static async unmarkSkillAsMastered(skillId: number): Promise<void> {
    await apiService.delete(`${this.BASE_URL}/${skillId}/master`);
  }

  /**
   * 获取用户已掌握的技能
   */
  static async getMasteredSkills(userId: number, params: {
    page?: number;
    size?: number;
  } = {}): Promise<{
    content: AtomicSkill[];
    totalElements: number;
    totalPages: number;
    size: number;
  }> {
    const response = await apiService.get(`${this.BASE_URL}/mastered`, { userId, ...params });
    const apiResponse = response.data;

    // 转换API响应中的技能数据
    const content = apiResponse.content.map((skill: AtomicSkillApiResponse) =>
      AtomicSkillUtils.fromApiResponse(skill)
    );

    return {
      ...apiResponse,
      content
    };
  }
}
