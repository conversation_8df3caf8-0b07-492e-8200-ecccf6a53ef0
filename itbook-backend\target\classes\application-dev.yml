# Dev环境配置 - 开发测试环境
# 用于前后端联调和集成测试

spring:
  # 数据库配置 - 连接MySQL开发数据库
  datasource:
    url: "***************************************************************************************************************************************************************************************************************************************************************"
    username: "root"
    password: "NW1M5@18N1YYzNlNz"
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 15
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      connection-test-query: SELECT 1
      auto-commit: false
  
  # JPA配置 - Dev环境更新表结构
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        '[format_sql]': true
        '[use_sql_comments]': true
        '[generate_statistics]': true
        '[enable_lazy_load_no_trans]': false  # 禁用懒加载，避免序列化问题
        jdbc:
          '[batch_size]': 20
        '[order_inserts]': true
        '[order_updates]': true
        '[batch_versioned_data]': true

# Dev环境日志配置
logging:
  level:
    '[com.itbook]': DEBUG
    '[org.springframework.security]': DEBUG
    '[org.springframework.web]': INFO
    '[org.hibernate.SQL]': DEBUG
    '[org.hibernate.type.descriptor.sql.BasicBinder]': TRACE
    '[org.springframework.transaction]': DEBUG
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/itbook-dev.log
    max-size: 100MB
    max-history: 30

# Dev环境应用配置
app:
  environment: dev

  # 开发环境服务器配置
  server:
    # 开发环境基础URL - 支持本地开发和局域网访问
    base-url: "http://localhost:8888"

  features:
    enable-analytics: false
    enable-monitoring: true
    enable-cache: true
    enable-rate-limiting: true
  
  # 开发环境安全配置
  security:
    cors:
      allowed-origins: "http://localhost:3000,http://localhost:8081,http://localhost:19006,http://*************:8081"
      allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
      allowed-headers: "*"
      allow-credentials: true
    
    # 开发环境密码策略（相对宽松）
    password:
      min-length: 6
      require-uppercase: false
      require-lowercase: false
      require-numbers: false
      require-special-chars: false
  
  # 测试配置
  test:
    enable-test-data: true
    test-users-count: 5
    enable-integration-tests: true
    mock-external-services: false

# JWT配置
jwt:
  secret: "dev-jwt-secret-key-for-testing-environment-2025"
  expiration: 86400000  # 24小时
  refresh-expiration: 604800000  # 7天

# API文档配置 - Dev环境启用
springdoc:
  api-docs:
    enabled: true
    path: /api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    try-it-out-enabled: true
    tags-sorter: alpha
    operations-sorter: alpha

# 开发工具配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,configprops,beans,mappings
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
  health:
    db:
      enabled: true
    diskspace:
      enabled: true
    redis:
      enabled: false  # 禁用Redis健康检查，使用本地缓存

# 服务器配置
server:
  port: 8888
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 50
      min-spare: 10
    connection-timeout: 20000
    max-connections: 1000
