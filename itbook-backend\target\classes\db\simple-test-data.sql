-- Simple test data for market analysis
SET NAMES utf8mb4;

-- Insert test companies first
INSERT IGNORE INTO company (id, name, description, industry, size, location, website, created_at, updated_at) VALUES 
(1, 'TechCorp', 'Leading technology company', 'Technology', 'LARGE', 'Beijing', 'https://techcorp.com', NOW(), NOW()),
(2, 'DataInc', 'Data analytics company', 'Analytics', 'MEDIUM', 'Shanghai', 'https://datainc.com', NOW(), NOW()),
(3, 'StartupXYZ', 'Innovative startup', 'Internet', 'SMALL', 'Shenzhen', 'https://startupxyz.com', NOW(), NOW());

-- Insert test jobs
INSERT INTO job (
    id, title, company_id, description, requirements, benefits, 
    location, job_type, experience_level, salary_min, salary_max, 
    currency, status, is_remote, is_urgent, view_count, application_count,
    created_at, updated_at, published_at
) VALUES 
(1, 'Senior Frontend Developer', 1, 
 'Frontend development with React and TypeScript',
 '["3+ years experience", "React/Vue", "TypeScript", "Webpack"]',
 '["Insurance", "Flexible hours", "Bonus"]',
 'Beijing', 'full-time', 'senior', 25000.00, 40000.00, 'CNY', 'ACTIVE', 0, 0, 1300, 89,
 NOW(), NOW(), NOW()),

(2, 'Java Backend Developer', 2, 
 'Backend development with Java and Spring',
 '["3+ years Java", "Spring Boot", "MySQL", "Redis"]',
 '["Insurance", "Annual leave", "Bonus"]',
 'Shanghai', 'full-time', 'mid', 20000.00, 35000.00, 'CNY', 'ACTIVE', 0, 0, 980, 156,
 NOW(), NOW(), NOW()),

(3, 'Product Manager', 3, 
 'Product planning and design',
 '["2+ years PM experience", "Product design", "Data analysis"]',
 '["Insurance", "Flexible work", "Stock options"]',
 'Shenzhen', 'full-time', 'mid', 18000.00, 30000.00, 'CNY', 'ACTIVE', 0, 0, 750, 203,
 NOW(), NOW(), NOW());
