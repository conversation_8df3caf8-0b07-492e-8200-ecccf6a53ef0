package com.itbook.algorithm;

import com.itbook.entity.AtomicSkill;
import com.itbook.entity.SkillRelationship;
import com.itbook.repository.AtomicSkillRepository;
import com.itbook.repository.SkillRelationshipRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 技能图谱算法核心类
 * 提供技能依赖分析、路径规划、循环检测等图算法功能
 * 
 * <AUTHOR> Team
 * @since 2025-07-21
 */
@Component
public class SkillGraphAlgorithm {

    @Autowired
    private AtomicSkillRepository atomicSkillRepository;

    @Autowired
    private SkillRelationshipRepository skillRelationshipRepository;

    /**
     * 构建技能依赖图
     */
    public Map<Long, List<Long>> buildDependencyGraph(List<Long> skillIds) {
        Map<Long, List<Long>> graph = new HashMap<>();
        
        // 初始化图节点
        for (Long skillId : skillIds) {
            graph.put(skillId, new ArrayList<>());
        }
        
        // 获取所有相关的技能关系
        List<SkillRelationship> relationships = skillRelationshipRepository
                .findBySourceSkillIdInOrTargetSkillIdIn(skillIds, skillIds);
        
        // 构建邻接表
        for (SkillRelationship rel : relationships) {
            if (rel.getRelationshipType() == SkillRelationship.RelationshipType.PREREQUISITE) {
                // 前置关系：source -> target
                Long sourceId = rel.getSourceSkillId();
                Long targetId = rel.getTargetSkillId();
                
                if (graph.containsKey(sourceId) && graph.containsKey(targetId)) {
                    graph.get(sourceId).add(targetId);
                }
            }
        }
        
        return graph;
    }

    /**
     * 拓扑排序 - 确定技能学习顺序
     */
    public List<Long> topologicalSort(List<Long> skillIds) {
        Map<Long, List<Long>> graph = buildDependencyGraph(skillIds);
        Map<Long, Integer> inDegree = calculateInDegree(graph);
        
        Queue<Long> queue = new LinkedList<>();
        List<Long> result = new ArrayList<>();
        
        // 找到入度为0的节点
        for (Long skillId : skillIds) {
            if (inDegree.getOrDefault(skillId, 0) == 0) {
                queue.offer(skillId);
            }
        }
        
        // 拓扑排序
        while (!queue.isEmpty()) {
            Long current = queue.poll();
            result.add(current);
            
            // 更新邻接节点的入度
            for (Long neighbor : graph.getOrDefault(current, Collections.emptyList())) {
                inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                if (inDegree.get(neighbor) == 0) {
                    queue.offer(neighbor);
                }
            }
        }
        
        // 检查是否存在循环依赖
        if (result.size() != skillIds.size()) {
            throw new RuntimeException("检测到技能依赖循环，无法生成有效的学习顺序");
        }
        
        return result;
    }

    /**
     * 计算入度
     */
    private Map<Long, Integer> calculateInDegree(Map<Long, List<Long>> graph) {
        Map<Long, Integer> inDegree = new HashMap<>();
        
        // 初始化所有节点的入度为0
        for (Long skillId : graph.keySet()) {
            inDegree.put(skillId, 0);
        }
        
        // 计算入度
        for (List<Long> neighbors : graph.values()) {
            for (Long neighbor : neighbors) {
                inDegree.put(neighbor, inDegree.getOrDefault(neighbor, 0) + 1);
            }
        }
        
        return inDegree;
    }

    /**
     * 检测循环依赖
     */
    public List<List<Long>> detectCycles(List<Long> skillIds) {
        Map<Long, List<Long>> graph = buildDependencyGraph(skillIds);
        List<List<Long>> cycles = new ArrayList<>();
        
        Set<Long> visited = new HashSet<>();
        Set<Long> recursionStack = new HashSet<>();
        Map<Long, Long> parent = new HashMap<>();
        
        for (Long skillId : skillIds) {
            if (!visited.contains(skillId)) {
                detectCyclesUtil(skillId, graph, visited, recursionStack, parent, cycles);
            }
        }
        
        return cycles;
    }

    /**
     * DFS检测循环依赖的辅助方法
     */
    private boolean detectCyclesUtil(Long skillId, Map<Long, List<Long>> graph,
                                   Set<Long> visited, Set<Long> recursionStack,
                                   Map<Long, Long> parent, List<List<Long>> cycles) {
        visited.add(skillId);
        recursionStack.add(skillId);
        
        for (Long neighbor : graph.getOrDefault(skillId, Collections.emptyList())) {
            parent.put(neighbor, skillId);
            
            if (!visited.contains(neighbor)) {
                if (detectCyclesUtil(neighbor, graph, visited, recursionStack, parent, cycles)) {
                    return true;
                }
            } else if (recursionStack.contains(neighbor)) {
                // 找到循环，构建循环路径
                List<Long> cycle = buildCyclePath(neighbor, skillId, parent);
                cycles.add(cycle);
                return true;
            }
        }
        
        recursionStack.remove(skillId);
        return false;
    }

    /**
     * 构建循环路径
     */
    private List<Long> buildCyclePath(Long start, Long end, Map<Long, Long> parent) {
        List<Long> cycle = new ArrayList<>();
        Long current = end;
        
        while (current != null && !current.equals(start)) {
            cycle.add(current);
            current = parent.get(current);
        }
        
        if (current != null) {
            cycle.add(start);
        }
        
        Collections.reverse(cycle);
        return cycle;
    }

    /**
     * 查找最短学习路径
     */
    public List<Long> findShortestLearningPath(Long startSkillId, Long targetSkillId) {
        Map<Long, List<Long>> graph = buildReverseGraph(Arrays.asList(startSkillId, targetSkillId));
        
        Queue<Long> queue = new LinkedList<>();
        Map<Long, Long> parent = new HashMap<>();
        Set<Long> visited = new HashSet<>();
        
        queue.offer(startSkillId);
        visited.add(startSkillId);
        parent.put(startSkillId, null);
        
        while (!queue.isEmpty()) {
            Long current = queue.poll();
            
            if (current.equals(targetSkillId)) {
                return buildPath(targetSkillId, parent);
            }
            
            for (Long neighbor : graph.getOrDefault(current, Collections.emptyList())) {
                if (!visited.contains(neighbor)) {
                    visited.add(neighbor);
                    parent.put(neighbor, current);
                    queue.offer(neighbor);
                }
            }
        }
        
        return Collections.emptyList(); // 无路径
    }

    /**
     * 构建反向图（用于路径查找）
     */
    private Map<Long, List<Long>> buildReverseGraph(List<Long> skillIds) {
        Map<Long, List<Long>> graph = new HashMap<>();
        
        // 扩展技能ID集合，包含相关技能
        Set<Long> expandedSkillIds = expandSkillIds(skillIds);
        
        for (Long skillId : expandedSkillIds) {
            graph.put(skillId, new ArrayList<>());
        }
        
        List<SkillRelationship> relationships = skillRelationshipRepository
                .findBySourceSkillIdInOrTargetSkillIdIn(
                    new ArrayList<>(expandedSkillIds), 
                    new ArrayList<>(expandedSkillIds)
                );
        
        for (SkillRelationship rel : relationships) {
            if (rel.getRelationshipType() == SkillRelationship.RelationshipType.PREREQUISITE) {
                // 反向边：target -> source
                Long sourceId = rel.getSourceSkillId();
                Long targetId = rel.getTargetSkillId();
                
                if (graph.containsKey(targetId) && graph.containsKey(sourceId)) {
                    graph.get(targetId).add(sourceId);
                }
            }
        }
        
        return graph;
    }

    /**
     * 扩展技能ID集合，包含相关技能
     */
    private Set<Long> expandSkillIds(List<Long> skillIds) {
        Set<Long> expanded = new HashSet<>(skillIds);
        
        // 获取相关技能关系
        List<SkillRelationship> relationships = skillRelationshipRepository
                .findBySourceSkillIdInOrTargetSkillIdIn(skillIds, skillIds);
        
        for (SkillRelationship rel : relationships) {
            expanded.add(rel.getSourceSkillId());
            expanded.add(rel.getTargetSkillId());
        }
        
        return expanded;
    }

    /**
     * 构建路径
     */
    private List<Long> buildPath(Long target, Map<Long, Long> parent) {
        List<Long> path = new ArrayList<>();
        Long current = target;
        
        while (current != null) {
            path.add(current);
            current = parent.get(current);
        }
        
        Collections.reverse(path);
        return path;
    }

    /**
     * 计算技能重要性分数（PageRank算法）
     */
    public Map<Long, Double> calculateSkillImportance(List<Long> skillIds, int iterations) {
        Map<Long, List<Long>> graph = buildDependencyGraph(skillIds);
        Map<Long, Double> importance = new HashMap<>();
        Map<Long, Double> newImportance = new HashMap<>();
        
        double dampingFactor = 0.85;
        double initialValue = 1.0 / skillIds.size();
        
        // 初始化重要性分数
        for (Long skillId : skillIds) {
            importance.put(skillId, initialValue);
        }
        
        // 迭代计算
        for (int i = 0; i < iterations; i++) {
            for (Long skillId : skillIds) {
                double score = (1.0 - dampingFactor) / skillIds.size();
                
                // 计算来自其他节点的贡献
                for (Long otherId : skillIds) {
                    if (!otherId.equals(skillId)) {
                        List<Long> neighbors = graph.getOrDefault(otherId, Collections.emptyList());
                        if (neighbors.contains(skillId) && !neighbors.isEmpty()) {
                            score += dampingFactor * importance.get(otherId) / neighbors.size();
                        }
                    }
                }
                
                newImportance.put(skillId, score);
            }
            
            // 更新重要性分数
            importance.putAll(newImportance);
        }
        
        return importance;
    }

    /**
     * 获取技能的所有前置技能（递归）
     */
    public Set<Long> getAllPrerequisites(Long skillId) {
        Set<Long> prerequisites = new HashSet<>();
        Set<Long> visited = new HashSet<>();
        
        getAllPrerequisitesRecursive(skillId, prerequisites, visited);
        
        return prerequisites;
    }

    /**
     * 递归获取前置技能
     */
    private void getAllPrerequisitesRecursive(Long skillId, Set<Long> prerequisites, Set<Long> visited) {
        if (visited.contains(skillId)) {
            return;
        }
        
        visited.add(skillId);
        
        List<SkillRelationship> directPrerequisites = skillRelationshipRepository
                .findByTargetSkillIdAndRelationshipType(skillId, SkillRelationship.RelationshipType.PREREQUISITE);
        
        for (SkillRelationship rel : directPrerequisites) {
            Long prereqId = rel.getSourceSkillId();
            prerequisites.add(prereqId);
            getAllPrerequisitesRecursive(prereqId, prerequisites, visited);
        }
    }

    /**
     * 验证学习路径的有效性
     */
    public boolean validateLearningPath(List<Long> skillPath) {
        for (int i = 0; i < skillPath.size(); i++) {
            Long currentSkill = skillPath.get(i);
            Set<Long> prerequisites = getAllPrerequisites(currentSkill);
            
            // 检查前置技能是否都在当前技能之前
            for (Long prereq : prerequisites) {
                int prereqIndex = skillPath.indexOf(prereq);
                if (prereqIndex == -1 || prereqIndex >= i) {
                    return false; // 前置技能不存在或顺序错误
                }
            }
        }
        
        return true;
    }

    /**
     * 优化学习路径顺序
     */
    public List<Long> optimizeLearningPath(List<Long> skillPath) {
        // 如果路径已经有效，尝试进一步优化
        if (validateLearningPath(skillPath)) {
            return optimizeValidPath(skillPath);
        }
        
        // 如果路径无效，重新排序
        return topologicalSort(skillPath);
    }

    /**
     * 优化有效路径
     */
    private List<Long> optimizeValidPath(List<Long> skillPath) {
        // 基于技能重要性和难度进行微调
        Map<Long, Double> importance = calculateSkillImportance(skillPath, 10);
        
        // 在保持依赖关系的前提下，优先安排重要技能
        List<Long> optimized = new ArrayList<>(skillPath);
        
        // 简单的冒泡优化（在不违反依赖的情况下）
        for (int i = 0; i < optimized.size() - 1; i++) {
            for (int j = i + 1; j < optimized.size(); j++) {
                if (canSwap(optimized, i, j) && 
                    importance.get(optimized.get(j)) > importance.get(optimized.get(i))) {
                    Collections.swap(optimized, i, j);
                }
            }
        }
        
        return optimized;
    }

    /**
     * 检查两个位置的技能是否可以交换
     */
    private boolean canSwap(List<Long> path, int i, int j) {
        Long skillI = path.get(i);
        Long skillJ = path.get(j);
        
        // 检查skillJ是否依赖skillI
        Set<Long> prereqsJ = getAllPrerequisites(skillJ);
        if (prereqsJ.contains(skillI)) {
            return false;
        }
        
        // 检查skillI是否依赖skillJ
        Set<Long> prereqsI = getAllPrerequisites(skillI);
        if (prereqsI.contains(skillJ)) {
            return false;
        }
        
        return true;
    }
}
