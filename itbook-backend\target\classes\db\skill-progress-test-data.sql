-- 技能进度测试数据
-- 为用户ID=1创建学习路径进度和步骤进度数据，用于测试技能进度API

SET NAMES utf8mb4;

-- 1. 确保有用户数据（如果不存在则插入）
INSERT IGNORE INTO `user` (id, username, email, password, nickname, status, created_at, updated_at) VALUES 
(1, 'admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '管理员', 'ACTIVE', NOW(), NOW());

-- 2. 确保有学习路径数据（如果不存在则插入）
INSERT IGNORE INTO `learning_path` (
    id, name, description, difficulty_level, estimated_hours, path_type, status, 
    skill_tags, tags, learner_count, completion_count, is_recommended, is_template,
    creator_id, created_at, updated_at
) VALUES 
(1, '前端开发工程师标准学习路径', '前端开发工程师的完整学习路径，涵盖核心技能和实战项目', 'BEGINNER', 120, 'STANDARD', 'PUBLISHED',
 '["JavaScript", "React", "CSS", "HTML", "TypeScript"]', '["前端", "Web开发", "React"]', 150, 45, 1, 0,
 1, NOW(), NOW()),

(2, 'Java后端开发学习路径', 'Java后端开发的系统性学习路径，包含Spring框架和数据库技术', 'INTERMEDIATE', 180, 'STANDARD', 'PUBLISHED',
 '["Java", "Spring Boot", "MySQL", "Redis", "微服务"]', '["后端", "Java", "Spring"]', 200, 60, 1, 0,
 1, NOW(), NOW()),

(3, 'React进阶开发路径', 'React框架的深入学习，包含状态管理、性能优化等高级主题', 'ADVANCED', 80, 'STANDARD', 'PUBLISHED',
 '["React", "Redux", "TypeScript", "性能优化", "测试"]', '["React", "前端进阶"]', 80, 25, 1, 0,
 1, NOW(), NOW());

-- 3. 插入学习路径步骤数据
INSERT IGNORE INTO `learning_path_step` (
    id, learning_path_id, name, description, step_order, step_type, content_type,
    estimated_minutes, is_required, weight, status, difficulty_level, created_at
) VALUES 
-- 前端开发路径的步骤
(1, 1, 'HTML基础', 'HTML标签、语义化、表单等基础知识', 1, 'COURSE', 'COURSE', 300, 1, 10.00, 'ACTIVE', 'BEGINNER', NOW()),
(2, 1, 'CSS样式设计', 'CSS选择器、布局、响应式设计', 2, 'COURSE', 'COURSE', 480, 1, 15.00, 'ACTIVE', 'BEGINNER', NOW()),
(3, 1, 'JavaScript编程', 'JavaScript语法、DOM操作、异步编程', 3, 'COURSE', 'COURSE', 600, 1, 20.00, 'ACTIVE', 'INTERMEDIATE', NOW()),
(4, 1, 'React框架入门', 'React组件、状态管理、生命周期', 4, 'COURSE', 'COURSE', 720, 1, 25.00, 'ACTIVE', 'INTERMEDIATE', NOW()),
(5, 1, '前端项目实战', '完整的前端项目开发实践', 5, 'PROJECT', 'EXERCISE', 900, 1, 30.00, 'ACTIVE', 'ADVANCED', NOW()),

-- Java后端路径的步骤
(6, 2, 'Java基础语法', 'Java语言基础、面向对象编程', 1, 'COURSE', 'COURSE', 480, 1, 15.00, 'ACTIVE', 'BEGINNER', NOW()),
(7, 2, 'Spring Boot框架', 'Spring Boot应用开发、依赖注入', 2, 'COURSE', 'COURSE', 600, 1, 20.00, 'ACTIVE', 'INTERMEDIATE', NOW()),
(8, 2, '数据库设计与操作', 'MySQL数据库设计、SQL查询优化', 3, 'COURSE', 'COURSE', 540, 1, 18.00, 'ACTIVE', 'INTERMEDIATE', NOW()),
(9, 2, '微服务架构', 'Spring Cloud、服务治理、分布式系统', 4, 'COURSE', 'COURSE', 720, 1, 25.00, 'ACTIVE', 'ADVANCED', NOW()),
(10, 2, '后端项目实战', '完整的后端系统开发', 5, 'PROJECT', 'EXERCISE', 1080, 1, 22.00, 'ACTIVE', 'ADVANCED', NOW()),

-- React进阶路径的步骤
(11, 3, 'React Hooks深入', 'useState、useEffect、自定义Hooks', 1, 'COURSE', 'COURSE', 360, 1, 20.00, 'ACTIVE', 'INTERMEDIATE', NOW()),
(12, 3, 'Redux状态管理', 'Redux、Redux Toolkit、中间件', 2, 'COURSE', 'COURSE', 420, 1, 25.00, 'ACTIVE', 'ADVANCED', NOW()),
(13, 3, 'TypeScript集成', 'TypeScript在React中的应用', 3, 'COURSE', 'COURSE', 300, 1, 20.00, 'ACTIVE', 'INTERMEDIATE', NOW()),
(14, 3, '性能优化技巧', 'React性能优化、代码分割', 4, 'COURSE', 'COURSE', 240, 1, 15.00, 'ACTIVE', 'ADVANCED', NOW()),
(15, 3, '单元测试', 'Jest、React Testing Library', 5, 'COURSE', 'COURSE', 300, 1, 20.00, 'ACTIVE', 'INTERMEDIATE', NOW());

DELETE FROM `user_learning_path_progress` WHERE user_id = 1;
INSERT INTO `user_learning_path_progress` (id, user_id, learning_path_id, status, completion_percentage, completed_steps, total_steps, studied_minutes, started_at, last_studied_at, target_completion_date, created_at, updated_at) VALUES (1, 1, 1, 'IN_PROGRESS', 60.00, 3, 5, 1380, '2025-01-01 10:00:00', '2025-01-10 15:30:00', '2025-03-01 00:00:00', NOW(), NOW());
INSERT INTO `user_learning_path_progress` (id, user_id, learning_path_id, status, completion_percentage, completed_steps, total_steps, studied_minutes, started_at, last_studied_at, target_completion_date, created_at, updated_at) VALUES (2, 1, 2, 'IN_PROGRESS', 40.00, 2, 5, 1080, '2025-01-05 09:00:00', '2025-01-12 14:20:00', '2025-04-01 00:00:00', NOW(), NOW());
INSERT INTO `user_learning_path_progress` (id, user_id, learning_path_id, status, completion_percentage, completed_steps, total_steps, studied_minutes, started_at, last_studied_at, target_completion_date, created_at, updated_at) VALUES (3, 1, 3, 'NOT_STARTED', 0.00, 0, 5, 0, NULL, NULL, '2025-05-01 00:00:00', NOW(), NOW());

DELETE FROM `user_step_progress` WHERE user_id = 1;
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (1, 1, 1, 'COMPLETED', 100.00, 300, '2025-01-01 10:00:00', '2025-01-02 16:00:00', '2025-01-02 16:00:00', 3, 5, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (2, 1, 2, 'COMPLETED', 100.00, 480, '2025-01-02 17:00:00', '2025-01-05 14:00:00', '2025-01-05 14:00:00', 4, 4, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (3, 1, 3, 'COMPLETED', 100.00, 600, '2025-01-05 15:00:00', '2025-01-08 18:00:00', '2025-01-08 18:00:00', 4, 5, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (4, 1, 4, 'IN_PROGRESS', 75.00, 540, '2025-01-08 19:00:00', NULL, '2025-01-10 15:30:00', NULL, NULL, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (5, 1, 5, 'NOT_STARTED', 0.00, 0, NULL, NULL, NULL, NULL, NULL, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (6, 1, 6, 'COMPLETED', 100.00, 480, '2025-01-05 09:00:00', '2025-01-08 17:00:00', '2025-01-08 17:00:00', 3, 4, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (7, 1, 7, 'IN_PROGRESS', 60.00, 360, '2025-01-08 18:00:00', NULL, '2025-01-12 14:20:00', NULL, NULL, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (8, 1, 8, 'NOT_STARTED', 0.00, 0, NULL, NULL, NULL, NULL, NULL, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (9, 1, 9, 'NOT_STARTED', 0.00, 0, NULL, NULL, NULL, NULL, NULL, NOW(), NOW());
INSERT INTO `user_step_progress` (id, user_id, step_id, status, completion_percentage, studied_minutes, started_at, completed_at, last_studied_at, difficulty_rating, quality_rating, created_at, updated_at) VALUES (10, 1, 10, 'NOT_STARTED', 0.00, 0, NULL, NULL, NULL, NULL, NULL, NOW(), NOW());

UPDATE `learning_path` SET skill_tags = '[{"name":"Java","category":"编程语言","targetLevel":"INTERMEDIATE","weight":0.9,"isCore":true},{"name":"Spring Boot","category":"开发框架","targetLevel":"INTERMEDIATE","weight":0.8,"isCore":true},{"name":"MySQL","category":"数据库","targetLevel":"BEGINNER","weight":0.6,"isCore":false},{"name":"后端开发","category":"技术领域","targetLevel":"INTERMEDIATE","weight":0.7,"isCore":true}]' WHERE id = 1;
UPDATE `learning_path` SET skill_tags = '[{"name":"React","category":"前端框架","targetLevel":"INTERMEDIATE","weight":0.9,"isCore":true},{"name":"JavaScript","category":"编程语言","targetLevel":"INTERMEDIATE","weight":0.8,"isCore":true},{"name":"HTML","category":"标记语言","targetLevel":"BEGINNER","weight":0.5,"isCore":false},{"name":"CSS","category":"样式语言","targetLevel":"BEGINNER","weight":0.5,"isCore":false},{"name":"前端开发","category":"技术领域","targetLevel":"INTERMEDIATE","weight":0.7,"isCore":true}]' WHERE id = 2;
UPDATE `learning_path` SET skill_tags = '[{"name":"全栈开发","category":"技术领域","targetLevel":"ADVANCED","weight":1.0,"isCore":true},{"name":"React","category":"前端框架","targetLevel":"INTERMEDIATE","weight":0.8,"isCore":true},{"name":"Java","category":"编程语言","targetLevel":"INTERMEDIATE","weight":0.8,"isCore":true},{"name":"Spring Boot","category":"开发框架","targetLevel":"INTERMEDIATE","weight":0.7,"isCore":true},{"name":"微服务","category":"架构模式","targetLevel":"ADVANCED","weight":0.9,"isCore":true}]' WHERE id = 3;
INSERT INTO `user_career_goal` (id, user_id, target_job_id, target_level, description, motivation, set_at, target_completion_date, is_active, priority, created_at, updated_at) VALUES (1, 1, 1, 'MID', '成为一名优秀的前端开发工程师', '对前端技术充满热情，希望能够开发出优秀的用户界面', '2025-01-01 10:00:00', '2025-06-01 00:00:00', 1, 1, NOW(), NOW()) ON DUPLICATE KEY UPDATE updated_at = NOW();
UPDATE `user_learning_path_progress` SET career_goal_id = 1 WHERE user_id = 1 AND learning_path_id IN (1, 3);
UPDATE `user_learning_path_progress` SET career_goal_id = 1 WHERE user_id = 1 AND learning_path_id = 2;
