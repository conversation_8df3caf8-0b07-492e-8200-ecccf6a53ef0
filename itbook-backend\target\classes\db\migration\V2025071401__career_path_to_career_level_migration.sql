-- ===================================================================
-- ITBook数据库重构 - career_path表数据迁移到career_level表
-- 版本: V2025071401
-- 创建时间: 2025-07-14
-- 作者: ITBook Team
-- 
-- 将career_path表的数据迁移到career_level表，然后删除career_path表
-- ===================================================================

-- 第一步：扩展career_level表结构，添加career_path表中的字段
ALTER TABLE `career_level` 
ADD COLUMN IF NOT EXISTS `duration` varchar(50) COMMENT '预期时长' AFTER `description`,
ADD COLUMN IF NOT EXISTS `skills` json COMMENT '所需技能列表' AFTER `duration`,
ADD COLUMN IF NOT EXISTS `projects` json COMMENT '推荐项目列表' AFTER `skills`;

-- 第二步：将career_path表中的数据迁移到career_level表
-- 更新已存在的career_level记录
UPDATE career_level cl 
JOIN career_path cp ON cl.career_goal_id = cp.career_goal_id AND cl.level_code = cp.level
SET 
    cl.duration = cp.duration,
    cl.skills = cp.skills,
    cl.projects = cp.projects,
    cl.salary_range_min = CASE 
        WHEN cp.salary_min IS NOT NULL THEN cp.salary_min * 1000 
        ELSE cl.salary_range_min 
    END,
    cl.salary_range_max = CASE 
        WHEN cp.salary_max IS NOT NULL THEN cp.salary_max * 1000 
        ELSE cl.salary_range_max 
    END,
    cl.updated_at = CURRENT_TIMESTAMP;

-- 第三步：插入career_path中存在但career_level中不存在的记录
INSERT INTO career_level (
    career_goal_id, level_code, level_name, description, duration, skills, projects,
    min_experience_years, max_experience_years, salary_range_min, salary_range_max, sort_order
)
SELECT 
    cp.career_goal_id,
    cp.level as level_code,
    CASE 
        WHEN cp.level = 'junior' THEN '初级工程师'
        WHEN cp.level = 'mid' THEN '中级工程师'
        WHEN cp.level = 'senior' THEN '高级工程师'
        ELSE CONCAT(UPPER(SUBSTRING(cp.level, 1, 1)), SUBSTRING(cp.level, 2), '工程师')
    END as level_name,
    CONCAT('职业级别：', cp.level, '，来源于career_path表迁移') as description,
    cp.duration,
    cp.skills,
    cp.projects,
    CASE 
        WHEN cp.level = 'junior' THEN 0
        WHEN cp.level = 'mid' THEN 2
        WHEN cp.level = 'senior' THEN 5
        ELSE 0
    END as min_experience_years,
    CASE 
        WHEN cp.level = 'junior' THEN 2
        WHEN cp.level = 'mid' THEN 5
        WHEN cp.level = 'senior' THEN 10
        ELSE NULL
    END as max_experience_years,
    CASE 
        WHEN cp.salary_min IS NOT NULL THEN cp.salary_min * 1000 
        ELSE NULL 
    END as salary_range_min,
    CASE 
        WHEN cp.salary_max IS NOT NULL THEN cp.salary_max * 1000 
        ELSE NULL 
    END as salary_range_max,
    CASE 
        WHEN cp.level = 'junior' THEN 1
        WHEN cp.level = 'mid' THEN 2
        WHEN cp.level = 'senior' THEN 3
        ELSE 99
    END as sort_order
FROM career_path cp
LEFT JOIN career_level cl ON cp.career_goal_id = cl.career_goal_id AND cp.level = cl.level_code
WHERE cl.id IS NULL;

-- 第四步：验证数据迁移完整性
-- 检查迁移后的数据
SELECT 
    'career_level_after_migration' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT career_goal_id) as unique_career_goals,
    COUNT(DISTINCT level_code) as unique_levels,
    SUM(CASE WHEN skills IS NOT NULL THEN 1 ELSE 0 END) as records_with_skills,
    SUM(CASE WHEN projects IS NOT NULL THEN 1 ELSE 0 END) as records_with_projects,
    SUM(CASE WHEN duration IS NOT NULL THEN 1 ELSE 0 END) as records_with_duration
FROM career_level;

-- 第五步：删除career_path表
DROP TABLE IF EXISTS `career_path`;

-- 第六步：为career_level表的新字段创建索引
ALTER TABLE `career_level` 
ADD INDEX IF NOT EXISTS `idx_career_level_duration` (`duration`),
ADD INDEX IF NOT EXISTS `idx_career_level_salary_range` (`salary_range_min`, `salary_range_max`);

-- 第七步：更新表注释
ALTER TABLE `career_level` COMMENT = '职业级别配置表 - 整合了职业发展路径信息';

-- 验证最终结果
SELECT 'Career path migration completed successfully!' as status;
